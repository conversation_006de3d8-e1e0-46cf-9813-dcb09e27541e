export const usersPageStrings = {
   users: 'Users',
   usersDesc: 'Add, edit and delete users and assign roles to them',
   addUser: 'Add User',
   activateUser: 'User activated successfully',
   deactivateUser: 'User deactivated successfully',
   actionFailed: 'Failed to perform action',
};

export const usersTableStrings = {
   name: 'Name',
   email: 'Email',
   role: 'Access Role',
   business: 'Assigned Business',
   editProfile: 'Edit Profile',
   activateUser: 'Activate User',
   deactivateUser: 'Deactivate User',
   removeFromOrganization: 'Remove from Organization',
};

export const addEditUserPageStrings = {
   addUser: 'Add User',
   userProfilePhoto: 'User Profile Photo',
   chooseImage: 'Choose Image',
   fullName: 'Full Name',
   emailAddress: 'Email Address',
   country: 'Country',
   language: 'Language',
   business: 'Business',
   currentBusiness: 'Current Business',
   accessRole: 'Access Role',
   chooseRole: 'Choose Role',
   admin: 'Admin',
   contributor: 'Contributor',
   saveUser: 'Save',
   createUser: 'Create User',
   discard: 'Discard',
   emailRegex: /\S+@\S+\.\S+/,
   incorrectURL: 'Please enter a valid email address',
   createUserSuccess: 'Role has been assigned successfully',
   roleConfirmation: 'Role confirmation mail sent to the user via email',
   createUserFailed: 'Failed to create user',
   updateUserSuccess: 'User details updated successfully',
   updateUserFailed: 'Failed to update user',
   languageOptions: {
      english: 'English',
      german: 'German (Coming soon...)',
   },
   passwordsDontMatch: 'Passwords do not match',
   requiredFields: 'Fill all the required fields',
};
