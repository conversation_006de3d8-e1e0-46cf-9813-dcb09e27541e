import React, { useRef, useEffect, useMemo, useState } from 'react';
import TypewriterText from './type-write-text';
import CreativePreview from './creative-preview';
import { Spinner } from '@chakra-ui/react';
import {
   Accordion,
   AccordionItem,
   AccordionTrigger,
   AccordionContent,
} from '@/components/ui/accordion';
import { convertToHTMLTable } from '../analytics-agent/helpers';
import { ListCircle, ListCircleCompleted } from '../analytics-agent/constants';
import { FaTag } from 'react-icons/fa6';
import { PiRepeatFill } from 'react-icons/pi';
type Status = 'completed' | 'in-progress' | 'pending' | 'error';

type TableSection = {
   sectionTitle: string;
   rows: Record<string, string | number>[];
};

type Message = {
   id: string;
   title: string;
   content: string;
   isStreaming: boolean;
   type: string;
   imageUrl?: string;
   tableData?: TableSection[];
   data?: unknown;
   timestamp: string;
   isComplete: boolean;
};

type Props = {
   streamingMessages: Message[];
   stepStatuses: { status: Status }[];
   activeStreamingIndex: number;
   setActiveStreamingIndex: React.Dispatch<React.SetStateAction<number>>;
   scrollRef?: React.RefObject<HTMLDivElement>;
   disableAnimation?: boolean;
};

const getAgentForStep = (idx: number, streamingMessages: Message[]): string => {
   const message = streamingMessages[idx];
   if (
      message &&
      message.title &&
      message.title.toLowerCase().includes('audience analysis')
   ) {
      return 'Analytics Agent';
   }
   return 'Performance Agent';
};

const getHandoverText = (
   idx: number,
   streamingMessages: Message[],
): string | null => {
   const message = streamingMessages[idx];

   const nextMessage = streamingMessages[idx + 1];
   if (
      nextMessage &&
      nextMessage.title &&
      nextMessage.title.toLowerCase().includes('audience analysis')
   ) {
      return 'Handing it over to Analytics Agent';
   }

   if (
      message &&
      message.title &&
      message.title.toLowerCase().includes('audience analysis')
   ) {
      return 'Handing it over to Performance Agent';
   }
   return null;
};

const StreamingMessageTimeline: React.FC<Props> = ({
   streamingMessages,
   stepStatuses,
   activeStreamingIndex,
   setActiveStreamingIndex,
   scrollRef,
   disableAnimation = false,
}) => {
   const messageRefs = useRef<{ [key: string]: HTMLLIElement | null }>({});
   const [accordionOpen, setAccordionOpen] = useState('item-1');

   const totalTimeSeconds = useMemo(() => {
      const completedMessages = streamingMessages.filter(
         (_, idx) => stepStatuses[idx]?.status === 'completed',
      );
      if (completedMessages.length < 2) return 0;
      const start = new Date(completedMessages[0].timestamp).getTime();
      const end = new Date(
         completedMessages[completedMessages.length - 1].timestamp,
      ).getTime();
      return Math.round((end - start) / 1000);
   }, [streamingMessages, stepStatuses]);

   useEffect(() => {
      const allDone =
         streamingMessages.length > 0 &&
         streamingMessages.every(
            (_, idx) => stepStatuses[idx]?.status === 'completed',
         );
      if (allDone) setAccordionOpen('');
   }, [streamingMessages, stepStatuses]);

   const scrollToBottom = () => {
      if (scrollRef?.current) {
         scrollRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'end',
            inline: 'nearest',
         });
      }
   };
   const scrollToMessage = (messageId: string) => {
      const messageElement = messageRefs.current[messageId];
      if (messageElement) {
         messageElement.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'nearest',
         });
      }
   };
   const scrollToBottomEnhanced = () => {
      if (scrollRef?.current) {
         const container = scrollRef.current.closest('.overflow-y-auto');
         if (container) {
            container.scrollTo({
               top: container.scrollHeight,
               behavior: 'smooth',
            });
         } else {
            scrollRef.current.scrollIntoView({
               behavior: 'smooth',
               block: 'end',
               inline: 'nearest',
            });
         }
      }
   };
   useEffect(() => {
      if (streamingMessages.length > 0) {
         const lastMessage = streamingMessages[streamingMessages.length - 1];
         if (lastMessage && messageRefs.current[lastMessage.id]) {
            setTimeout(() => {
               scrollToMessage(lastMessage.id);
            }, 100);
         }
      }
   }, [streamingMessages.length]);
   useEffect(() => {
      if (activeStreamingIndex < streamingMessages.length) {
         const currentMessage = streamingMessages[activeStreamingIndex];
         if (currentMessage && messageRefs.current[currentMessage.id]) {
            setTimeout(() => {
               scrollToMessage(currentMessage.id);
            }, 50);
            setTimeout(() => {
               scrollToBottom();
            }, 100);
         }
      }
   }, [activeStreamingIndex, streamingMessages]);

   const allCompleted =
      streamingMessages.length > 0 &&
      streamingMessages.every(
         (_, idx) => stepStatuses[idx]?.status === 'completed',
      );

   const verticalLineClass = allCompleted
      ? 'border-s border-gray-200'
      : 'border-s !border-[#4285F4]';

   const textColorClass = allCompleted ? 'text-gray-500' : 'text-gray-800';

   return (
      <div className='flex flex-col w-full p-[12px_14px] bg-white'>
         <Accordion
            type='single'
            collapsible
            value={accordionOpen}
            onValueChange={setAccordionOpen}
         >
            <AccordionItem value='item-1'>
               <AccordionTrigger className='text-gray-500 text-[16px] justify-start gap-1 hover:cursor-pointer [&[data-state=open]>svg]:rotate-90 hover:no-underline'>
                  <div className='text-[12px] md:text-[16px]'>
                     Thought for {totalTimeSeconds}s
                  </div>
               </AccordionTrigger>
               <AccordionContent className='pl-[10px]'>
                  <ol
                     className={`relative w-[95%] ${verticalLineClass} ${textColorClass} pl-5`}
                  >
                     {streamingMessages.map((message, idx) => {
                        if (idx > activeStreamingIndex) return null;
                        let status: Status = 'pending';
                        if (stepStatuses[idx]?.status === 'completed')
                           status = 'completed';
                        else if (stepStatuses[idx]?.status === 'in-progress')
                           status = 'in-progress';
                        else if (stepStatuses[idx]?.status === 'error')
                           status = 'error';

                        const agent = getAgentForStep(idx, streamingMessages);
                        const handoverText = getHandoverText(
                           idx,
                           streamingMessages,
                        );

                        let dot;
                        if (allCompleted) {
                           dot = <ListCircle />;
                        } else {
                           dot =
                              status === 'completed' ||
                              status === 'in-progress' ? (
                                 <ListCircleCompleted />
                              ) : (
                                 <ListCircle />
                              );
                        }

                        return (
                           <li
                              className='relative mb-5'
                              key={message.id}
                              ref={(el) => {
                                 messageRefs.current[message.id] = el;
                              }}
                              style={{ listStyle: 'none' }}
                           >
                              <span
                                 className='absolute flex items-center justify-center w-8 h-8'
                                 style={{ left: '-36px' }}
                              >
                                 {dot}
                              </span>
                              <div className='ml-5'>
                                 <h3 className='text-[14px] md:text-[16px] font-bold ml-[-5px] mt-[-3px]'>
                                    {agent}:
                                 </h3>

                                 <div className='flex flex-col w-full gap-1 mt-2'>
                                    <div className='flex items-center gap-2 mb-1'>
                                       <span className='font-semibold flex items-center gap-2'>
                                          <FaTag className='text-gray-500' />
                                          {message.title}
                                       </span>
                                       {status === 'in-progress' && (
                                          <span className='ml-2 animate-pulse flex items-center gap-1 text-gray-400'>
                                             <Spinner
                                                className='!border-gray-400'
                                                size='sm'
                                             />
                                             Thinking...
                                          </span>
                                       )}
                                    </div>
                                    <div className='mb-1'>
                                       <TypewriterText
                                          text={message.content}
                                          speed={20}
                                          messageId={message.id}
                                          onComplete={() => {
                                             if (
                                                idx === activeStreamingIndex &&
                                                !message.isStreaming
                                             ) {
                                                setActiveStreamingIndex(
                                                   (prev) => prev + 1,
                                                );
                                             }
                                             setTimeout(() => {
                                                scrollToBottomEnhanced();
                                             }, 100);
                                             setTimeout(() => {
                                                scrollToBottom();
                                             }, 200);
                                          }}
                                          onType={() => {
                                             setTimeout(() => {
                                                scrollToMessage(message.id);
                                             }, 30);
                                          }}
                                          disableAnimation={disableAnimation}
                                       />
                                    </div>

                                    {handoverText && status === 'completed' && (
                                       <div className='mb-1 text-sm text-gray-500 italic flex items-center gap-1'>
                                          <PiRepeatFill />
                                          {handoverText}
                                       </div>
                                    )}
                                    {message.tableData &&
                                       Array.isArray(message.tableData) &&
                                       message.tableData.length > 0 && (
                                          <div className='mt-2 space-y-2'>
                                             {message.tableData.map(
                                                (tableSection, index) => (
                                                   <div
                                                      key={index}
                                                      className='overflow-x-auto my-2'
                                                   >
                                                      <p className='font-semibold text-sm mt-2'>
                                                         {
                                                            tableSection.sectionTitle
                                                         }
                                                         :
                                                      </p>

                                                      <div className='overflow-x-auto'>
                                                         <div
                                                            className='w-full'
                                                            dangerouslySetInnerHTML={{
                                                               __html:
                                                                  convertToHTMLTable(
                                                                     [
                                                                        Object.keys(
                                                                           tableSection
                                                                              .rows[0] ||
                                                                              {},
                                                                        ).join(
                                                                           ' | ',
                                                                        ),
                                                                        '|',
                                                                        ...tableSection.rows.map(
                                                                           (
                                                                              row,
                                                                           ) =>
                                                                              Object.values(
                                                                                 row,
                                                                              ).join(
                                                                                 ' | ',
                                                                              ),
                                                                        ),
                                                                     ].join(
                                                                        '\n',
                                                                     ),
                                                                  ),
                                                            }}
                                                         />
                                                      </div>
                                                   </div>
                                                ),
                                             )}
                                          </div>
                                       )}

                                    {message.type === 'ad-creative' &&
                                    message.imageUrl ? (
                                       <div className='rounded-xl border border-gray-200 overflow-hidden mt-2'>
                                          <div className='p-2 bg-gray-100 border-b border-gray-200'>
                                             <p className='font-semibold text-xs'>
                                                Generated Creative
                                             </p>
                                          </div>
                                          <div className='p-2 flex justify-center'>
                                             <CreativePreview
                                                imageUrl={message.imageUrl}
                                                description={
                                                   typeof message.data ===
                                                      'object' &&
                                                   message.data &&
                                                   'description' in message.data
                                                      ? (
                                                           message.data as {
                                                              description?: string;
                                                           }
                                                        ).description
                                                      : undefined
                                                }
                                             />
                                          </div>
                                       </div>
                                    ) : (
                                       message.imageUrl && (
                                          <div className='rounded-xl border border-gray-200 overflow-hidden mt-2'>
                                             <div className='p-2 bg-gray-100 border-b border-gray-200'>
                                                <p className='font-semibold text-xs'>
                                                   Generated Creative
                                                </p>
                                             </div>
                                             <div className='p-2'>
                                                <img
                                                   src={message.imageUrl}
                                                   alt='Ad Creative'
                                                   className='max-w-xs rounded-lg border border-gray-200 shadow-sm'
                                                />
                                             </div>
                                          </div>
                                       )
                                    )}
                                 </div>
                              </div>
                           </li>
                        );
                     })}
                  </ol>
               </AccordionContent>
            </AccordionItem>
         </Accordion>
      </div>
   );
};

export default StreamingMessageTimeline;
