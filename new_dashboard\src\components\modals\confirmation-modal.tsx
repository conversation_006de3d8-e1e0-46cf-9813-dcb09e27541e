import React from 'react';
import {
   <PERSON><PERSON>,
   DialogContent,
   DialogHeader,
   <PERSON><PERSON>Title,
   DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { AlertTriangle, Info, CheckCircle } from 'lucide-react';
import { useAppSelector } from '../../store/store';
import { useDispatch } from 'react-redux';
import { closeModal } from '../../store/reducer/modal-reducer';

interface ConfirmationModalProps {
   title: string;
   message: string;
   confirmButtonText?: string;
   cancelButtonText?: string;
   confirmButtonColor?: string;
   cancelButtonColor?: string;
   icon?: 'warning' | 'info' | 'success';
   showCancelButton?: boolean;
   onConfirm: () => void;
   onCancel?: () => void;
}

const ConfirmationModal: React.FC = () => {
   const dispatch = useDispatch();
   const { payload } = useAppSelector((state) => state.modal);

   const {
      title,
      message,
      confirmButtonText = 'OK',
      cancelButtonText = 'Cancel',
      confirmButtonColor = 'blue',
      // cancelButtonColor = 'gray',
      icon = 'info',
      showCancelButton = false,
      onConfirm,
      onCancel,
   } = (payload?.modalProps || {}) as ConfirmationModalProps;

   const getIconComponent = () => {
      const iconProps = { className: 'h-12 w-12' };

      switch (icon) {
         case 'warning':
            return (
               <AlertTriangle
                  {...iconProps}
                  className='h-12 w-12 text-orange-500'
               />
            );
         case 'success':
            return (
               <CheckCircle
                  {...iconProps}
                  className='h-12 w-12 text-green-500'
               />
            );
         default:
            return <Info {...iconProps} className='h-12 w-12 text-blue-500' />;
      }
   };

   const handleConfirm = () => {
      onConfirm();
      dispatch(closeModal());
   };

   const handleCancel = () => {
      if (onCancel) onCancel();
      dispatch(closeModal());
   };

   const getConfirmButtonVariant = () => {
      switch (confirmButtonColor) {
         case 'blue':
            return 'default';
         case 'red':
            return 'destructive';
         default:
            return 'default';
      }
   };

   return (
      <Dialog
         open
         onOpenChange={() => (showCancelButton ? handleCancel() : undefined)}
      >
         <DialogContent className='max-w-md bg-white dark:bg-zinc-900 shadow-lg rounded-lg z-50'>
            <DialogHeader>
               <DialogTitle>{title}</DialogTitle>
            </DialogHeader>

            <div className='flex flex-col items-center space-y-4 py-4'>
               <div className='flex justify-center'>{getIconComponent()}</div>

               <p className='text-center text-sm text-gray-700 dark:text-gray-300'>
                  {message}
               </p>
            </div>

            <DialogFooter>
               {showCancelButton && (
                  <Button variant='outline' onClick={handleCancel}>
                     {cancelButtonText}
                  </Button>
               )}
               <Button
                  variant={getConfirmButtonVariant()}
                  onClick={handleConfirm}
               >
                  {confirmButtonText}
               </Button>
            </DialogFooter>
         </DialogContent>
      </Dialog>
   );
};

export default ConfirmationModal;
