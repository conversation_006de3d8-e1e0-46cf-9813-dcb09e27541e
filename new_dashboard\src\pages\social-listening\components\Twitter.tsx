import React from 'react';
import Swal from 'sweetalert2';
import Card from './Card';
import image from '../images/integrations/x.jpg';

import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { AuthUser } from '../../../types/auth';
import { dialogMessage } from '../../../utils/strings/content-manager';
import socialWatchEndpoints from '../../../api/service/social-watch/apis';
import { channelNames } from '../utils/constant';
import { connectToTwitterSentiment } from '../utils';
import { useAppDispatch, useAppSelector } from '../../../store/store';
import {
   setIsConnecting,
   setIsDisconnecting,
   setTwitterConnection,
} from '../../../store/reducer/integration-reducer';

import './Twitter.scss';
import { loadingStateChannel } from '../../dashboard/utils/query-keys';

const Twitter: React.FC = () => {
   const { isFetching, isConnecting, isDisconnecting, connections } =
      useAppSelector((state) => state.integration);

   const dispatch = useAppDispatch();

   const client_id = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   )?.client_id;

   const handlePinTwitter = async (oauthTokenInput: string) => {
      try {
         const { value: pin } = (await Swal.fire({
            title: 'Enter Pin',
            input: 'text',
            inputPlaceholder: 'Enter Pin',
            showCancelButton: true,
         })) as { value: string };

         if (pin) {
            dispatch(setIsConnecting(loadingStateChannel.TWITTER));
            const {
               data: { connectionDetails },
            } = await socialWatchEndpoints.fetchTwitterAccessToken({
               clientId: client_id!,
               pin,
               oauthToken: oauthTokenInput,
            });
            const { oauthToken, oauthTokenSecret, screenName, userId } =
               connectionDetails;
            /**
             * TODO:
             * 1. Save the details in the backend
             * 2. Set the connection details in a central place
             */
            await connectToTwitterSentiment({
               client_id: client_id!,
               actual_account_name: screenName,
               isConnect: true,
               oauthToken,
               oauthTokenSecret,
               userId,
            });

            dispatch(
               setTwitterConnection({
                  actual_account_name: screenName,
                  oauthToken,
                  oauthTokenSecret,
                  userId,
                  channelName: channelNames.TWITTER,
               }),
            );
         }
      } catch (err) {
         console.log('err', err);
      } finally {
         dispatch(setIsConnecting(null));
      }
   };

   const onButtonClick = () => {
      if (connections.twitter) void removeAccount();
      else void login();
   };

   const login = async () => {
      try {
         const {
            data: { tokenData },
         } = await socialWatchEndpoints.requestTwitterToken();
         const { oauthToken, authorizeURL } = tokenData;

         window.open(authorizeURL, '_blank')?.focus();
         await handlePinTwitter(oauthToken);
      } catch (err) {
         console.log('err', err);
      }
   };

   async function removeAccount() {
      const result = await Swal.fire({
         title: dialogMessage.delete.title,
         text: dialogMessage.delete.description,
         icon: 'warning',
         showCancelButton: true,
         confirmButtonColor: '#3085d6',
         cancelButtonColor: '#d33',
         confirmButtonText: dialogMessage.delete.buttonMessage,
      });

      if (result.isConfirmed) {
         try {
            dispatch(setIsDisconnecting(loadingStateChannel.TWITTER));
            await connectToTwitterSentiment({
               client_id: client_id!,
               isConnect: false,
            });
            dispatch(setTwitterConnection(null));
         } catch (err) {
            console.error('Failed to disconnect');
         } finally {
            dispatch(setIsDisconnecting(null));
         }
      }
   }

   const isConnected = connections.twitter
      ? Boolean(connections.twitter.userId)
      : false;

   return (
      <Card
         isFetching={isFetching}
         isConnecting={isConnecting?.includes(loadingStateChannel.TWITTER)}
         isDisconnecting={isDisconnecting?.includes(
            loadingStateChannel.TWITTER,
         )}
         isConnected={isConnected}
         heading={connections.twitter?.actual_account_name || 'X'}
         src={image}
         onButtonClick={onButtonClick}
      />
   );
};

export default Twitter;
