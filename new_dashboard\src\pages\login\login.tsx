// import React, { useState } from 'react';
// import { useNavigate } from 'react-router-dom';
// import { FaEye, FaEyeSlash } from 'react-icons/fa';
// import {
//    Box,
//    Button,
//    FormControl,
//    Input,
//    Stack,
//    Image,
//    useToast,
//    FormErrorMessage,
//    InputGroup,
//    InputRightElement,
// } from '@chakra-ui/react';
// import { useApiMutation } from '../../hooks/react-query-hooks';
// import { Keys, LocalStorageService } from '../../utils/local-storage';
// import { loginStrings } from '../../utils/strings/login-strings';
// import { appStrings } from '../../utils/strings/app-strings';

// import ICON from '../../assets/icons/icon.png';
// import keys from '../../utils/strings/query-keys';
// import backendEndpoints from '../../api/service/auth';

// function Login() {
//    const [showPassword, setShowPassword] = useState(false);
//    const [form, setForm] = useState({
//       email: '',
//       password: '',
//    });
//    const [error, setError] = useState({
//       email: '',
//       password: '',
//    });
//    const navigate = useNavigate();
//    const toast = useToast();

//    const { isPending, mutate } = useApiMutation({
//       queryKey: [keys.login],
//       mutationFn: backendEndpoints.login,
//       onSuccessHandler: (data) => {
//          const { tokenDetails, fullName } = data;

//          if (!tokenDetails || tokenDetails.length <= 0) {
//             toast({
//                title: loginStrings.loginFailed,
//                description: loginStrings.loginFailedDesc,
//                status: 'error',
//                duration: 5000,
//                isClosable: true,
//             });

//             return;
//          }

//          const { email_address, client_id, email_token } = tokenDetails[0];

//          const userDetails = {
//             email: email_address,
//             client_id,
//             fullName,
//          };

//          LocalStorageService.setItem(Keys.FlableUserDetails, userDetails);
//          LocalStorageService.setItem(Keys.Token, email_token);
//          LocalStorageService.setItem(Keys.UserName, email_address);
//          LocalStorageService.setItem(Keys.ClientId, client_id);
//          navigate('/marco');

//          toast({
//             title: loginStrings.loginSuccessful,
//             description: loginStrings.loginSuccessfulDesc,
//             status: 'success',
//             duration: 5000,
//             isClosable: true,
//          });
//       },
//       onError: (msg) => {
//          toast({
//             title: loginStrings.loginFailed,
//             description: msg,
//             status: 'error',
//             duration: 5000,
//             isClosable: true,
//          });
//       },
//    });

//    const validateForm = () => {
//       let isValid = true;
//       let emailError = '';
//       let passwordError = '';
//       if (!form.email) {
//          emailError = 'Email is required';
//          isValid = false;
//       } else if (!/\S+@\S+\.\S+/.test(form.email)) {
//          passwordError = 'Password is required';
//          isValid = false;
//       }
//       if (!form?.password) {
//          passwordError = 'Password is required';
//          isValid = false;
//       } else if (form?.password?.length < 6) {
//          passwordError = 'Password must be at least 6 characters long';
//          isValid = false;
//       }
//       setError({ email: emailError, password: passwordError });
//       return isValid;
//    };
//    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
//       const { name, value } = e.target;
//       setForm((prevForm) => ({
//          ...prevForm,
//          [name]: value,
//       }));
//       setError((prevErrors) => ({
//          ...prevErrors,
//          [name]: '',
//       }));
//    };

//    const handleSubmit = () => {
//       if (!validateForm()) {
//          return;
//       }
//       mutate(form);
//    };

//    return (
//       <Box
//          display='flex'
//          justifyContent='center'
//          alignItems='center'
//          height='100vh'
//          bg='##FFFFFF'
//          pt={40}
//          pb={40}
//       >
//          <Stack
//             direction={['column', 'row']}
//             spacing={['0', '0']}
//             width={['40%', '60%']}
//             height={['100%', '100%']}
//             justifyContent='center'
//             alignItems='center'
//             boxShadow={['none', 'lg']}
//             bg={
//                'linear-gradient(to right, #EBEBEB 60%, rgb(117, 117, 117) 100%)'
//             }
//             borderTopRightRadius={['0', '8']}
//             borderBottomRightRadius={['0', '8']}
//             borderTopLeftRadius={['0', '8']}
//             borderBottomLeftRadius={['0', '8']}
//          >
//             <Box
//                display={['none', 'block']}
//                width='100%'
//                maxW='450px'
//                pt={10}
//                pl={20}
//                pr={20}
//                boxShadow='lg'
//                bg='white'
//                height={['0', '100%']}
//                borderTopLeftRadius={['0', '8']}
//                borderBottomLeftRadius={['0', '8']}
//             >
//                <Box
//                   display='flex'
//                   justifyContent='center'
//                   alignItems='center'
//                   mt={10}
//                   mb={10}
//                >
//                   <Image src={ICON} alt='Flable Icon' w={'15%'} />
//                   <Box ml={2} fontSize='xl' fontWeight='bold'>
//                      {appStrings.companyName}
//                      {/* <Box fontSize='sm' color='gray.500'>
//                         {appStrings.appName}
//                      </Box> */}
//                   </Box>
//                </Box>
//                <FormControl id='email' mt={4} isInvalid={!!error.email}>
//                   <Input
//                      type='email'
//                      name='email'
//                      value={form.email}
//                      onChange={handleChange}
//                      placeholder='Email'
//                      variant='outline'
//                      size={'lg'}
//                   />
//                   {error.email && (
//                      <FormErrorMessage>{error.email}</FormErrorMessage>
//                   )}
//                </FormControl>
//                <FormControl mt={10} id='password' isInvalid={!!error.password}>
//                   <InputGroup size='lg'>
//                      <Input
//                         type={showPassword ? 'text' : 'password'}
//                         name='password'
//                         value={form.password}
//                         onChange={handleChange}
//                         placeholder='Password'
//                         variant='outline'
//                         size={'lg'}
//                      />
//                      <InputRightElement>
//                         <Button
//                            h='1.75rem'
//                            size='sm'
//                            onClick={() => setShowPassword(!showPassword)}
//                            variant={'ghost'}
//                         >
//                            {showPassword ? <FaEyeSlash /> : <FaEye />}
//                         </Button>
//                      </InputRightElement>
//                   </InputGroup>
//                   {error.password && (
//                      <FormErrorMessage>{error.password}</FormErrorMessage>
//                   )}
//                </FormControl>
//                <Button
//                   width='full'
//                   disabled={isPending}
//                   mt={10}
//                   colorScheme='gray'
//                   onClick={handleSubmit}
//                   size={'sm'}
//                   isLoading={isPending}
//                >
//                   {loginStrings.login}
//                </Button>
//                {/* <Box
//                   display='flex'
//                   justifyContent='right'
//                   alignItems='right'
//                   mt={2}
//                >
//                   <Box fontSize='sm'>{loginStrings.forgotPassword}</Box>
//                </Box> */}
//                {/* <Box
//                   display='flex'
//                   justifyContent='center'
//                   alignItems='center'
//                   mt={10}
//                   fontSize='sm'
//                   color='gray.500'
//                >
//                   {loginStrings.dontHaveAccount}
//                   <Box ml={1} color='#22DBF5'>
//                      {loginStrings.register}
//                   </Box>
//                </Box> */}
//                {/* <Box display='flex' justifyContent='center' alignItems='center'>
//                   <Button
//                      display='flex'
//                      justifyContent='center'
//                      alignItems='center'
//                      width='30%'
//                      mt={4}
//                      colorScheme='blue'
//                      size={'sm'}
//                   >
//                      {loginStrings.takeATour}
//                   </Button>
//                </Box> */}
//             </Box>
//             <Box
//                width='100%'
//                height='100%'
//                justifyContent='center'
//                alignItems='center'
//                display={'grid'}
//             >
//                <Box width='100%' maxW='600px' justifyContent={'center'}>
//                   <Box fontSize='4xl' fontWeight='bold' textAlign='center'>
//                      {appStrings.appGreet}
//                   </Box>
//                   <Box fontSize='md' textAlign='center'>
//                      {appStrings.appTagLine}
//                   </Box>
//                </Box>
//             </Box>
//          </Stack>
//       </Box>
//    );
// }

// export default Login;
