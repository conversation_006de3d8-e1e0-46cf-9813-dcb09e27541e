import dashboardApiAgent from '../../../agent';
import { PromiseAxios } from '../../common';

/** MISCELLANEOUS **/
export interface StreamChunk {
   type: 'tool_call' | 'tool_input' | 'transition' | 'thought' | 'final_result';
   node?: React.ReactNode;
   content?: string;
}

export interface ChunkData {
   status: 'in_progress' | 'completed';
   agent_name: string;
   step_id: string | Record<string, string>;
   type: 'tool_call' | 'tool_input' | 'transition' | 'thought' | 'final_result';
   content: string;
   is_final_chunk: boolean;
   timestamp: number;
   metadata: Record<string, string | number | Record<string, string>>;
   error_count: number;
}

export interface AnalyticsAgentChat {
   client_id: string;
   user_id: string;
   session_id: string;
   chat_id: string;
   user_query: string;
   response_status: 'success' | 'error' | 'pending';
   final_response: string;
   agent_name: string;
   prev_agent_response: string;
   prev_agent_name: string;
   channel: string;
   context_snapshot: Record<string, string>;
   response_like_dislike: string;
   response_feedback_category: string;
   rewrite_response: boolean;
   copy_response: boolean;
   response_time: number;
   user_feedback_comments: string;
   question_mode:
      | 'data-analyst'
      | 'cmo'
      | 'alerting-agent'
      | 'diagnostic-agent';
   diagnostics_prompt_meta_data: Record<string, string>;
   session_name: string;
   created_at: string;
   updated_at: string;
   question_modes?: string[];
}

export interface FeatureUsage {
   client_id: string;
   user_id: string;
   feature_name: string;
   feature_type: string;
   is_enabled: boolean;
   no_of_calls: number;
   free_limit_expired: boolean;
   last_call_made_at: string;
   mode: string;
}

/** PAYLOADS **/
export interface FetchSessionHistoryPayload {
   session_id: string;
   client_id: string;
   user_id: string;
}

export interface FetchAllSessionsHistoryPayload {
   client_id: string;
   user_id: string;
   page: number; // Added for pagination
}
export type FetchAllSessionsHistoryResponse = AnalyticsAgentChat[];

export interface AddToSessionHistoryPayload {
   client_id: string;
   user_id: string;
   session_id: string;
   chat_id: string;
   user_query: string;
   response_status: 'success' | 'error' | 'pending';
   final_response: string;
   agent_name: string;
   prev_agent_response: string;
   prev_agent_name: string;
   channel: string;
   context_snapshot: Record<string, string>;
   response_like_dislike: string;
   response_feedback_category: string;
   rewrite_response: boolean;
   copy_response: boolean;
   response_time: number;
   user_feedback_comments: string;
   question_mode:
      | 'data-analyst'
      | 'cmo'
      | 'alerting-agent'
      | 'diagnostic-agent';
   diagnostics_prompt_meta_data: Record<string, string>;
   session_name: string;
}

export interface FetchFeatureUsagePayload {
   client_id: string;
   user_id: string;
   feature_name: string;
   feature_type: string;
}

export interface TrackFeatureUsagePayload {
   client_id: string;
   user_id: string;
   feature_name: string;
   feature_type: string;
}

export interface FetchChatInsightsByIDPayload {
   client_id: string;
   user_id: string;
   session_id: string;
}

export interface AddInsightsToSessionHistoryPayload {
   client_id: string;
   user_id: string;
   session_id: string;
   chat_id: string;
   chat_flow_context: ChunkData[];
}

export interface LikeDislikeChatPayload {
   client_id: string;
   user_id: string;
   session_id: string;
   chat_id: string;
   action: 'liked' | 'disliked';
}

export interface UpdateChatRewrittenPayload {
   client_id: string;
   user_id: string;
   session_id: string;
   chat_id: string;
}

export interface UpdateChatCopiedPayload {
   client_id: string;
   user_id: string;
   session_id: string;
   chat_id: string;
}

export interface UpdateChatFeedbackPayload {
   client_id: string;
   user_id: string;
   session_id: string;
   chat_id: string;
   type: string;
   feedback: string;
}

/** QUERY RESULTS **/

/** RESPONSES **/
export interface FetchSessionInsightsResponse {
   client_id: string;
   user_id: string;
   session_id: string;
   chat_id: string;
   chat_flow_context: ChunkData[];
}

/** ENDPOINTS **/
interface Endpoints {
   fetchSessionHistory: (
      payload: FetchSessionHistoryPayload,
   ) => PromiseAxios<AnalyticsAgentChat[]>;

   fetchAllSessionsHistory: (
      payload: FetchAllSessionsHistoryPayload,
   ) => PromiseAxios<FetchAllSessionsHistoryResponse>;

   addToSessionHistory: (
      payload: AddToSessionHistoryPayload,
   ) => PromiseAxios<void>;

   fetchUserFeatureUsage: (
      payload: FetchFeatureUsagePayload,
   ) => PromiseAxios<FeatureUsage[]>;

   trackFeatureUsage: (payload: TrackFeatureUsagePayload) => PromiseAxios<void>;

   fetchSessionInsightsByID: (
      payload: FetchChatInsightsByIDPayload,
   ) => PromiseAxios<FetchSessionInsightsResponse[]>;

   addInsightsToSessionHistory: (
      payload: AddInsightsToSessionHistoryPayload,
   ) => PromiseAxios<void>;

   likeDislikeChat: (payload: LikeDislikeChatPayload) => PromiseAxios<void>;

   updateChatRewritten: (
      payload: UpdateChatRewrittenPayload,
   ) => PromiseAxios<void>;

   updateChatCopied: (payload: UpdateChatCopiedPayload) => PromiseAxios<void>;

   updateChatFeedback: (
      payload: UpdateChatFeedbackPayload,
   ) => PromiseAxios<void>;
}

const analyticAgentAPI: Endpoints = {
   fetchSessionHistory: (payload) => {
      return dashboardApiAgent.get(
         `/analytics-agent/${payload.client_id}/${payload.user_id}/session/${payload.session_id}`,
      );
   },

   fetchAllSessionsHistory: (payload) => {
      return dashboardApiAgent.get(
         `/analytics-agent/${payload.client_id}/${payload.user_id}/session`,
         {
            params: {
               page: payload.page, // Added for pagination
            },
         },
      );
   },

   addToSessionHistory: (payload) => {
      return dashboardApiAgent.post(
         `/analytics-agent/${payload.client_id}/${payload.user_id}/session/${payload.session_id}`,
         payload,
      );
   },

   fetchUserFeatureUsage: (payload) => {
      return dashboardApiAgent.get(
         `/settings/${payload.client_id}/${payload.user_id}/feature-usage`,
         {
            params: payload,
         },
      );
   },

   trackFeatureUsage: (payload) => {
      return dashboardApiAgent.post(
         `/settings/${payload.client_id}/${payload.user_id}/feature-usage`,
         payload,
      );
   },

   fetchSessionInsightsByID: (payload) => {
      return dashboardApiAgent.get(
         `/analytics-agent/${payload.client_id}/${payload.user_id}/session/${payload.session_id}/insights`,
      );
   },

   addInsightsToSessionHistory: (payload) => {
      return dashboardApiAgent.post(
         `/analytics-agent/${payload.client_id}/${payload.user_id}/session/${payload.session_id}/insights`,
         payload,
      );
   },

   likeDislikeChat: (payload) => {
      return dashboardApiAgent.put(
         `/analytics-agent/${payload.client_id}/${payload.user_id}/session/${payload.session_id}/chat/${payload.chat_id}/like-dislike`,
         payload,
      );
   },

   updateChatRewritten: (payload) => {
      return dashboardApiAgent.put(
         `/analytics-agent/${payload.client_id}/${payload.user_id}/session/${payload.session_id}/chat/${payload.chat_id}/rewrite`,
      );
   },

   updateChatCopied: (payload) => {
      return dashboardApiAgent.put(
         `/analytics-agent/${payload.client_id}/${payload.user_id}/session/${payload.session_id}/chat/${payload.chat_id}/copied`,
      );
   },

   updateChatFeedback: (payload) => {
      return dashboardApiAgent.put(
         `/analytics-agent/${payload.client_id}/${payload.user_id}/session/${payload.session_id}/chat/${payload.chat_id}/feedback`,
         payload,
      );
   },
};

export default analyticAgentAPI;
