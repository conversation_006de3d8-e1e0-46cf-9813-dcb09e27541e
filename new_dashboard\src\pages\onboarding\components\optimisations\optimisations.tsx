import {
   Accordion,
   AccordionItem,
   AccordionButton,
   Text,
   AccordionIcon,
   AccordionPanel,
   Box,
   Flex,
   Progress,
} from '@chakra-ui/react';
import {
   AmazonAds,
   AmazonSellerPartner,
   FacebookAds,
   GoogleAds,
   Integration,
   Shopify,
   Hubspot,
} from '../../../social-listening/components';
import './optimisations.scss';
import FlablePixelBody from '../flable-pixel-step/flable-pixel-body';
import CompetitorsStepBody from '../competitors-step/competitors-step-body';
import { useAppSelector } from '../../../../store/store';
import IntegrationStatusTag from '../../../../components/integration-status-tag/integration-status-tag';
import { OptimisationsStatus } from '../../../../store/reducer/onboarding-reducer';
import { OptimisationStatusKeys } from '../../../../layouts/app-layout';
import introJs from 'intro.js';
import { TooltipPosition } from 'intro.js/src/packages/tooltip';
import {
   componentNames,
   setFlag,
} from '../../../../store/reducer/tour-reducer';
import { useDispatch } from 'react-redux';
import { useEffect } from 'react';
import Unicommerce from '@/pages/social-listening/components/Unicommerce';

const Optimisations = () => {
   const intro = introJs();
   const dispatch = useDispatch();

   const { optimisations } = useAppSelector((state) => state.tour);
   const { optimisationsStatus } = useAppSelector((state) => state.onboarding);

   const entries = Object.entries(optimisationsStatus).filter(
      ([key]) => key !== 'complete',
   );

   const totalKeys = entries.length;
   const trueKeys = entries.filter(([, status]) => status).length;
   const percentage = (trueKeys / totalKeys) * 100;

   const integrations: {
      title: string;
      componentKey: OptimisationStatusKeys;
      components: React.FC[];
      completed: boolean;
   }[] = [
      {
         title: 'CRM, Channels & Marketplace',
         componentKey: 'channels_marketplace',
         components: [Shopify, Hubspot, AmazonSellerPartner, Unicommerce],
         completed: optimisationsStatus.channels_marketplace,
      },
      {
         title: 'Ads Account',
         components: [GoogleAds, FacebookAds, AmazonAds],
         componentKey: 'ads_account',
         completed: optimisationsStatus.ads_account,
      },
   ];

   const steps: {
      element: string;
      intro: string;
      position: TooltipPosition;
   }[] = [
      {
         element: '#progressBar',
         intro: 'This progress bar helps you track the completion of your profile setup. The more you complete your profile, the more beneficial will be the platform.',
         position: 'top',
      },
      {
         element: '#accordion-button-integrationBtn',
         intro: 'In integrations, you can integrate social media, ads platforms & shops.',
         position: 'top',
      },
      {
         element: '#accordion-button-flablePixelBtn',
         intro: 'In Flable Pixel, you can add tracking script in your web page so that we show you the web analytics data.',
         position: 'top',
      },
      {
         element: '#accordion-button-competitorBtn',
         intro: 'In competitors, you can add your competitors social media. This helps you understand their social media.',
         position: 'top',
      },
   ];

   const startTour = () => {
      intro.setOptions({ steps });
      void intro.start();
      dispatch(
         setFlag({
            componentName: componentNames.OPTIMISATIONS,
            flag: false,
         }),
      );
   };

   const calculateTrueValues = (
      optimisationsStatus: OptimisationsStatus,
   ): number => {
      const keysToCheck = ['channels_marketplace', 'ads_account'];
      const result: number = keysToCheck.filter(
         (key: string) =>
            optimisationsStatus[key as OptimisationStatusKeys] === true,
      ).length;

      return result;
   };

   useEffect(() => {
      if (optimisations) startTour();
   }, [optimisations]);

   return (
      <Flex alignItems='center' justifyContent='center'>
         <Box width='90%' mt={5}>
            <Text as='h2' fontSize='20px' fontWeight='700' ml={3} mb={5}>
               Optimisations
            </Text>
            <Flex
               width='95%'
               margin='auto'
               justifyContent='space-between'
               alignItems='center'
            >
               <Progress
                  id='progressBar'
                  width='90%'
                  colorScheme='green'
                  size='md'
                  value={percentage}
                  mb={5}
               />
               <Text fontSize='16px' fontWeight='700' ml={3} mb={5}>
                  {`${percentage.toFixed(0)}% completed`}
               </Text>
            </Flex>
            <Accordion
               margin='auto'
               width='97%'
               defaultIndex={[]}
               allowToggle
               mt={3}
            >
               <AccordionItem className='send-snippet' id='integrationBtn'>
                  <AccordionButton
                     display='flex'
                     alignItems='center'
                     justifyContent='space-between'
                  >
                     <Text
                        fontSize='20px'
                        fontWeight='700'
                        color='#3F3D56'
                        className='optimisationText'
                     >
                        Integrations
                     </Text>
                     <Flex alignItems='center' justifyContent='center' gap={2}>
                        <IntegrationStatusTag
                           complete={
                              optimisationsStatus.ads_account &&
                              optimisationsStatus.channels_marketplace
                           }
                           label='integrations'
                           number={2 - calculateTrueValues(optimisationsStatus)}
                        />
                        <AccordionIcon />
                     </Flex>
                  </AccordionButton>
                  <AccordionPanel pb={4}>
                     <Box width='100%' className='widget-container'>
                        {integrations.map((integration, index) => (
                           <Integration
                              id={`integrationCategory${index + 1}`}
                              key={index}
                              components={integration.components}
                              title={integration.title}
                              completed={integration.completed}
                              componentKey={integration.componentKey}
                           />
                        ))}
                     </Box>
                  </AccordionPanel>
               </AccordionItem>
               <AccordionItem
                  className='send-snippet'
                  mt={5}
                  id='flablePixelBtn'
               >
                  <AccordionButton
                     display='flex'
                     alignItems='center'
                     justifyContent='space-between'
                  >
                     <Text
                        fontSize='20px'
                        fontWeight='700'
                        color='#3F3D56'
                        className='optimisationText'
                     >
                        Flable Pixel
                     </Text>
                     <Flex
                        alignItems='center'
                        justifyContent='center'
                        gap={2}
                        className='flex-container'
                     >
                        <IntegrationStatusTag
                           complete={optimisationsStatus.flable_pixel}
                           label='flable_pixel'
                        />
                        <AccordionIcon />
                     </Flex>
                  </AccordionButton>
                  <AccordionPanel pb={4} className='panel'>
                     <FlablePixelBody />
                  </AccordionPanel>
               </AccordionItem>
               <AccordionItem
                  className='send-snippet '
                  mt={5}
                  id='competitorBtn'
               >
                  <AccordionButton
                     display='flex'
                     alignItems='center'
                     justifyContent='space-between'
                  >
                     <Text
                        fontSize='20px'
                        fontWeight='700'
                        color='#3F3D56'
                        className='optimisationText'
                     >
                        Competitors
                     </Text>
                     <Flex alignItems='center' justifyContent='center' gap={2}>
                        <IntegrationStatusTag
                           complete={optimisationsStatus.competitors}
                           label='competitors'
                        />
                        <AccordionIcon />
                     </Flex>
                  </AccordionButton>
                  <AccordionPanel pb={4}>
                     <CompetitorsStepBody />
                  </AccordionPanel>
               </AccordionItem>
            </Accordion>
         </Box>
      </Flex>
   );
};

export default Optimisations;
