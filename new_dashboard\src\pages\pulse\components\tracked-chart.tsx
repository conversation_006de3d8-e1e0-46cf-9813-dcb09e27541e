import React, { useEffect, useState } from 'react';
import Chart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';
import { useColorMode } from '@chakra-ui/react';

interface KPIData {
   date: string;
   kpi_value: number;
}

interface ChartProp {
   kpiDetails: {
      displayName: string;
      allData: KPIData[];
      stat: string;
   };
}

const CombinedChart: React.FC<ChartProp> = ({ kpiDetails }) => {
   const chartColor =
      kpiDetails.stat.toLowerCase() === 'active' ? '#15994A' : '#A389D4';

   const barData = kpiDetails.allData.map((x: KPIData) => x.kpi_value);

   const [chartData, setChartData] = useState({
      options: {
         chart: {
            id: kpiDetails.displayName,
            toolbar: {
               show: false,
            },
         },
         xaxis: {
            type: 'datetime',
            categories: kpiDetails.allData.map((x: KPIData) => x.date).sort(),
            labels: {
               show: false,
               style: {
                  colors: useColorMode().colorMode === 'dark' ? '#fff' : '#000',
               },
            },
         },
         yaxis: {
            labels: {
               show: false,
               style: {
                  colors: useColorMode().colorMode === 'dark' ? '#fff' : '#000',
               },
            },
         },
         grid: {
            show: false,
            labels: {
               style: {
                  colors: useColorMode().colorMode === 'dark' ? '#fff' : '#000',
               },
            },
         },
         colors: [chartColor],
         legend: {
            show: false,
         },
         dataLabels: {
            enabled: true,
            offsetY: 10,
            style: {
               fontSize: '12px',
               colors: ['#000'],
            },
            background: {
               enabled: false,
            },
         },
         tooltip: {
            intersect: false,
            x: {
               format: 'dd MMM yyyy',
               labels: {
                  style: {
                     colors:
                        useColorMode().colorMode === 'dark' ? '#fff' : '#000',
                  },
               },
            },
            y: {
               formatter: function (val: number) {
                  return `${val}`;
               },
            },
         },
      },
      series: [
         {
            name: kpiDetails.displayName,
            type: 'bar',
            data: barData,
         },
      ],
   });

   useEffect(() => {
      setChartData({
         ...chartData,
         options: {
            ...chartData.options,
            xaxis: {
               ...chartData.options.xaxis,
               categories: kpiDetails.allData.map((x: KPIData) => x.date),
               labels: {
                  show: true,
                  style: {
                     colors:
                        useColorMode().colorMode === 'dark' ? '#fff' : '#000',
                  },
               },
            },
         },
         series: [
            {
               name: kpiDetails.displayName,
               type: 'bar',
               data: barData,
            },
         ],
      });
   }, [kpiDetails]);

   return (
      <Chart
         options={chartData.options as ApexOptions}
         series={chartData.series}
         type='bar'
         width='250'
         height='150'
      />
   );
};

export default CombinedChart;
