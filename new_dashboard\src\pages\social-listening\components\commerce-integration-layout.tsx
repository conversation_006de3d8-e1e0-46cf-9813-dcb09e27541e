import React, { ReactNode, JSX } from 'react';
import './commerce-integration-layout.scss';

interface Props {
   image: string;
   leftContent: JSX.Element;
   children: ReactNode;
}

const CommerceIntegrationLayout: React.FC<Props> = ({
   image,
   leftContent,
   children,
}) => {
   return (
      <div className='commerce-integration'>
         <div className='commerce-integration__left'>{leftContent}</div>
         <div className='commerce-integration__right'>
            <div className='commerce-integration__right-logo'>
               <img src={image} alt='Logo' />
            </div>
            <div className='commerce-integration__right-content'>
               {children}
            </div>
         </div>
      </div>
   );
};

export default CommerceIntegrationLayout;
