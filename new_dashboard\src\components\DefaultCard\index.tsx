import { Button } from '../ui/button';
import { useNavigate } from 'react-router-dom';
import inActivePlanImg from '../../assets/image/InactivePlan.svg';
import { cn } from '@/utils';

type CtaProps = {
   banner?: 'inactivePlan';
   imgClassName?: string;
   title?: string;
   titleClassName?: string;
   desc?: string;
   descClassName?: string;
   actionLabel?: string;
   navigate?: string;
   externalLink?: boolean;
   className?: string;
};

const images = {
   inactivePlan: inActivePlanImg,
};

const DefaultCard = ({
   banner,
   imgClassName,
   title,
   titleClassName,
   desc,
   descClassName,
   actionLabel,
   navigate: link,
   externalLink = false,
   className,
}: CtaProps) => {
   const navigate = useNavigate();

   const handleClick = () => {
      if (!link) return;

      if (externalLink) {
         window.open(link, '_blank');
      } else {
         navigate(link);
      }
   };

   return (
      <section className='flex py-10 items-center justify-around'>
         <div
            className={cn(
               'flex flex-col space-y-6 items-center justify-around text-center w-full',
               className,
            )}
         >
            {banner && (
               <img src={images[banner]} className={cn(imgClassName)} />
            )}
            {title && (
               <h6
                  className={cn('head4 text-jet font-semibold', titleClassName)}
               >
                  {title}
               </h6>
            )}
            {desc && (
               <p
                  className={cn(
                     'text-charcoal para4 font-medium',
                     descClassName,
                  )}
               >
                  {desc}
               </p>
            )}
            {actionLabel && (
               <Button onClick={handleClick}>{actionLabel}</Button>
            )}
         </div>
      </section>
   );
};

export default DefaultCard;
