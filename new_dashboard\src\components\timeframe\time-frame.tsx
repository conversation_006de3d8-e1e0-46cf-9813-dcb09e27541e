import React from 'react';
import './timeframe.scss';

interface TimeFrameSelectionProps {
   onSelect: (selectedTimeFrame: number) => void;
}

const TimeFrameSelection: React.FC<TimeFrameSelectionProps> = ({
   onSelect,
}) => {
   const handleSelect = (timeFrame: number) => {
      onSelect(timeFrame);
   };

   return (
      <div className='timeframe'>
         <button className='timeframe-btn' onClick={() => handleSelect(1)}>
            1 day{' '}
         </button>
         <button className='timeframe-btn' onClick={() => handleSelect(7)}>
            7 days{' '}
         </button>
         <button className='timeframe-btn' onClick={() => handleSelect(30)}>
            30 days
         </button>
         <button className='timeframe-btn' onClick={() => handleSelect(45)}>
            45 days
         </button>
         <button className='timeframe-btn' onClick={() => handleSelect(60)}>
            60 days
         </button>
         <button className='timeframe-btn' onClick={() => handleSelect(90)}>
            90 days
         </button>
         <button className='timeframe-btn' onClick={() => handleSelect(180)}>
            180 days
         </button>
         <button className='timeframe-btn' onClick={() => handleSelect(360)}>
            360 days
         </button>
      </div>
   );
};

export default TimeFrameSelection;
