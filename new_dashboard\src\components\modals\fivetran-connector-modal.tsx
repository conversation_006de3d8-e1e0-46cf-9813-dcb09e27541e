import { useAppSelector } from '../../store/store';
import ModalWrapper from './modal-wrapper';

function FivetranConnectorModal() {
   const { payload } = useAppSelector((state) => state.modal);

   const { heading, content } = (payload?.modalProps || {}) as {
      heading: string;
      content: string;
   };

   return (
      <ModalWrapper
         heading={heading}
         overlayBgcolor='rgba(0, 0, 0, .5)'
         noCloseBtn={true}
      >
         <p>{content}</p>
      </ModalWrapper>
   );
}

export default FivetranConnectorModal;
