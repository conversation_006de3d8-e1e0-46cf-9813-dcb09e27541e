import './button.scss';

interface Props {
   text: string;
   onClick: (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
   className?: string;
}

function Button({ text, className, onClick }: Props) {
   return (
      <button
         className={className ? className + ' custom-btn' : 'custom-btn'}
         onClick={onClick}
      >
         {text}
      </button>
   );
}

export default Button;
