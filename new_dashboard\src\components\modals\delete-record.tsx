import { Button, Flex, Text, useToast } from '@chakra-ui/react';
import ModalWrapper from './modal-wrapper';
import { useAppDispatch, useAppSelector } from '../../store/store';
import { closeModal } from '../../store/reducer/modal-reducer';
import { useApiMutation } from '../../hooks/react-query-hooks';
import endPoints from '../../api/service/cfo';
import { CFOKeys } from '../../pages/dashboard/utils/query-keys';
import { Keys, LocalStorageService } from '../../utils/local-storage';
import {
   setFixedExpenses,
   setShippingProfiles,
   setVariableExpenses,
   ShippingProfiles,
} from '../../store/reducer/cfo-reducer';

function DeleteRecordModal() {
   const modalData = useAppSelector(
      (state) => state.modal.payload?.modalProps,
   ) as {
      type: string;
      data: ShippingProfiles;
   };
   const { shippingProfiles, fixedExpenses, variableExpenses } = useAppSelector(
      (state) => state.cfo,
   );
   const toast = useToast();
   const dispatch = useAppDispatch();
   const getTitle = (type: string) => {
      switch (type) {
         case 'fixed':
            return 'Fixed Expense';
         case 'variable':
            return 'Variable Expense';
         case 'shippingProfile':
            return 'Shipping Profile';
         default:
            return 'Record';
      }
   };
   const handleDelete = () => {
      deleteRecord({
         type: modalData.type,
         clientId: LocalStorageService.getItem(Keys.ClientId) as string,
         zone: modalData.data.zone,
         id: modalData.data.id,
      });
   };
   const handleSuccess = () => {
      dispatch(closeModal());
      toast({
         title: `'${getTitle(modalData.type)} Deleted Successfully`,
         status: 'success',
         duration: 2000,
         isClosable: true,
      });
      switch (modalData.type) {
         case 'fixed':
            dispatch(
               setFixedExpenses(
                  fixedExpenses.filter((x) => x.id != modalData.data.id),
               ),
            );
            break;
         case 'variable':
            dispatch(
               setVariableExpenses(
                  variableExpenses.filter((x) => x.id != modalData.data.id),
               ),
            );
            break;
         case 'shippingProfile':
            dispatch(
               setShippingProfiles(
                  shippingProfiles.filter((x) => x.id != modalData.data.id),
               ),
            );
            break;
      }
   };
   const { mutate: deleteRecord } = useApiMutation({
      queryKey: [CFOKeys.deleteRecord],
      mutationFn: endPoints.deleteRecord,
      onSuccessHandler: handleSuccess,
   });
   return (
      <ModalWrapper
         heading={`Delete Your ${getTitle(modalData.type)}`}
         parentClassName='delete-record'
      >
         <Flex direction={'column'} gap={3} borderTop={'1px solid #C2CBD4'}>
            <Text
               pt={6}
               pb={3}
            >{`Deleting this ${getTitle(modalData.type)} will remove it form all past data. This action can’t be undone, are
you sure you want to delete ?`}</Text>
            <Flex
               pt={3}
               justifyContent={'flex-end'}
               gap={4}
               borderTop={'1px solid #C2CBD4'}
            >
               <Button
                  onClick={() => dispatch(closeModal())}
                  py={4}
                  px={6}
                  border={'1px solid #C2CBD4'}
                  background={'none'}
                  borderRadius={'7px'}
               >
                  Cancel
               </Button>
               <Button
                  onClick={handleDelete}
                  py={4}
                  px={6}
                  color={'white'}
                  borderRadius={'7px'}
                  backgroundColor={'#FF2222'}
                  _hover={{
                     backgroundColor: '#FF4444',
                  }}
                  border={'1px solid #FF9999'}
               >
                  Delete
               </Button>
            </Flex>
         </Flex>
      </ModalWrapper>
   );
}

export default DeleteRecordModal;
