import React, { useState, useEffect } from 'react';
import './popup.scss';
import { Link } from 'react-router-dom';
import AdsetPopup from './adset-popup';
import { IndustryBenchmarking, UserDetails } from './interface';
import { useAppSelector } from '../../../store/store';
import {
   cap,
   toShowCurrency,
   truncateText,
   sortAdSetsByNestedKPI,
   DEFAULT_SORT_BY_OBJECTIVE,
   objectiveToMetrics,
   withoutBaseMetrics,
   getDateRange,
} from '../utils/helper';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import MultiChart from './campaign-insights-chart';
import { Box, Flex, Skeleton, SkeletonText, Tooltip } from '@chakra-ui/react';
import { LinkIcon } from '@chakra-ui/icons';
import { calculateHelper } from '../../utils/kpiCalculaterHelper';
import { Lu<PERSON>rrowDownUp, LuArrowUp, LuArrowDown } from 'react-icons/lu';
import pulseMetaService, {
   DaywiseAdsetKPIsCalculated,
   DaywiseCampaignKPIsCalculated,
} from '../../../api/service/pulse/performance-insights/meta-ads';
import { FaCircle } from 'react-icons/fa';
import { useApiQuery } from '../../../hooks/react-query-hooks';
import { pulseMetaKeys } from '../../dashboard/utils/query-keys';

interface PopupProps {
   isOpen: boolean;
   onClose: () => void;
   details: string;
   campaign: DaywiseCampaignKPIsCalculated;
}

interface SortDetails {
   metric: string;
   order: 'asc' | 'desc';
}

const Popup: React.FC<PopupProps> = ({ onClose, campaign }) => {
   const userDetails =
      LocalStorageService.getItem<UserDetails>(Keys.FlableUserDetails) ||
      ({} as UserDetails);

   const { dateRange, prevRange } = useAppSelector((state) => state.kpi);
   const { channel, metric, objective, metricsOptions } = useAppSelector(
      (state) => state.dropdown,
   );

   const metricOrder = objectiveToMetrics[objective] || [];
   const { start_date, end_date, prev_start_date, prev_end_date, days } =
      getDateRange(dateRange, prevRange);

   const defaultSortOrder: SortDetails = {
      metric:
         DEFAULT_SORT_BY_OBJECTIVE[
            objective as keyof typeof DEFAULT_SORT_BY_OBJECTIVE
         ],
      order: 'desc' as 'asc' | 'desc',
   };

   const [isNestedOpen, setIsNestedOpen] = useState(false);
   const [adsets, setAdsets] = useState<DaywiseAdsetKPIsCalculated[]>([]);
   const [selectedMetric, setSelectedMetric] = useState('');
   const [selectedAdset, setSelectedAdset] =
      useState<DaywiseAdsetKPIsCalculated>();
   const [sortDetails, setSortDetails] =
      useState<SortDetails>(defaultSortOrder);
   const [expandedRows, setExpandedRows] = useState<{ [key: number]: boolean }>(
      {},
   );

   const budgetSpendPayload = {
      client_id: userDetails?.client_id,
      campaign_id: String(campaign.campaign_id),
      objective: objective,
      start_date: start_date,
      end_date: end_date,
   };

   const {
      data: budgetSpendCampaigns,
      isFetching: isBudgetSpendCampaignsFetching,
      isSuccess: fetchedBudgetSpendCampaigns,
   } = useApiQuery({
      queryKey: [
         pulseMetaKeys?.budgetSpend,
         String(channel),
         objective,
         `${campaign.campaign_id}`,
         metric,
         selectedMetric,
         JSON.stringify(dateRange),
         JSON.stringify(prevRange),
      ],
      queryFn: () =>
         pulseMetaService.fetchCampaignWithBudgetSpend(budgetSpendPayload),
      enabled: true,
      refetchOnWindowFocus: false,
   });

   const adsetPayload = {
      client_id: userDetails.client_id,
      campaign_id: campaign.campaign_id,
      objective: objective,
      start_date: start_date,
      end_date: end_date,
      prev_start_date: prev_start_date,
      prev_end_date: prev_end_date,
   };

   const {
      data: metaAdsets,
      isFetching: isMetaAdsetsFetching,
      isSuccess: fetchedMetaAdsets,
   } = useApiQuery({
      queryKey: [
         pulseMetaKeys.metaAdsets,
         metric,
         objective,
         `${campaign.campaign_id}`,
         JSON.stringify(dateRange),
         JSON.stringify(prevRange),
      ],
      queryFn: () => pulseMetaService.fetchMetaAdsetsDaywise(adsetPayload),
      enabled: true,
      refetchOnWindowFocus: false,
   });

   const chartInsightsPayload = {
      client_id: userDetails.client_id,
      timeframe: [start_date, end_date, String(days)],
      chartData: {
         [selectedMetric]:
            budgetSpendCampaigns?.data?.[
               selectedMetric as keyof typeof budgetSpendCampaigns
            ] || [],
         spend: budgetSpendCampaigns?.data?.spend || [],
      },
   };

   const { data: chartInsights, isFetching: isChartInsightsFetching } =
      useApiQuery({
         queryKey: [
            pulseMetaKeys?.chartInsights,
            String(channel),
            objective,
            selectedMetric,
            `${campaign.campaign_id}`,
            JSON.stringify(dateRange),
            JSON.stringify(prevRange),
         ],
         queryFn: () =>
            pulseMetaService.fetchChartInsights(chartInsightsPayload),
         enabled: !!budgetSpendCampaigns?.data && !!selectedMetric,
         refetchOnWindowFocus: false,
         // staleTime: Infinity,
      });

   const benchmarkInsightsPayload = {
      client_id: userDetails.client_id,
      timeframe: [start_date, end_date, String(days)],
      chartData: {
         [selectedMetric]:
            budgetSpendCampaigns?.data?.[
               selectedMetric as keyof typeof budgetSpendCampaigns
            ] || [],
         spend: budgetSpendCampaigns?.data?.spend || [],
      },
      adsets: adsets,
   };

   const { data: benchmarkInsights, isFetching: isBenchmarkInsightsFetching } =
      useApiQuery({
         queryKey: [
            pulseMetaKeys?.benchmarkInsights,
            String(channel),
            objective,
            metric,
            JSON.stringify(dateRange),
            JSON.stringify(prevRange),
         ],
         queryFn: () =>
            pulseMetaService.fetchBenchmarkInsights(benchmarkInsightsPayload),
         enabled:
            !!budgetSpendCampaigns?.data &&
            !!metaAdsets?.data &&
            !!selectedMetric,
         refetchOnWindowFocus: false,
         // staleTime: Infinity,
      });

   const adsetInsightsPayload = {
      client_id: userDetails.client_id,
      timeframe: [start_date, end_date, String(days)],
      chartData: {
         [selectedMetric]:
            budgetSpendCampaigns?.data?.[
               selectedMetric as keyof typeof budgetSpendCampaigns
            ] || [],
         spend: budgetSpendCampaigns?.data?.spend || [],
      },
      adsets: adsets,
   };

   const { data: adsetInsights, isFetching: isAdsetInsightsFetching } =
      useApiQuery({
         queryKey: [
            pulseMetaKeys?.adsetInsights,
            String(channel),
            objective,
            metric,
            JSON.stringify(dateRange),
            JSON.stringify(prevRange),
         ],
         queryFn: () =>
            pulseMetaService.fetchAdsetInsights(adsetInsightsPayload),
         enabled:
            !!budgetSpendCampaigns?.data &&
            !!metaAdsets?.data &&
            !!selectedMetric &&
            !!adsets.length,

         refetchOnWindowFocus: false,
         // staleTime: Infinity,
      });

   const toggleRow = (key: number) => {
      setExpandedRows((prevState) => ({
         ...prevState,
         [key]: !prevState[key],
      }));
   };

   const handleMetricChange = (newMetric: string) => {
      setSelectedMetric(newMetric);
   };

   const extractLinesFromRecommendation = (
      recommendationString: string,
   ): string[] | null => {
      try {
         const cleanedString: string = recommendationString.replace(
            /```json\n|```/g,
            '',
         );

         const parsedJson = JSON.parse(cleanedString) as Record<string, string>;

         return Object.values(parsedJson);
      } catch (error) {
         console.error('Error parsing recommendation', error);
         return null;
      }
   };

   const handleAdSetClick = (adset_id: number) => {
      const selectedAdset = adsets?.find(
         (adset) => Number(adset.adset_id) === adset_id,
      );

      setSelectedAdset(selectedAdset);
      setIsNestedOpen(true);
   };

   const handleAdSetsSorting = (metric: string) => {
      const order =
         sortDetails.metric === metric
            ? sortDetails.order === 'asc'
               ? 'desc'
               : 'asc'
            : 'asc';
      setSortDetails({ metric, order });

      const sortedAdGroups = sortAdSetsByNestedKPI(adsets, metric, order);

      setAdsets([...sortedAdGroups]);
   };

   const handleNestedClose = () => {
      setIsNestedOpen(false);
   };

   useEffect(() => {
      const metrics = metricsOptions.filter((x) =>
         Object.keys(budgetSpendCampaigns?.data || {}).includes(x.value),
      );

      const newMetric = Object.keys(budgetSpendCampaigns?.data || {}).includes(
         metric,
      )
         ? metric
         : metrics?.[0]?.value;

      setSelectedMetric(newMetric || '');
   }, [fetchedBudgetSpendCampaigns]);

   useEffect(() => {
      if (metaAdsets?.data) {
         const formattedAdsets = sortAdSetsByNestedKPI(
            metaAdsets.data,
            DEFAULT_SORT_BY_OBJECTIVE[
               objective as keyof typeof DEFAULT_SORT_BY_OBJECTIVE
            ],
            'desc',
            metricOrder,
         );

         setAdsets(formattedAdsets);
      }
   }, [fetchedMetaAdsets]);

   return (
      <div className='popup-overlay'>
         <div className='popup-content'>
            <div className='heading'>
               <h3>{campaign?.campaign_name}</h3>
               <button className='close-button' onClick={onClose}>
                  x
               </button>
            </div>
            <hr className='divider' />
            <div className='top'>
               <button>{cap(channel)}</button>
               <button>{`${days} ${days > 1 ? 'days' : 'day'}`}</button>
            </div>
            <h3 className='heading-bs'>Budget vs Spend</h3>
            <Flex direction='column' gap={2} className='details'>
               <MultiChart
                  chart_data={budgetSpendCampaigns?.data || {}}
                  handleMetricChange={handleMetricChange}
                  selectedMetric={selectedMetric}
                  loading={isChartInsightsFetching}
               />
               <Flex className='recommendations' direction={'column'}>
                  <h3>Overall Campaign Recommendations</h3>
                  {isChartInsightsFetching || isBudgetSpendCampaignsFetching ? (
                     <>
                        <SkeletonText mt={4} noOfLines={3} />
                     </>
                  ) : chartInsights?.recommendation ? (
                     <ul className='recommendations-list'>
                        {extractLinesFromRecommendation(
                           chartInsights?.recommendation,
                        )?.map((line, index) => <li key={index}>{line}</li>)}
                     </ul>
                  ) : (
                     <ul>No data available</ul>
                  )}
               </Flex>
            </Flex>

            <Flex className='industry-ben' direction='column'>
               <h3>Industry Benchmark</h3>
               <Flex direction='column' gap={2} className='industry-benchmark'>
                  <table className='adset-table'>
                     <thead>
                        <tr>
                           <th>KPI</th>
                           <th>Campaign Value</th>
                           <th>Industry Benchmark</th>
                           <th>Historical Mean</th>
                           <th>Historical Min-Max</th>
                           <th>Observations</th>
                        </tr>
                     </thead>
                     <tbody>
                        {isBenchmarkInsightsFetching ||
                        isBudgetSpendCampaignsFetching ||
                        isMetaAdsetsFetching ? (
                           <>
                              {Array.from(
                                 { length: metricsOptions.length + 1 },
                                 (_, i) => {
                                    return (
                                       <tr key={i}>
                                          {Array.from({ length: 5 }, (_, j) => {
                                             return (
                                                <td key={j}>
                                                   <SkeletonText
                                                      noOfLines={1}
                                                   />
                                                </td>
                                             );
                                          })}
                                          <td>
                                             <SkeletonText
                                                noOfLines={3}
                                                gap='2'
                                             />
                                          </td>
                                       </tr>
                                    );
                                 },
                              )}
                           </>
                        ) : (
                           withoutBaseMetrics?.[objective]?.map((kpi, idx) => {
                              const currencyValue = toShowCurrency(
                                 kpi,
                                 campaign?.recent_currency,
                              );

                              const parsedBenchmarking: IndustryBenchmarking | null =
                                 benchmarkInsights?.recommendation
                                    ? (JSON.parse(
                                         benchmarkInsights?.recommendation,
                                      ) as IndustryBenchmarking)
                                    : null;

                              return (
                                 <tr key={idx}>
                                    <td style={{ fontWeight: '500' }}>
                                       {cap(kpi?.toUpperCase())}
                                    </td>
                                    <td>
                                       {parsedBenchmarking
                                          ?.current_campaign_values?.[kpi] !=
                                       null
                                          ? currencyValue
                                             ? `${currencyValue} ${parsedBenchmarking.current_campaign_values[kpi]}`
                                             : parsedBenchmarking
                                                  .current_campaign_values[kpi]
                                          : 'N/A'}
                                    </td>
                                    <td>
                                       {parsedBenchmarking
                                          ?.industry_benchmark?.[kpi] ?? 'N/A'}
                                    </td>
                                    <td>
                                       <Tooltip
                                          label={cap(
                                             parsedBenchmarking?.timeframe
                                                ?.value as string,
                                          )}
                                          placement='top'
                                          fontSize='small'
                                       >
                                          <button>
                                             {parsedBenchmarking
                                                ?.overall_historical_mean?.[
                                                kpi
                                             ] != null
                                                ? currencyValue
                                                   ? `${currencyValue} ${parsedBenchmarking.overall_historical_mean[kpi]}`
                                                   : parsedBenchmarking
                                                        ?.overall_historical_mean?.[
                                                        kpi
                                                     ]
                                                : 'N/A'}
                                          </button>
                                       </Tooltip>
                                    </td>
                                    <td>
                                       <Tooltip
                                          label={cap(
                                             parsedBenchmarking?.timeframe
                                                ?.value as string,
                                          )}
                                          placement='top'
                                          fontSize='small'
                                       >
                                          <button>
                                             {parsedBenchmarking
                                                ?.overall_historical_min_max?.[
                                                kpi
                                             ] ?? 'N/A'}
                                          </button>
                                       </Tooltip>
                                    </td>
                                    <td>
                                       {parsedBenchmarking?.observations?.[
                                          kpi
                                       ] ?? 'Data not available'}
                                    </td>
                                 </tr>
                              );
                           })
                        )}
                     </tbody>
                  </table>
               </Flex>
            </Flex>
            <div className='ad-set'>
               <h3>Adset Comparison</h3>
               <div className='adset-overview'>
                  <table className='adset-table'>
                     <thead>
                        <tr>
                           <th>
                              Ad Sets <LinkIcon />
                           </th>
                           <th>Audience Interest</th>
                           <th>Audience Behavior</th>
                           {adsets &&
                              objectiveToMetrics?.[objective]?.map(
                                 (kpi, index) => (
                                    <th key={index}>
                                       <Flex
                                          justifyContent='start'
                                          alignItems='center'
                                          cursor='pointer'
                                          onClick={() =>
                                             handleAdSetsSorting(kpi)
                                          }
                                          role='group'
                                       >
                                          {kpi === 'conversion_rate'
                                             ? 'CVR'
                                             : cap(kpi?.toUpperCase())}
                                          {sortDetails.metric === kpi ? (
                                             sortDetails.order === 'asc' ? (
                                                <LuArrowUp />
                                             ) : (
                                                <LuArrowDown />
                                             )
                                          ) : (
                                             <Box
                                                opacity={0}
                                                transition='opacity 0.2s ease'
                                                _groupHover={{ opacity: 1 }}
                                                ml='4px'
                                             >
                                                <LuArrowDownUp />
                                             </Box>
                                          )}
                                       </Flex>
                                    </th>
                                 ),
                              )}
                        </tr>
                     </thead>
                     <tbody>
                        {isMetaAdsetsFetching
                           ? Array.from({ length: 2 }).map((_, rowIndex) => (
                                <tr key={rowIndex}>
                                   {Array.from({ length: 12 }).map(
                                      (_, cellIndex) => (
                                         <td key={cellIndex}>
                                            <Skeleton
                                               noOfLines={1}
                                               height='10px'
                                            />
                                         </td>
                                      ),
                                   )}
                                </tr>
                             ))
                           : adsets &&
                             adsets.map((adset, index) => {
                                return (
                                   <tr key={index}>
                                      <td width='150px'>
                                         <Flex alignItems='center' gap='10px'>
                                            <Link
                                               to=''
                                               className='links'
                                               onClick={() =>
                                                  handleAdSetClick(
                                                     Number(adset.adset_id),
                                                  )
                                               }
                                            >
                                               {adset?.adset_name}
                                            </Link>
                                            <Box>
                                               {adset?.adset_status ===
                                               'ACTIVE' ? (
                                                  <Tooltip label='Active'>
                                                     <span>
                                                        <FaCircle
                                                           size='12px'
                                                           color='green'
                                                        />
                                                     </span>
                                                  </Tooltip>
                                               ) : (
                                                  <Tooltip label='Paused'>
                                                     <span>
                                                        <FaCircle
                                                           size='12px'
                                                           color='gray'
                                                        />
                                                     </span>
                                                  </Tooltip>
                                               )}
                                            </Box>
                                         </Flex>
                                      </td>
                                      <td
                                         key={index}
                                         className='audience-interest'
                                         width='150px'
                                      >
                                         {truncateText(
                                            adset?.audience_interest,
                                            expandedRows?.[index],
                                            80,
                                         ) || 'N/A'}
                                         {adset?.audience_interest?.length >
                                            80 && (
                                            <a
                                               href='#'
                                               className='view-more'
                                               onClick={(e) => {
                                                  e.preventDefault();
                                                  toggleRow(index);
                                               }}
                                            >
                                               {expandedRows?.[index]
                                                  ? 'View Less'
                                                  : 'View More'}
                                            </a>
                                         )}
                                      </td>
                                      <td width='150px'>
                                         {adset?.audience_behavior || 'N/A'}
                                      </td>
                                      {adset?.kpis?.map((kpi, kpiIndex) => {
                                         const {
                                            percentage,
                                            color,
                                            direction,
                                            currentValue,
                                         } = calculateHelper(
                                            kpi?.kpi_name,
                                            kpi?.kpi_current,
                                            kpi?.kpi_previous,
                                         );

                                         const arrow =
                                            direction === 'is up' ? '↑' : '↓';
                                         const currencyValue = toShowCurrency(
                                            kpi?.kpi_name,
                                            campaign?.recent_currency,
                                         );
                                         if (
                                            !objectiveToMetrics[
                                               objective
                                            ].includes(kpi?.kpi_name)
                                         ) {
                                            return <></>;
                                         }
                                         return (
                                            <td
                                               key={kpiIndex}
                                               className='adset-kpis'
                                            >
                                               <div>
                                                  {currentValue !== 'N/A'
                                                     ? currencyValue
                                                     : ''}
                                                  {currentValue}
                                               </div>
                                               <div>
                                                  {percentage && direction && (
                                                     <span
                                                        style={{
                                                           color,
                                                           display:
                                                              'inline-block', // Added this so that if the arrow doesn't fit on the same line, then both percentage and arrow will move to next line
                                                        }}
                                                     >
                                                        {percentage} {arrow}
                                                     </span>
                                                  )}
                                               </div>
                                            </td>
                                         );
                                      })}
                                   </tr>
                                );
                             })}
                     </tbody>
                  </table>

                  <Flex className='recommendations' direction={'column'}>
                     <h4 className='recommendations-title'>
                        Recommendations for Adsets
                     </h4>
                     {isMetaAdsetsFetching || isAdsetInsightsFetching ? (
                        <>
                           <SkeletonText mt={4} noOfLines={3} />
                        </>
                     ) : adsetInsights?.recommendation ? (
                        <ul className='recommendations-list'>
                           {extractLinesFromRecommendation(
                              adsetInsights?.recommendation,
                           )?.map((line, index) => <li key={index}>{line}</li>)}
                        </ul>
                     ) : (
                        <ul>No data available</ul>
                     )}
                  </Flex>
               </div>
            </div>
         </div>
         {selectedAdset && isNestedOpen && (
            <AdsetPopup
               isOpen={isNestedOpen}
               onBack={handleNestedClose}
               onClose={onClose}
               adset={selectedAdset}
               campaign={campaign}
            />
         )}
      </div>
   );
};

export default Popup;
