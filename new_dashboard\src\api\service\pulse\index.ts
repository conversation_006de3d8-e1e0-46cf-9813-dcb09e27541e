import { AxiosResponse } from 'axios';
import dashboardApiAgent from '../../agent';
import { PromiseAxios } from '../common';
import { TrackedCampaign } from '../../../pages/pulse/components/interface';
import { DaywiseAdsetKPIsCalculated } from './performance-insights/meta-ads';

export interface DynamicInsightRankData {
   business_category: string;
   insight_type: string;
   insight_text: string;
   insight_value: string;
   load_date: string;
}

interface UpdateTrackPayload {
   client_id: string;
   load_date: string;
   insight_text: string;
   tracked: boolean;
}
interface KPI {
   kpi_date: string;
   kpi_name: string;
   kpi_value: string | number;
   kpi_current: string | number;
   kpi_previous: string | number;
   campaign_status: string;
   objective?: string;
   currency: string;
   budget: string;
   channel?: string;
}

export interface TrackedKPIs {
   client_id: string;
   objective: string;
   campaign_id: string;
   kpi_name: string;
   tracked: boolean;
   channel: string;
}
export interface daywiseKPIs {
   [key: string]: Record<string, number>;
}
export interface Campaign {
   campaign_id: number;
   campaign_name: string;
   campaign_status: string;
   currency: string;
   budget: number;
   day_wise_kpis?: daywiseKPIs | null;
   kpis: KPI[];
   total_val: KPIAggregate;
   prev_total_val: KPIAggregate;
   trackedKPIs: TrackedKPIs[];
}

export interface CampaignInsightsKPIs {
   kpi_date: string;
   kpi_name: string;
   kpi_value: string | number;
   kpi_current: string | number;
   kpi_previous: string | number;
   campaign_status: string;
   objective?: string;
   currency?: string;
   budget?: string;
   channel?: string;
}

export interface GetCampignInsightsPayload {
   campaign_id: number;
   campaign_name: string;
   kpis: CampaignInsightsKPIs[];
   total_val: KPIAggregate;
   prev_total_val: KPIAggregate;
   trackedKPIs: TrackedKPIs[];
   objective: string;
}
export interface KPIAggregate {
   [key: string]: number | null;
}

export interface CampaignDaywiseDataResponse {
   [key: string]: Campaign[];
}
export interface CampaignDaywiseKpiResponse {
   [key: string]: CampaignKPI[];
}
export interface CampaignKPI {
   kpi_name: string;
   kpi_value: number;
   budget: number;
   date_time: string;
   currency: string;
}
interface RankResponse {
   rankData: DynamicInsightRankData[];
}

export interface AdsetKpi {
   kpi: string;
   kpi_previous: string | number;
   kpi_current: string | number;
   age_targeting: string;

   age_current: string;
   placement_targeting: string;

   placement_current: string;

   audience_behaviors: string;
   audience_interests: string;
   region_current: string;
}

export interface Adset {
   adset_id: number;
   adset_name: string;
   adset_status: string;
   currency: string;
   performance_category: {
      category: string;
      insights: string;
   };
   kpis: AdsetKpi[];
}
export interface AdsetWithKpiDataResponse {
   fn_adset_with_kpi_get: Adset[];
}
interface Kpis {
   kpi: string;
   kpi_previous: string | number | null;
   kpi_current: string | number | null;
   tracked?: boolean;
}
interface CampaignKpis {
   campaign_id: number;
   campaign_name: string;
   campaign_status: string;
   currency: string;
   kpis: Kpis[];
}

export interface AdDetails {
   client_id: string;
   ad_id: string;
   creative_id: string;
   caption: string;
   title: string;
   creative_status: string;
   ad_status: string;
   creative_type: string;
   call_to_action_type: string;
   instagram_permalink_url: string;
   video_thumbnail: string;
   redirect_link: string;
   image_hash: string;
   images_in_carousel: string;
   image_link: string;
   updated_time: string;
   created_time: string;
}
export interface AdData {
   ad_details: AdDetails[];
   ad_id: number;
   ad_name: string;
   ad_status: string;
   currency: string;
   kpis: Kpis[];
}
export interface AdDataResponse {
   fn_ad_with_kpi_get: AdData[];
}
export interface CampaignKpiwiseDataResponse {
   fn_campaign_with_kpi_details_get: CampaignKpis[];
}

export interface SummaryResponse {
   [key: string]: SummaryRecommendation;
}

export interface SummaryRecommendation {
   Recommendation: string;
}

export interface AdSetRecc {
   scored_adsets: {
      data: ScoredAdset[];
   };
   recommendation: string;
}
export type ScoredAdset = {
   adset_id: string;
   adset_name: string;
   // [key: string]: string | number | null | PerformanceCategory | undefined;
   performance_category?: PerformanceCategory;
};
export interface PerformanceCategory {
   category: string;
   insights: string;
}

interface AdsetSummaryResponse {
   age: string;
   gender: string;
   placement: string;
   region: string;
   country: string;
}

interface TrackedKpisResponse {
   currentPeriod: TrackedCampaign[];
   prevPeriod: TrackedCampaign[];
}

export interface KpiPrevData {
   kpi_start_date: string;
   kpi_end_date: string;
   kpi_prev_start_date: string;
   kpi_prev_end_date: string;
   kpi_name: string;
   kpi_value: number;
   kpi_prev_agg_value: number;
   objective: string;
}
export interface CampaignPrevData {
   campaign_id: string;
   campaign_name: string;
   kpis: KpiPrevData[];
}
interface TrackedKpisPrevResponse {
   result: TrackedCampaign[];
}
interface Objectives {
   fn_get_client_objectives: string[];
   fn_get_client_GoogleAdsObjectives: string[];
}

type DynamicObjectives = Objectives[];

// ------------------------ Google Ads ---------------------------

type DynamicKpisNames = KpisNames[];

interface KpisNames {
   fn_get_client_GoogleAdsKpis: string[];
}

export interface GoogleCampaignKpiwiseDataResponse {
   fn_googleads_campaign_kpi_wise_data_get: GogoleKPiwiseData[];
}

export interface GogoleKPiwiseData {
   campaign_id: number;
   campaign_name: string;
   kpis: KPI[] | null;
   kpi_wise_data: KpiwiseAggregate;
   total_val: KPIAggregate;
}
export interface KpiwiseAggregate {
   [key: string]: KPIEntry[];
}

export interface KPIEntry {
   date_time: string;
   kpi_value: number;
   budget: number;
   currency: string;
}

export interface GoogleAdgroupsDataResponse {
   fn_googleads_adgroup_with_kpi_get: GoogleAdgroupsData[];
}

export interface GoogleAdgroupsData {
   ad_group_id: number;
   ad_group_name: string;
   ad_group_status: string;
   kpis_value: AdgroupKPIData[];
   prev_total_val: KPIAggregate;
   kpis: AdgroupKPIwiseData[];
   device_wise: DimensionsAggregated;
   hour_wise: DimensionsAggregated;
   day_of_week_wise: DimensionsAggregated;
}
export interface AdgroupKPIData {
   kpi_date: string;
   kpi_name: string;
   kpi_value: number | string;
   adgroup_status: string;
}

export interface AdgroupKPIwiseData {
   kpi: string;
   kpi_current: number | string | null;
   kpi_previous: number | string | null;
}

export interface DimensionsAggregated {
   [kpiName: string]: KeywordDimension[];
}
export interface KeywordDimension {
   keyword_name: string;
   current_value: number | null;
   previous_value: number | null;
}

export interface GoogleAdsDataResponse {
   fn_googleads_ads_with_kpi_get: GoogleAdsData[];
}

export interface GoogleAdsData {
   ad_id: number;
   ad_name: string;
   ad_status: string;
   ad_url: string;
   ad_strength: string;
   headlines: string;
   kpis_value: AdgroupKPIData[];
   prev_total_val: KPIAggregate;
   kpis: AdgroupKPIwiseData[];
}

export interface AdgroupSummaryResponse {
   [key: string]: {
      Recommendation: string;
   };
}

export interface GoogleAdsKeywordDataResponse {
   [key: string]: GoogleAdsKeywordsData;
}
export interface GoogleAdsKeywordsData {
   [key: string]: GoogleAdsKeywordTermData | GoogleAdsSearchTermData;
}
export type GoogleAdsSearchTermData = {
   [key: string]: {
      [key: string]: KeywordKPIValues;
   };
};
export type GoogleAdsKeywordTermData = {
   kpi: { keyword: KeywordKPIValues };
   keywordMatchType: string;
};
export interface KeywordKPIValues {
   [key: string]: number | null;
}

// export interface GoogleAdsKeywordData {
//    ad_group_id: number;
//    kpi_values: keywordsKpiValues[];
//    filteredKeyword: filteredKeyword | null;
// }
// interface keywordsKpiValues {
//    kpi_name: string;
//    keyword: string | null;
//    search_terms: string | null;
// }
// interface filteredKeyword {
//    [key: string]: CategoryKpis;
// }
// export interface CategoryKpis {
//    aggregatedKpis: AggregatedKpis;
//    matchTypes: MatchTypes;
// }

// interface AggregatedKpis {
//    [keywordName: string]: KpiValues | null;
// }
// interface MatchTypes {
//    [matchType: string]: SearchTerm[] | null;
// }

// interface SearchTerm {
//    searchTerm: string;
//    kpiValues: KpiValues;
// }
// interface KpiValues {
//    [key: string]: number | null;
// }

interface GoogleAdsTrackedKpisDataResponse {
   fn_get_googleads_campaign_kpis: trackedkpisData[];
}

export interface trackedkpisData {
   campaign_id: number;
   campaign_name: string;
   channel_type: string;
   kpi_name: string;
   kpis: KPI[] | null;
   currency: string;
   campaign_status: string;
   total_val: Record<string, number | null> | null;
   grouped_kpis: trackedGroupKpis | null;
}

export interface trackedGroupKpis {
   [key: string]: Record<string, number | null>;
}

interface GoogleAdsTrackedPrevKpisDataResponse {
   fn_get_googleads_campaign_kpis: trackedPrevkpisData[];
}
export interface trackedPrevkpisData extends trackedkpisData {
   kpi_start_date: string;
   kpi_end_date: string;
   kpi_prev_start_date: string;
   kpi_prev_end_date: string;
}

interface Endpoints {
   fetchDynamicInsightsRank: (payload: {
      client_id: string | undefined;
      days: string;
   }) => Promise<AxiosResponse<RankResponse>>;
   updateTrackWebInsight: (
      payload: UpdateTrackPayload,
   ) => Promise<AxiosResponse<string>>;
   fetchTrackedWebInsights: (clientId: string) => PromiseAxios<{
      trackedInsights: { insight_text: string }[];
   }>;

   /** META ADS **/

   fetchMetaAdsObjectives: (Payload: {
      client_id: string;
   }) => Promise<AxiosResponse<DynamicObjectives>>;

   fetchCampaignDaywise: (payload: {
      client_id: string;
      objective: string;
      kpis: string[];
      start_date: string;
      end_date: string;
      prev_start_date: string;
      prev_end_date: string;
   }) => Promise<AxiosResponse<CampaignDaywiseDataResponse[]>>;

   fetchCampaignKpiDaywise: (payload: {
      client_id: string;
      campaign_id: string;
      objective: string;
      start_date: string;
      end_date: string;
   }) => Promise<AxiosResponse<CampaignDaywiseKpiResponse>>;
   fetchCampaignKpiswise: (payload: {
      client_id: string;
      objective: string;
      kpis: string;
      days: number;
   }) => Promise<AxiosResponse<CampaignKpiwiseDataResponse[]>>;
   fetchAdsets: (payload: {
      client_id: string;
      campaign_id: string;
      kpis: string;
      days: number;
   }) => Promise<AxiosResponse<AdsetWithKpiDataResponse[]>>;
   fetchAds: (payload: {
      client_id: string;
      adset_id: string;
      kpis: string;
      days: number;
   }) => Promise<AxiosResponse<AdDataResponse[]>>;
   fetchCampaignChartdataSummary: (payload: {
      client_id: string;
      chartData: CampaignDaywiseKpiResponse;
      timeframe: Array<string>;
   }) => Promise<AxiosResponse<SummaryResponse>>;
   fetchCampaignBenchmarkSummary: (payload: {
      client_id: string;
      chartData: CampaignDaywiseKpiResponse;
      timeframe: Array<string>;
      adsets: DaywiseAdsetKPIsCalculated[] | undefined;
   }) => Promise<AxiosResponse<SummaryResponse>>;
   fetchAdsetReccomendation: (payload: {
      client_id: string;
      chartData: CampaignDaywiseKpiResponse;
      timeframe: Array<string>;
      adsets: DaywiseAdsetKPIsCalculated[] | undefined;
   }) => Promise<AxiosResponse<AdSetRecc>>;
   fetchAdsetSummary: (payload: {
      client_id: string;
      current_adset_id: number;
      current_adset_name: string;
      adset_data: DaywiseAdsetKPIsCalculated[];
      objective: string;
   }) => Promise<AxiosResponse<AdsetSummaryResponse>>;
   updateTrackedKpis: (payload: {
      client_id: string;
      kpi_name: string;
      objective: string;
      campaign_id: string;
      tracked: boolean;
      channel: string;
   }) => Promise<AxiosResponse<string>>;
   fetchTrackedKpis: (payload: {
      client_id: string;
      channel: string;
      start_date: string;
      end_date: string;
      prev_start_date: string;
      prev_end_date: string;
      groupBy: string;
   }) => Promise<AxiosResponse<TrackedKpisResponse>>;
   fetchTrackedPrevKpis: (Payload: {
      client_id: string;
      channel: string;
      start_date: string;
      end_date: string;
      prev_start_date: string;
      prev_end_date: string;
   }) => Promise<AxiosResponse<TrackedKpisPrevResponse>>;

   // ------------------------------ Google ads  ---------------------- //
   fetchGoogleAdsObjectives: (payload: {
      client_id: string;
   }) => Promise<AxiosResponse<DynamicObjectives>>;
   fetchGoogleAdsKpisNames: (payload: {
      client_id: string;
      channel_type: string;
   }) => Promise<AxiosResponse<DynamicKpisNames>>;
   fetchGoogleAdsCampaignDaywise: (payload: {
      client_id: string;
      channel_type: string;
      kpis: string[];
      start_date: string;
      end_date: string;
      prev_start_date: string;
      prev_end_date: string;
   }) => Promise<AxiosResponse<CampaignDaywiseDataResponse[]>>;
   fetchGoogleAdsKpiwiseData: (payload: {
      client_id: string;
      campaign_id: string;
      channel_type: string;
      start_date: string;
      end_date: string;
   }) => Promise<AxiosResponse<GoogleCampaignKpiwiseDataResponse[]>>;

   fetchGoogleAdgroups: (payload: {
      campaign_id: number;
      client_id: string;
      channel_type: string;
      kpis: string[];
      start_date: string;
      end_date: string;
      prev_start_date: string;
      prev_end_date: string;
   }) => Promise<AxiosResponse<GoogleAdgroupsDataResponse[]>>;
   fetchGoogelAds: (payload: {
      client_id: string;
      campaign_id: number;
      ad_group_id: number;
      channel_type: string;
      kpis: string[];
      start_date: string;
      end_date: string;
      prev_start_date: string;
      prev_end_date: string;
   }) => Promise<AxiosResponse<GoogleAdsDataResponse[]>>;
   updateGoogleAdsTrackedKpis: (payload: {
      client_id: string;
      kpi_name: string;
      objective: string;
      campaign_id: number;
      tracked: boolean;
      channel: string;
   }) => Promise<AxiosResponse<string>>;
   fetchGoogleAdskeywords: (payload: {
      client_id: string;
      campaign_id: number;
      ad_group_id: number;
      // kpis: string[];
      start_date: string;
      end_date: string;
   }) => Promise<AxiosResponse<GoogleAdsKeywordDataResponse[]>>;
   fetchGoogleTrackedKpis: (payload: {
      client_id: string;
      channel: string;
      start_date: string;
      end_date: string;
      groupBy: string;
   }) => Promise<AxiosResponse<GoogleAdsTrackedKpisDataResponse[]>>;
   fetchGoogleTrackedPrevKpis: (payload: {
      client_id: string;
      channel: string;
      groupBy: string;
      start_date: string;
      end_date: string;
      prev_start_date: string;
      prev_end_date: string;
   }) => Promise<AxiosResponse<GoogleAdsTrackedPrevKpisDataResponse[]>>;
   fetchCampaignChartSummary: (payload: {
      client_id: string;
      timeframe: Array<string>;
      campaign_type: string;
      campaign_name: string;
      campaign_id: number;
      chartData: KpiwiseAggregate;

      // ad_groups: GoogleAdgroupsData[];
      // currency: string;
      // campaign_data: CampaignKpis;
   }) => Promise<AxiosResponse<SummaryResponse>>;
   fetchCampaignAdgroupSummary: (payload: {
      client_id: string;
      timeframe: Array<string>;
      campaign_type: string;
      campaign_name: string;
      campaign_id: number;
      ad_groups: GoogleAdgroupsData[];

      // client_id: string;
      // timeframe: Array<string>;
      // ad_group_name: string;
      // ad_group_id: number;
      // currency: string;
      // keyword_matchType: string;
      // keyword: filteredKeyword;
      // metric: string;
      // adgroup_ChartType: string;
      // ads: GoogleAdsData[];
   }) => Promise<AxiosResponse<SummaryResponse>>;
   fetchAdgroupKeywordSummary: (payload: {
      client_id: string;
      timeframe: Array<string>;
      campaign_type: string;
      campaign_name: string;
      campaign_id: number;
      keyword: GoogleAdsKeywordsData[];

      // ad_groups: GoogleAdgroupsData[];
      // timeframe: Array<string>;
      // channel_type: string;
      // currency: string;
      // client_id: string;
      // campaign_name: string;
      // campaign_id: number;
      // campaign_data: CampaignKpis;
      // chartData: KpiwiseAggregate;
   }) => Promise<AxiosResponse<SummaryResponse>>;
   fetchAdgroupGraphSummary: (payload: {
      client_id: string;
      timeframe: Array<string>;
      campaign_type: string;
      campaign_name: string;
      campaign_id: number;
      metric: string;
      adgroup_ChartType: string;
      ad_group_data: GoogleAdgroupsData;

      // client_id: string;
      // timeframe: Array<string>;
      // ad_group_name: string;
      // ad_group_id: number;
      // currency: string;
      // keyword_matchType: string;
      // keyword: filteredKeyword;
      // ads: GoogleAdsData[];
   }) => Promise<AxiosResponse<SummaryResponse>>;
   fetchAdgroupSummary: (payload: {
      client_id: string;
      timeframe: Array<string>;
      campaign_type: string;
      campaign_name: string;
      campaign_id: number;
      ads: GoogleAdsData[];

      // client_id: string;
      // timeframe: Array<string>;
      // ad_group_name: string;
      // ad_group_id: number;
      // currency: string;
      // keyword_matchType: string;
      // keyword: filteredKeyword;
      // metric: string;
      // adgroup_ChartType: string;
      // ad_group_data: GoogleAdgroupsData;
   }) => Promise<AxiosResponse<SummaryResponse>>;
}

const pulseBackendEndpoints: Endpoints = {
   fetchDynamicInsightsRank: (payload) =>
      dashboardApiAgent.post('/dynamic-insights', payload),
   updateTrackWebInsight: (payload) =>
      dashboardApiAgent.post('/dynamic-insights/update-track', payload),
   fetchTrackedWebInsights: (clientId) =>
      dashboardApiAgent.post('/dynamic-insights/tracked-insights', {
         clientId,
      }),
   fetchCampaignDaywise: (payload) =>
      dashboardApiAgent.post('/performance-insights/campaigns', payload),
   fetchCampaignKpiDaywise: (payload) =>
      dashboardApiAgent.post(
         '/performance-insights/campaign-kpi-data',
         payload,
      ),
   fetchCampaignKpiswise: (payload) =>
      dashboardApiAgent.post('/performance-insights/campaigns-kpis', payload),
   fetchAdsets: (payload) =>
      dashboardApiAgent.post('/performance-insights/adsets', payload),
   fetchAds: (payload) =>
      dashboardApiAgent.post('/performance-insights/ads', payload),
   fetchCampaignChartdataSummary: (payload) =>
      dashboardApiAgent.post(
         '/performance-insights/campaign_chartdata_recommendation',
         payload,
      ),
   fetchCampaignBenchmarkSummary: (payload) =>
      dashboardApiAgent.post(
         '/performance-insights/benchmarking_recommendation',
         payload,
      ),
   fetchAdsetReccomendation: (payload) =>
      dashboardApiAgent.post(
         '/performance-insights/adset_recommendation',
         payload,
      ),

   fetchAdsetSummary: (payload) =>
      dashboardApiAgent.post(
         '/performance-insights/get_adset_insights',
         payload,
      ),
   updateTrackedKpis: (payload) =>
      dashboardApiAgent.put('performance-insights/update-track', payload),
   fetchTrackedKpis: (payload) =>
      dashboardApiAgent.post(
         'performance-insights/meta/tracked-campaign-kpis',
         payload,
      ),
   fetchTrackedPrevKpis: (payload) =>
      dashboardApiAgent.post(
         'performance-insights/get_tracked_prev_kpis',
         payload,
      ),
   fetchMetaAdsObjectives: (payload) =>
      dashboardApiAgent.post(
         'performance-insights/get_client_objectives',
         payload,
      ),
   // google-ads routes
   fetchGoogleAdsObjectives: (payload) =>
      dashboardApiAgent.post('google-ads/channel-types', payload),
   fetchGoogleAdsKpisNames: (payload) =>
      dashboardApiAgent.post('google-ads/kpi-names', payload),
   fetchGoogleAdsCampaignDaywise: (payload) =>
      dashboardApiAgent.post('google-ads/campaigns', payload),
   fetchGoogleAdsKpiwiseData: (payload) =>
      dashboardApiAgent.post('google-ads/campaigns-kpi-data', payload),
   fetchGoogleAdgroups: (payload) =>
      dashboardApiAgent.post('google-ads/ad-groups', payload),
   fetchGoogelAds: (payload) =>
      dashboardApiAgent.post('google-ads/ads', payload),
   fetchCampaignChartSummary: (payload) =>
      dashboardApiAgent.post('google-ads/campaign-chart-insights', payload),
   fetchCampaignAdgroupSummary: (payload) =>
      dashboardApiAgent.post('google-ads/campaign-adgroup-insights', payload),
   fetchAdgroupKeywordSummary: (payload) =>
      dashboardApiAgent.post('google-ads/ad-group-keyword-insights', payload),
   fetchAdgroupGraphSummary: (payload) =>
      dashboardApiAgent.post('google-ads/ad-group-graph-insights', payload),
   fetchAdgroupSummary: (payload) =>
      dashboardApiAgent.post('google-ads/ad-group-insights', payload),
   updateGoogleAdsTrackedKpis: (payload) =>
      dashboardApiAgent.put('google-ads/track-kpi', payload),
   fetchGoogleAdskeywords: (payload) =>
      dashboardApiAgent.post('google-ads/keywords', payload),
   fetchGoogleTrackedKpis: (payload) =>
      dashboardApiAgent.post('google-ads/tracked-kpis', payload),
   fetchGoogleTrackedPrevKpis: (payload) =>
      dashboardApiAgent.post('google-ads/tracked-prev-kpis', payload),
};

export default pulseBackendEndpoints;
