import { UserSocialDetails } from '../api/service/onboarding';
import { Media } from '../api/service/social-watch';

export interface CalendarItem {
   uuid: string;
   title: string;
   start: Date;
   end: Date;
   social_media_type: string;
   is_schedule: boolean;
   client_id: string;
   media?: MultimediaData[];
   _id: string;
}

export interface SocialMediaType {
   social_media: string;
}
export type Status = 'idle' | 'loading' | 'success' | 'failed';

export interface MultimediaData {
   media_id?: string;
   image_link?: string;
   rawFile?: string;
   socialMedia?: string;
}

export interface PageInfo {
   id: string;
   name: string;
   page: boolean;
}
export interface UploadToSocialMedia {
   file: File | null;
   encodedUri: string;
   decodedUri: string;
}

export interface MediaState {
   mediaData: Media[];
   status: Status;
   multiMedia: {
      [key: string]: MultimediaData[];
   };
   rawFiles: {
      [key: string]: string[];
   };
   selectedTab: string;
   uploadToSocialMedia: UploadToSocialMedia;
   marcoSidebarOpen: boolean;
   connectionDetails: UserSocialDetails[];
}

export interface AuthMediaData {
   clientID: string;
   openLinkedinModal?: boolean;
}
export interface LinkedinUserDetails {
   access_token: string;
   userId: string;
   screenName: string;
}
