import React from 'react';
import './web-insights.scss';

interface WebinsightsProps {
   components: React.ReactNode[];
   title: string;
   id?: string;
}

const Webinsights: React.FC<WebinsightsProps> = ({ components, title, id }) => (
   <div className='pulse'>
      <div className='title'>
         <h5 id={id} style={{ color: 'var(--title-color)' }}>
            {title}
         </h5>
      </div>
      <div className='pulse-components'>
         {components.map((Component, index) => (
            <React.Fragment key={index}>{Component}</React.Fragment>
         ))}
      </div>
   </div>
);

export default Webinsights;
