import React, { useState, useRef } from 'react';
import { RadioGroup, Stack, Radio, Tooltip } from '@chakra-ui/react';
import './saveas-menu.scss';
import download_icon from '../../assets/icons/download-icon.svg';
import useClickOutside from '../../hooks/click-outside';

interface SaveAsMenuProps {
   handleSave: (url: string, format: string) => void;
   imageUrl: string;
}

const SaveAsMenu: React.FC<SaveAsMenuProps> = ({ handleSave, imageUrl }) => {
   const [saveFormat, setSaveFormat] = useState<string>('png');
   const [showMenu, setShowMenu] = useState<boolean>(false);
   const menuRef = useRef<HTMLDivElement>(null);

   const toggleMenu = () => {
      setShowMenu(!showMenu);
   };
   const handleSaveAndFormatChange = (value: string) => {
      setSaveFormat(value);
      handleSave(imageUrl, value);
      toggleMenu();
   };
   useClickOutside(menuRef, () => {
      setShowMenu(false);
   });

   return (
      <>
         <Tooltip
            label='Save As'
            fontSize='sm'
            bg='#437eeb'
            color='white'
            marginTop='5px'
            marginLeft='30px'
         >
            <img src={download_icon} alt='save_as' onClick={toggleMenu} />
         </Tooltip>
         <div className='save-as-menu' ref={menuRef}>
            {showMenu && (
               <RadioGroup value={saveFormat}>
                  <Stack direction='column' p={1}>
                     <Stack direction='row' spacing={4}>
                        <Radio
                           value='png'
                           onClick={() => handleSaveAndFormatChange('png')}
                        >
                           <p className='value-text'>PNG</p>
                        </Radio>
                        <Radio
                           value='jpeg'
                           onClick={() => handleSaveAndFormatChange('jpeg')}
                        >
                           <p className='value-text'>JPEG</p>
                        </Radio>
                     </Stack>
                     <Stack direction='row' spacing={4}>
                        <Radio
                           value='pdf'
                           onClick={() => handleSaveAndFormatChange('pdf')}
                        >
                           <p className='value-text'>PDF</p>
                        </Radio>
                        <Radio
                           value='csv'
                           onClick={() => handleSaveAndFormatChange('csv')}
                        >
                           <p className='value-text'>CSV</p>
                        </Radio>
                     </Stack>
                  </Stack>
               </RadioGroup>
            )}
         </div>
      </>
   );
};

export default SaveAsMenu;
