import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion } from 'framer-motion';
import { useState } from 'react';

type PricingSwitchProps = {
   onSwitch: () => void;
};

const tabOptions = [
   { value: '0', label: 'Monthly' },
   { value: '1', label: 'Annually (SAVE 20%)' },
];

const PricingSwitch = ({ onSwitch }: PricingSwitchProps) => {
   const [selected, setSelected] = useState('0');

   const handleSwitch = (value: string) => {
      setSelected(value);
      onSwitch();
   };

   return (
      <div className='pricingSwitch pb-4 flex items-center justify-center'>
         <Tabs defaultValue='0' onValueChange={handleSwitch}>
            <TabsList className='relative p-2 px-4 bg-skyLight rounded-full inline-flex w-fit gap-6'>
               <motion.div
                  className='absolute top-2 bottom-2 rounded-full bg-white shadow-md border border-fog'
                  layout
                  initial={false}
                  transition={{ type: 'spring', stiffness: 500, damping: 30 }}
                  style={{
                     left: selected === '0' ? '2%' : '46%',
                     width: selected === '0' ? '40%' : '52%',
                  }}
               />

               {tabOptions.map((tab) => (
                  <TabsTrigger
                     key={tab.value}
                     value={tab.value}
                     className='relative z-10 px-14 py-3 whitespace-nowrap'
                  >
                     {tab.label}
                  </TabsTrigger>
               ))}
            </TabsList>
         </Tabs>
      </div>
   );
};

export default PricingSwitch;
