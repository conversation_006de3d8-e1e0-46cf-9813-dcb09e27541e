@use '../../sass/variable.scss';
.custom-select-container {
   position: relative;
   display: inline-block;
   height: 50px;
}

.custom-select-header {
   display: flex;
   align-items: center;
   justify-content: space-between;
   padding: 5px 10px;
   border: 1px solid #ccc;
   border-radius: 8px;
   cursor: pointer;
   height: 40px;
   width: 160px;
   // [data-theme='dark'] & {
   //    background-color: $controls;
   // }
}

.selected-option {
   flex: 1;
}

.dropdown-icon {
   margin-left: 5px;
}

.dropdown-icon.open {
   transform: rotate(180deg);
}

.custom-select-options {
   position: absolute;
   width: 450px;
   left: 1px;
   top: calc(100% + 20px);
   max-height: 200px;
   overflow-y: auto;
   border: 1px solid #ccc;
   border-radius: 8px;
   background-color: #fff;
   z-index: 1000;
   display: flex;
   flex-wrap: wrap;
   // [data-theme='dark'] & {
   //    background-color: $controls;
   // }
}
.custom-select-options-up {
   position: absolute;
   width: 450px;
   left: 1px;
   top: -220px;
   max-height: 200px;
   overflow-y: auto;
   border: 1px solid #ccc;
   border-radius: 8px;
   background-color: #fff;
   z-index: 1000;
   display: flex;
   gap: 4px;
   flex-wrap: wrap;
   // [data-theme='dark'] & {
   //    background-color: $controls;
   // }
}
.option {
   padding: 10px;
   min-width: 120px;
   cursor: pointer;
   display: flex;
   flex-direction: column;
   align-items: flex-start;
   font-size: 16px;
   box-sizing: border-box;
   &:disabled {
      opacity: 0.6;
   }
   &:disabled:hover {
      background: none;
      cursor: not-allowed;
   }
   .option-description {
      color: #000;
      // [data-theme='dark'] & {
      //    color: $text_color;
      // }
   }
   .option-description.orange {
      color: orange;
   }
}

.option-icon {
   width: 24px;
   height: 24px;
   margin-right: 10px;
}

.option-description {
   font-size: 10px;
   color: #999;
   margin-top: 5px;
}

.option:hover {
   background: #437eeb0d;
   // [data-theme='dark'] & {
   //    background: $controls_hover;
   // }
}
