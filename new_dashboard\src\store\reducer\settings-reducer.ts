import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { GeneralSettings } from '../../api/service/settings';

interface InitialState {
   generalSettings: GeneralSettings;
   language: string;
   timezone: string;
   industry: string;
   category: string;
   timezone_name: string;
}

const initialState: InitialState = {
   generalSettings: {} as GeneralSettings,
   language: '',
   timezone: '',
   industry: '',
   category: '',
   timezone_name: '',
};

const settingsSlice = createSlice({
   name: 'settings',
   initialState,
   reducers: {
      setGeneralSettings: (state, action: PayloadAction<GeneralSettings>) => {
         state.generalSettings = action.payload;
      },
      updateLanguage: (state, action: PayloadAction<string>) => {
         state.language = action.payload;
      },
      updateTimezone: (state, action: PayloadAction<string>) => {
         state.timezone = action.payload;
      },
      updateIndustry: (
         state,
         action: PayloadAction<{ industry: string; category: string }>,
      ) => {
         state.industry = action.payload.industry;
         state.category = action.payload.category;
      },
   },
});

export const {
   setGeneralSettings,
   updateLanguage,
   updateTimezone,
   updateIndustry,
} = settingsSlice.actions;

export default settingsSlice.reducer;
