.skeleton-loading {
   position: relative;
   overflow: hidden;
   height: 70px;
   display: flex;
   flex-direction: column;
   gap: 20px;
   width: 100%;
}

.skeleton-loader-1,
.skeleton-loader-2,
.skeleton-loader-3 {
   position: relative;
   border-radius: 10px;
   overflow: hidden;
   background-color: #f6f6f6;
}

.skeleton-loader-1 {
   width: 80%;
   height: 13px;
}

.skeleton-loader-2 {
   width: 70%;
   height: 12px;
}

.skeleton-loader-3 {
   width: 50%;
   height: 10px;
}

.skeleton-loader-1::before,
.skeleton-loader-2::before,
.skeleton-loader-3::before {
   content: '';
   position: absolute;
   top: 0;
   left: 0;
   width: 100%;
   height: 100%;
   background: linear-gradient(
      90deg,
      rgba(71, 166, 255, 0.894) 0%,
      rgb(90, 131, 255) 50%,
      rgb(93, 177, 255) 100%
   );
   animation: skeleton-pulse 1s infinite linear;
}

@keyframes skeleton-pulse {
   0% {
      transform: translateX(-100%);
   }
   100% {
      transform: translateX(100%);
   }
}
