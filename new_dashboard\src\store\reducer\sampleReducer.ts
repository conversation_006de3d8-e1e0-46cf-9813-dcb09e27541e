// sample redux toolkit code
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface SampleState {
   value: number;
}

const initialState: SampleState = {
   value: 0,
};

const sampleSlice = createSlice({
   name: 'sample',
   initialState,
   reducers: {
      increment: (state: SampleState) => ({
         ...state,
         value: state.value + 1,
      }),
      decrement: (state: SampleState) => ({
         ...state,
         value: state.value - 1,
      }),
      incrementByAmount: (
         state: SampleState,
         action: PayloadAction<number>,
      ) => ({
         ...state,
         value: state.value + action.payload,
      }),
   },
});

export const { increment, decrement, incrementByAmount } = sampleSlice.actions;

export default sampleSlice.reducer;
