import { ChevronDownIcon, ChevronUpIcon } from '@chakra-ui/icons';
import { useState } from 'react';
import { useAppDispatch } from '../../store/store';
import { openModal } from '../../store/reducer/modal-reducer';
import { modalTypes } from '../../components/modals/modal-types';
import useIntegrationConnectionDetails from '../../hooks/use-integration-connection-details';

import './pill-content-accordian.scss';

interface Props {
   title: string;
   description: string;
   postLink: string;
   snippet: string;
   handleBackToWrapper: () => void;
}
// const options = ['LinkedIn', 'Instagram', 'Twitter'];
function decodeHtmlEntities(encodedString: string): string {
   const parser = new DOMParser();
   const decodedString = parser.parseFromString(encodedString, 'text/html')
      .documentElement.textContent;
   return decodedString || '';
}
function PillContentAccordian({
   title,
   description,
   postLink,
   snippet,
   handleBackToWrapper,
}: Props) {
   const [isOpen, setIsOpen] = useState(false);

   const { mediaTypes } = useIntegrationConnectionDetails();
   const text = `Using the title "${title}" and the snippet "${snippet}", create an engaging and unique post that highlights my company's expertise and relevance in the industry. The content should seamlessly integrate my company name and align with industry trends. If the topic seems off-track, creatively adapt it to ensure it remains relevant to my company's focus and industry standards.`;

   function handleClick() {
      setIsOpen((prev) => !prev);
   }
   const dispatch = useAppDispatch();

   function handleOpen() {
      dispatch(
         openModal({
            modalType: modalTypes.SOCIAL_MEDIA_CHANNEL_MODAL,
            modalProps:
               mediaTypes.length > 0
                  ? { options: mediaTypes, text, handleBackToWrapper }
                  : { undefined },
         }),
      );
   }
   const cleanedDescription = decodeHtmlEntities(description);
   return (
      <div className='PillContentAccordian'>
         <div className='top'>
            <div className='left'>
               {isOpen ? (
                  <ChevronUpIcon onClick={handleClick} />
               ) : (
                  <ChevronDownIcon onClick={handleClick} />
               )}

               <h4 className='title'>{title}</h4>
            </div>
            <button className='generateBtn' onClick={handleOpen}>
               Generate
            </button>
         </div>
         {isOpen && (
            <div className='bottom-content'>
               <div>{cleanedDescription}</div>
               <div className='post-link'>
                  <a href={postLink} target='_blank'>
                     View post
                  </a>
               </div>
            </div>
         )}
      </div>
   );
}

export default PillContentAccordian;
