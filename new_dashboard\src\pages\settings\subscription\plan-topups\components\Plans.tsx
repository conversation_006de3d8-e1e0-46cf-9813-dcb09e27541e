import { useState } from 'react';
import PricingSwitch from './PricingSwitch';
import PricingCard from './PricingCard';
import PlansTable from './PricingTable';
import { useApiQuery } from '@/hooks/react-query-hooks';
import subsEndPoints from '@/api/service/subscription';
import { Skeleton } from '@/components/ui/skeleton';
import {
   combinedExtraOptionsByAdSpend,
   combinedSupportOptionsByAdSpend,
} from '../../constants';

const ad_spend = '$8500 to $18000';

export default function Plan() {
   const [isYearly, setIsYearly] = useState(false);
   const togglePricingPeriod = () => setIsYearly(!isYearly);

   const { data: plans, isLoading: isPlanLoading } = useApiQuery({
      queryKey: ['plans'],
      queryFn: () => subsEndPoints.fetchPlans(),
      enabled: true,
      refetchOnWindowFocus: false,
      staleTime: Infinity,
   });

   return (
      <div className='py-8'>
         <PricingSwitch onSwitch={togglePricingPeriod} />
         <section className='flex flex-wrap justify-center gap-8 p-8'>
            {isPlanLoading
               ? Array.from({ length: 2 }).map((_, idx) => (
                    <Skeleton key={idx} className='h-125 w-[40%]' />
                 ))
               : plans &&
                 plans
                    .filter(
                       (plan) =>
                          plan.monthly_ad_spend.includes(ad_spend, 0) ||
                          plan.monthly_ad_spend === '0',
                    )
                    .map((plan) => {
                       return (
                          <PricingCard
                             key={plan.id}
                             {...plan}
                             isYearly={isYearly}
                          />
                       );
                    })}
         </section>
         {isPlanLoading
            ? Array.from({ length: 13 }).map((_, idx) => (
                 <Skeleton key={idx} className='h-10 w-full mt-2 rounded-xs' />
              ))
            : plans && (
                 <PlansTable
                    plansData={plans.filter(
                       (plan) =>
                          plan.monthly_ad_spend.includes(ad_spend, 0) ||
                          plan.monthly_ad_spend === '0',
                    )}
                    extraOptions={
                       combinedExtraOptionsByAdSpend[
                          ad_spend as keyof typeof combinedExtraOptionsByAdSpend
                       ]
                    }
                    supportOptions={
                       combinedSupportOptionsByAdSpend[
                          ad_spend as keyof typeof combinedSupportOptionsByAdSpend
                       ]
                    }
                 />
              )}
      </div>
   );
}
