# Flable New dashboard coding guidelines

## Table of Contents

1. [Introduction](#introduction)
2. [Project Structure](#project-structure)
3. [Naming Conventions](#naming-conventions)
4. [Component Guidelines](#component-guidelines)
5. [TypeScript Guidelines](#typescript-guidelines)
6. [Styling](#styling)
7. [Code Quality](#code-quality)
8. [API handling](#api-handling)
9. [Proposed Implementation(for future)](#proposed-implementations)

## Introduction

This document provides coding guidelines and best practices for developing and maintaining a React TypeScript project.

## Project Structure

Organize the project files in a meaningful way. A common structure might look like this:

<pre>
<code>
src/
|-- api
|-- layouts
├── assets/
├── components/
│   ├── component-name/
│   │   ├── component-name.tsx
│   │   ├── component-name.test.tsx
│   │   ├── component-name.css
│   │   └── index.ts
├── hooks/
├── pages/
├── utils/
├── App.tsx
└── ...
</code>
</pre>

## Naming Conventions

- **Files and Folders:** Use kebab case for a component folder name and the actual component file and it should contain component in CamelCase.
- **Components:** Name components using PascalCase.
- **Variables:** Use camelCase for variables and functions.
- **Types and Interfaces:** Use PascalCase for TypeScript types and interfaces.

## Component Guidelines

- **Props and State:** Define prop and state types using TypeScript interfaces or types.
- **Small Components:** Keep components small and focused. If a component grows too large, consider breaking it into smaller subcomponents.
- **Reusable Components:** Create reusable components and place them in the `components` directory otherwise if it is used only for a specific feature then keep it in the same directory.
- **Strings:** Define all your strings in the utils/strings/<file_name>.ts file and use it in your respective components or files.

## TypeScript Guidelines

- **Types vs Interfaces:** Use `interface` for defining object shapes, and `type` for other types (unions, primitives, etc.).
- **Type Safety:** Ensure type safety throughout the project. Avoid using `any` unless absolutely necessary.
- **Enums and Constants:** Use `enum` for related constants and `const` for constant variables.

- **localStorage:** Define all your storage keys in utils/local-storage.ts file
 <pre>
 <code>
 // Using interface for defining object shapes
 interface User {
   id: number;
   name: string;
   email: string;
 }

// Using type for defining other types
type Status = 'active' | 'inactive' | 'pending';

type Point = {
x: number;
y: number;
};

// Function using User interface
function printUserInfo(user: User) {
console.log(`User ID: ${user.id}, Name: ${user.name}, Email: ${user.email}`);
}

// Function using Status type
function setStatus(status: Status) {
console.log(`Setting status to: ${status}`);
}
</code>

</pre>

## Styling

- **Class Naming:** Follow the BEM (Block Element Modifier) naming convention for class names.

BEM stands for Block Element modifier

1. Block: A standalone component that is meaningful on its own. It represents the higher-level component in the hierarchy.

2. Element: A part of the block that has no standalone meaning and is semantically tied to the block. It represents a descendant of the block.

3. Modifier: A flag on a block or element that changes its appearance or behavior. It represents a different state or variation of the block or element.

```
<div class="button">
  <span class="button__icon"></span>
  <button class="button button--primary">Primary Button</button>
  <i class="button button--primary__icon"></i>
</div>

// Base button styles
.button {
  &__icon {
    // Icon styles
  }

  &--primary {
    // Primary button styles

    &__icon {
      // Icon styles for primary button
    }
  }
}

```

1. button is the block.
2. button\_\_icon is an element of the button block.
3. button--primary is a modifier of the button block, indicating a primary variation.

## Code Quality

- **Linting:** Use ESLint with TypeScript support to maintain code quality and consistency. We are using lint-staged hence before comitting the code, it will run lint command
- **Formatting:** Use Prettier for code formatting. This also runs before committing the coee but in case you're committing the code using --no-verify command, please make sure to run `npm run format:fix' beforehand

## API Handling

1. Use axios for all your http calls. We are gonna use react query in near future to handle all the api handling

## Proposed Implementations

1. **React Query**

- **Description:** Integrate React Query to handle data fetching, caching, and synchronization in the application.
- **Benefits:**
   - Reduced boilerplate code for data fetching.
   - Built-in caching and error handling.
   - Enhanced performance through automatic refetching and stale-while-revalidate strategies.

2. **Lazy Loading**

- **Benefits:**
   - Faster initial load time, leading to improved user engagement.
   - Reduced network usage and bandwidth consumption.
   - Enhanced perceived performance by prioritizing critical content.

3. **Service Workers**

- **Description:** Utilize service workers to enable offline functionality and improve the reliability of the application. Service workers are background scripts that can intercept network requests, cache assets, and provide offline access to content.
- **Benefits:**
   - Offline access to cached content, ensuring a seamless user experience even in low or no connectivity environments.
   - Improved performance through caching of assets and reduced server requests.
   - Enhanced reliability by serving cached content during network outages or server failures.
