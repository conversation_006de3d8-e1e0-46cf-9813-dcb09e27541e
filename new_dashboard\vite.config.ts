import { defineConfig } from 'vite';
import path from 'path';
import react from '@vitejs/plugin-react'; // Assuming a React project
import tailwindcss from '@tailwindcss/vite'; // importing tailwind
import svgr from 'vite-plugin-svgr'; // for importing SVGs as React components

export default defineConfig({
   build: {
      rollupOptions: {
         input: 'index.html',
      },
   },

   envDir: 'config/env',
   plugins: [react(), tailwindcss(), svgr()], // Add React plugin + tailwind plugin
   resolve: {
      alias: {
         '@': path.resolve(__dirname, './src'),
      },
   },
   server: {
      port: 3000, // Set the port to 3000
      hmr: true,
   },
});
