@use '../../sass/variable.scss';
.upload-image-modal {
   .upload-description {
      font-size: 0.9rem;
      margin-bottom: 1rem;
      color: #555;
   }

   .file-input {
      margin-bottom: 1.5rem;
      padding-left: 80px;
      input[type='file'] {
         position: absolute;
         left: 0;
         top: 0;
         opacity: 0;
         cursor: pointer;
      }
      .upload-btn {
         color: #437eeb;
         background-color: white;
         padding: 0.5rem 1rem;
         border: 1px solid #437eeb;
         border-radius: 5px;
         cursor: pointer;
         transition: background-color 0.3s ease;

         // [data-theme='dark'] & {
         //    background-color: $background;
         // }

         &:hover {
            background-color: #437eeb;
            color: white;
         }
         height: 45px;

         align-items: center;
      }

      .loader {
         color: #437eeb;
         background-color: white;
         display: flex;
         align-items: center;
         gap: 4px;
         padding: 0.5rem 1rem;
         width: 150px;
         border: 1px solid #437eeb;
         border-radius: 5px;
         cursor: pointer;
      }
   }
}
