import Category from './category';
import { getPinnedAgg } from '../utils/helpers';
import { PinnedKPIProps } from '../utils/interface';

function PinnedKPI(props: PinnedKPIProps) {
   const { metaData, aggCategoryData, comparedAgg, loading, pinnedId } = props;
   const { pinnedKpiAgg, pinnedPrevAgg, filteredMeta } = getPinnedAgg(
      metaData,
      Object.values(aggCategoryData),
      Object.values(comparedAgg),
   );

   return (
      <Category
         head='pinned'
         id={pinnedId}
         metaData={filteredMeta}
         data={pinnedKpiAgg}
         loading={loading}
         prevData={pinnedPrevAgg}
      />
   );
}

export default PinnedKPI;
