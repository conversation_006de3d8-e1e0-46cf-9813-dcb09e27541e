import {
   Modal,
   ModalOverlay,
   ModalContent,
   ModalCloseButton,
   ModalBody,
   useDisclosure,
   Tooltip,
   Button,
} from '@chakra-ui/react';
import { FaExpandArrowsAlt } from 'react-icons/fa';
import './summarize-menu.scss';
import { PerformanceChartData } from '../chatbox/interface';
import PerformanceChart from '../chart/performancechart/performance-chart';

interface SummarizeMenuProps {
   handleSummarize: (id: string) => void;
   id: string;
   performanceData: PerformanceChartData | PerformanceChartData[] | undefined;
   chartType?: string;
   toShowMenu: boolean;
}

function SummarizeMenu({
   id,
   handleSummarize,
   performanceData,
   chartType,
   toShowMenu,
}: SummarizeMenuProps) {
   const { isOpen, onOpen, onClose } = useDisclosure();
   const handleClick = () => {
      handleSummarize(id);
   };
   if (
      Array.isArray(performanceData) ||
      (performanceData && performanceData.data.length == 0)
   )
      return null;
   return (
      <div className='summarize-menu'>
         {toShowMenu && (
            <Button
               onClick={handleClick}
               color='#5579f2'
               variant={'outline'}
               borderColor={'#5579f2'}
               size={{ base: 'xs', lg: 'sm' }}
               style={{ borderRadius: '5px', height: '30px' }}
               _hover={{ bg: '#5579f2', color: 'white' }}
            >
               Summarize
            </Button>
         )}
         <Tooltip
            label='Maximize'
            fontSize='sm'
            bg='#437eeb'
            color='white'
            marginTop='5px'
            marginLeft='30px'
         >
            <button onClick={onOpen} className='maximize'>
               <FaExpandArrowsAlt />
            </button>
         </Tooltip>
         <Modal isOpen={isOpen} onClose={onClose}>
            <ModalOverlay />
            <ModalContent maxWidth='70%' maxHeight='100%' p={8}>
               <ModalCloseButton />
               <ModalBody overflow={'auto'}>
                  <PerformanceChart
                     performanceData={performanceData}
                     chartType={chartType}
                  />
               </ModalBody>
            </ModalContent>
         </Modal>
      </div>
   );
}

export default SummarizeMenu;
