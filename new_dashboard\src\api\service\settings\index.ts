import { PromiseAxios } from '../common';
import dashboardApiAgent from '../../agent';
import {
   DeleteReportPayload,
   GetReportPayload,
   MetricReport,
   UpdateReportPayload,
   PauseReportPayload,
} from '../../../pages/dashboard/utils/interface';
import { AxiosResponse } from 'axios';

export interface Competitors {
   handle: string;
   metadata?: object;
}

export interface Settings {
   language: string | null;
   timezone: string | null;
   Industry: string | null;
   category: string | null;
}

export interface IndustryOptions {
   [industry: string]: string[];
}

export interface DefaultResponse {
   status: 'Success' | 'Failure';
   message: string;
}

export interface GeneralSettingsPayload {
   client_id: string;
   email_address: string;
}

export interface GeneralSettings {
   client_id: string;
   language: string;
   timezone: string;
   industry: string;
   currency: string;
   annual_revenue: string;
   profile_image: string | null;
   timezone_name: string;
}

export interface LanguageTimezonePayload {
   [key: string]: string;
   client_id: string;
   language: string;
   timezone: string;
}

export interface AccountDetailsPayload {
   client_id: string;
   email_address: string;
   annual_revenue: string;
   industry: string;
   currency: string;
}

export interface ProfileImagePayload {
   client_id: string;
   email_address: string;
   profile_image: string;
}

interface Endpoints {
   fetchGeneralSettings: (
      payload: GeneralSettingsPayload,
   ) => PromiseAxios<GeneralSettings>;

   updateLanguageTimezone: (
      payload: LanguageTimezonePayload,
   ) => PromiseAxios<DefaultResponse>;

   updateAccountDetails: (
      payload: AccountDetailsPayload,
   ) => PromiseAxios<DefaultResponse>;

   updateProfileImage: (
      payload: ProfileImagePayload,
   ) => PromiseAxios<DefaultResponse>;

   fetchLanguage: (payload: {
      client_id: string;
   }) => PromiseAxios<Pick<Settings, 'language'> | null>;

   fetchTimezone: (payload: {
      client_id: string;
   }) => PromiseAxios<Pick<Settings, 'timezone'> | null>;

   fetchIndustry: (payload: {
      client_id: string;
   }) => PromiseAxios<Pick<Settings, 'Industry' | 'category'> | null>;

   updateLanguage: (payload: {
      client_id: string;
      language: string;
   }) => PromiseAxios<Pick<Settings, 'language'> | null>;

   updateTimezone: (payload: {
      client_id: string;
      timezone: string;
   }) => PromiseAxios<Pick<Settings, 'timezone'> | null>;

   updateIndustry: (payload: {
      client_id: string;
      industry: string;
      category: string;
   }) => PromiseAxios<Pick<Settings, 'Industry' | 'category'> | null>;

   verifySocial: (payload: {
      handle: string;
   }) => PromiseAxios<{ verified: boolean }>;

   fetchCompetitors: (payload: {
      client_id: string;
      channel: string;
   }) => PromiseAxios<Array<Competitors>>;

   updateCompetitors: (payload: {
      client_id: string;
      channel: string;
      competitor_handles: Array<Competitors>;
   }) => PromiseAxios<string>;
   sendReportMail: (payload: MetricReport) => Promise<AxiosResponse<string>>;
   createReport: (payload: MetricReport) => Promise<AxiosResponse<string>>;
   updateReport: (
      payload: UpdateReportPayload,
   ) => Promise<AxiosResponse<string>>;
   deleteReport: (
      payload: DeleteReportPayload,
   ) => Promise<AxiosResponse<string>>;
   pauseResumeAutoReport: (
      payload: PauseReportPayload,
   ) => Promise<AxiosResponse<string>>;
   getReports: (
      payload: GetReportPayload,
   ) => Promise<AxiosResponse<MetricReport[]>>;
}

const settingsBackendEndpoints: Endpoints = {
   fetchGeneralSettings: (payload) =>
      dashboardApiAgent.get('/settings/general_settings', {
         params: payload,
      }),

   updateLanguageTimezone: (payload) =>
      dashboardApiAgent.put('/settings/language_timezone', payload),

   updateAccountDetails: (payload) =>
      dashboardApiAgent.put('/settings/account_details', payload),

   updateProfileImage: (payload) =>
      dashboardApiAgent.put('/settings/profile_image', payload),

   fetchLanguage: (payload) =>
      dashboardApiAgent.get('/settings/language', {
         params: { client_id: payload.client_id },
      }),

   fetchTimezone: (payload) =>
      dashboardApiAgent.get('/settings/timezone', {
         params: { client_id: payload.client_id },
      }),

   fetchIndustry: (payload) =>
      dashboardApiAgent.get('/settings/industry', {
         params: { client_id: payload.client_id },
      }),

   updateLanguage: (payload) =>
      dashboardApiAgent.post('/settings/language/update', payload),

   updateTimezone: (payload) =>
      dashboardApiAgent.post('/settings/timezone/update', payload),

   updateIndustry: (payload) =>
      dashboardApiAgent.post('/settings/industry/update', payload),

   verifySocial: (payload) =>
      dashboardApiAgent.get('/settings/verify', {
         params: {
            handle: payload.handle,
         },
      }),

   fetchCompetitors: (payload) =>
      dashboardApiAgent.get('/settings/competitors', {
         params: { client_id: payload.client_id, channel: payload.channel },
      }),

   updateCompetitors: (payload) =>
      dashboardApiAgent.post('/settings/competitors', payload),
   sendReportMail: async (payload) => {
      const res = await dashboardApiAgent.post(
         '/settings/reports/send',
         payload,
      );
      return res;
   },
   createReport: async (payload) => {
      const res = await dashboardApiAgent.post('/settings/reports', payload);
      return res;
   },
   updateReport: async (payload) => {
      const res = await dashboardApiAgent.put('/settings/reports', payload);
      return res;
   },
   deleteReport: async (payload) => {
      const res = await dashboardApiAgent.delete(
         `/settings/reports/${payload.clientId}/${payload.reportId}`,
      );
      return res;
   },
   pauseResumeAutoReport: async (payload) => {
      const res = await dashboardApiAgent.put(
         `/settings/reports/${payload.clientId}/${payload.isSubscribed}/${payload.reportId}`,
      );
      return res;
   },
   getReports: async (payload) => {
      const res = await dashboardApiAgent.get(
         `/settings/reports/${payload.clientId}/${payload.userId}`,
      );
      return res;
   },
};

export default settingsBackendEndpoints;
