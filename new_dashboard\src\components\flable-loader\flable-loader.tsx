import './flable-loader.css';

const FlableLoader = () => {
   return (
      <div className='loader-wrap'>
         <div className='preloader'>
            <div className='preloader-close'>x</div>
            <div id='handle-preloader' className='handle-preloader'>
               <div className='animation-preloader'>
                  <div className='txt-loading'>
                     <img
                        src='./assets/images/favIcon.png'
                        alt=''
                        className='favIcon'
                        style={{ marginRight: '10px' }}
                     />
                     <span data-text-preloader='f' className='letters-loading'>
                        f
                     </span>
                     <span data-text-preloader='l' className='letters-loading'>
                        l
                     </span>
                     <span data-text-preloader='a' className='letters-loading'>
                        a
                     </span>
                     <span data-text-preloader='b' className='letters-loading'>
                        b
                     </span>
                     <span data-text-preloader='l' className='letters-loading'>
                        l
                     </span>
                     <span data-text-preloader='e.' className='letters-loading'>
                        e.
                     </span>
                     <span data-text-preloader='a' className='letters-loading'>
                        a
                     </span>
                     <span data-text-preloader='i' className='letters-loading'>
                        i
                     </span>
                  </div>
               </div>
            </div>
         </div>
      </div>
   );
};

export default FlableLoader;
