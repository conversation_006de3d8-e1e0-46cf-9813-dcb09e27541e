import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface ConfigState {
   title: string;
   userName: string;
   aiText: {
      [key: string]: string;
   };
   id: number;
   aibutton: string;
   loading: boolean;
   ResUrl: string;
   step: number;
}

const initialState: ConfigState = {
   title: 'Dashboard',
   userName: '',
   aiText: {},
   id: 0,
   aibutton: 'AI Assist',
   loading: false,
   ResUrl: '',
   step: 0,
};

const configSlice = createSlice({
   name: 'config',
   initialState,
   reducers: {
      setTitle: (state: ConfigState, action: PayloadAction<string>) => {
         state.title = action.payload;
      },
      setUserName: (state: ConfigState, action: PayloadAction<string>) => {
         state.userName = action.payload;
      },
      setAiText: (
         state: ConfigState,
         action: PayloadAction<{ media: string; text: string }>,
      ) => {
         const { media, text } = action.payload;
         state.aiText = { ...state.aiText, [media]: text };
      },
      resetAiText: (state: ConfigState) => {
         state.aiText = {};
      },
      setAiButton: (state: ConfigState, action: PayloadAction<string>) => {
         state.aibutton = action.payload;
      },
      setLoading: (state: ConfigState, action: PayloadAction<boolean>) => {
         state.loading = action.payload;
      },
      setResponseUrl: (state: ConfigState, action: PayloadAction<string>) => {
         console.log('action.payload imga', action.payload);
         state.ResUrl = action.payload;
      },
      handleStep: (state, action: PayloadAction<number>) => {
         state.step = action.payload;
      },
   },
});

export const {
   setTitle,
   setUserName,
   setAiText,
   resetAiText,
   setAiButton,
   setLoading,
   setResponseUrl,
   handleStep,
} = configSlice.actions;

export default configSlice.reducer;
