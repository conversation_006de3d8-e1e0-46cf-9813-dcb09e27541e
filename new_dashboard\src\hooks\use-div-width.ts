import { useRef, useState, useEffect, ElementRef } from 'react';

const useDivWidth = () => {
   const divRef = useRef<ElementRef<'div'>>(null);
   const [width, setWidth] = useState(0);

   useEffect(() => {
      const updateWidth = () => {
         if (divRef.current) {
            setWidth(divRef.current.offsetWidth);
         }
      };

      updateWidth();
      window.addEventListener('resize', updateWidth);
      return () => {
         window.removeEventListener('resize', updateWidth);
      };
   }, []);

   return { divRef, width };
};

export default useDivWidth;
