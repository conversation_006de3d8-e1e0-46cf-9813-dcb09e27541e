import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { LinkedinUserDetails, PageInfo } from '../../types/social-watch';

interface TwitterConnection {
   oauthToken: string;
   oauthTokenSecret: string;
   actual_account_name: string;
   userId: string;
   channelName: string;
}

interface LinkedinConnection {
   user: LinkedinUserDetails;
   pages: PageInfo[];
}

interface InitialState {
   isFetching: boolean;
   isConnecting: null | string;
   isDisconnecting: null | string;
   connections: {
      twitter: null | TwitterConnection;
      linkedin: null | LinkedinConnection;
   };
}

const initialState: InitialState = {
   isFetching: false,
   isConnecting: null,
   isDisconnecting: null,
   connections: {
      twitter: null,
      linkedin: null,
   },
};

const integrationSlice = createSlice({
   name: 'integration',
   initialState,
   reducers: {
      setIsFetching: (state, action: PayloadAction<boolean>) => {
         state.isFetching = action.payload;
      },
      setIsConnecting: (state, action: PayloadAction<string | null>) => {
         state.isConnecting = action.payload;
      },
      setIsDisconnecting: (state, action: PayloadAction<string | null>) => {
         state.isDisconnecting = action.payload;
      },
      setTwitterConnection: (
         state,
         action: PayloadAction<TwitterConnection | null>,
      ) => {
         state.isFetching = false;
         state.connections.twitter = action.payload;
      },

      setLinkedinConnection: (
         state,
         action: PayloadAction<LinkedinConnection | null>,
      ) => {
         state.isFetching = false;
         state.connections.linkedin = action.payload;
      },
   },
});

export const {
   setIsFetching,
   setTwitterConnection,
   setIsConnecting,
   setIsDisconnecting,
   setLinkedinConnection,
} = integrationSlice.actions;

export default integrationSlice.reducer;
