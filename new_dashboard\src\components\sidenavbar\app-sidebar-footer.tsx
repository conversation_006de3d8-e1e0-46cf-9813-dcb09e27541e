import settingsIcon from '@/assets/icons/settings-sidebar-icon.svg';
import {
   SidebarGroup,
   SidebarMenu,
   SidebarGroupContent,
   SidebarMenuButton,
   SidebarMenuItem,
} from '@/components/ui/sidebar';
import { useAppSelector } from '@/store/store';
import { AuthUser } from '@/types/auth';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import { useNavigate } from 'react-router-dom';
import ProgressCircle from '../progress-circle/progress-circle';
import { ReactNode } from 'react';

interface NAVS {
   name: string;
   url: string;
   icon: string | ReactNode;
}

const AppSidebarFooter = () => {
   const navigate = useNavigate();

   const { optimisationsStatus } = useAppSelector((state) => state.onboarding);

   const userDetails = LocalStorageService.getItem(
      Keys.FlableUserDetails,
   ) as AuthUser;

   const handleClick = (url: string) => {
      navigate(url);
   };

   const entries = Object.entries(optimisationsStatus).filter(
      ([key]) => key !== 'complete',
   );
   const totalKeys = entries.length;
   const trueKeys = entries.filter(([, status]) => status).length;
   const percentage = (trueKeys / totalKeys) * 100;

   const APP_SIDEBAR_FOOTER: NAVS[] = [];

   if (userDetails?.user_role === 'Admin') {
      !optimisationsStatus.complete &&
         APP_SIDEBAR_FOOTER.push({
            name: 'Optimisations',
            url: '/optimisations',
            icon: (
               <ProgressCircle
                  value={percentage}
                  size='20px'
                  color='green'
                  label={`${percentage.toFixed(0)}%`}
               />
            ),
         });

      APP_SIDEBAR_FOOTER.push({
         name: 'Settings',
         url: '/settings',
         icon: settingsIcon,
      });
   }

   return (
      <SidebarGroup>
         <SidebarGroupContent>
            <SidebarMenu>
               {APP_SIDEBAR_FOOTER.map((item) => (
                  <SidebarMenuItem key={item.name}>
                     <SidebarMenuButton
                        className='mb-4 hover:cursor-pointer'
                        asChild
                        tooltip={item.name}
                        onClick={() => handleClick(item.url)}
                     >
                        <div className='flex items-center gap-2'>
                           {typeof item.icon === 'string' ? (
                              <img
                                 src={item.icon}
                                 alt={`${item.name} icon`}
                                 className='w-[24px] text-black'
                              />
                           ) : (
                              item.icon
                           )}
                           <span className='text-[16px] text-black font-bold'>
                              {item.name}
                           </span>
                        </div>
                     </SidebarMenuButton>
                  </SidebarMenuItem>
               ))}
            </SidebarMenu>
         </SidebarGroupContent>
      </SidebarGroup>
   );
};

export default AppSidebarFooter;
