import React from 'react';
import PerformanceL<PERSON><PERSON><PERSON> from './performance-line-chart';
import PerformanceBar<PERSON>hart from './performance-bar-chart';
import PerformancePieChart from './performance-pie-chart';
import { PerformanceChartData } from '../../chatbox/interface';
import KPITable from '../../kpi-table/kpi-table';
interface PerformanceChartProps {
   performanceData: PerformanceChartData | PerformanceChartData[] | undefined;
   chartType?: string;
}

const PerformanceChart: React.FC<PerformanceChartProps> = ({
   performanceData,
   chartType,
}) => {
   if (!performanceData) return null;
   let type = chartType ? chartType : 'bar';
   let prevData, xAxisField: string, yAxisField: string;

   if (Array.isArray(performanceData)) {
      xAxisField = performanceData[1].schema.fields[1].name;
      yAxisField = performanceData[1].schema.fields[2].name;
      prevData = performanceData[1];
      prevData?.data.sort(
         (a, b) =>
            new Date(a[xAxisField]).getTime() -
            new Date(b[xAxisField]).getTime(),
      );
      performanceData = performanceData[0];
   } else {
      xAxisField = performanceData.schema.fields[1].name;
      yAxisField = performanceData.schema.fields[2].name;
   }
   performanceData.data = performanceData.data.filter(
      (item) => item[yAxisField] !== null && item[yAxisField] !== undefined,
   );
   if (
      (performanceData.schema?.fields &&
         performanceData.schema?.fields?.length > 4) ||
      performanceData.table
   ) {
      type = 'table';
      performanceData.data.sort(
         (a, b) =>
            new Date(b[xAxisField]).getTime() -
            new Date(a[xAxisField]).getTime(),
      );
   } else if (xAxisField.includes('dat')) {
      performanceData.data.sort(
         (a, b) =>
            new Date(a[xAxisField]).getTime() -
            new Date(b[xAxisField]).getTime(),
      );
   }

   let chartComponent;

   if (performanceData) {
      switch (type) {
         case 'line':
            chartComponent = (
               <PerformanceLineChart
                  theme='dark'
                  performanceData={performanceData}
                  height={350}
                  prevData={prevData}
               />
            );
            break;
         case 'bar':
            chartComponent = (
               <PerformanceBarChart
                  performanceData={performanceData}
                  height={350}
                  prevData={prevData}
                  theme='dark'
               />
            );
            break;
         case 'pie':
            chartComponent = (
               <PerformancePieChart
                  performanceData={performanceData}
                  height={350}
                  theme='dark'
               />
            );
            break;
         case 'table':
            chartComponent = <KPITable performanceData={performanceData} />;
            break;
         default:
            break;
      }
      return <>{chartComponent}</>;
   }
};

export default PerformanceChart;
