import React from 'react';
import {
   CampaignDetails,
   AdsetDetails,
} from '../../../../api/service/agentic-workflow/meta-ads-manager';
import { FaInstagram } from 'react-icons/fa6';
import { FaFacebook } from 'react-icons/fa';
import { BiSolidPackage } from 'react-icons/bi';
import { PiCoinsFill } from 'react-icons/pi';
import { PiTargetFill } from 'react-icons/pi';
import GenderIcon from './Icons/genderIcon.svg';

import locIcon from './Icons/LocIcon.svg';

import atIcon from './Icons/at.svg';
import listIcon from './Icons/list.svg';
interface CampaignSummaryCardProps {
   campaignDetails: CampaignDetails | null;
   adsetDetails: AdsetDetails | null;
}

const CampaignSummaryCard: React.FC<CampaignSummaryCardProps> = ({
   campaignDetails,
   adsetDetails,
}) => {
   if (!campaignDetails || !adsetDetails) return null;

   const { name: campaignName, daily_budget } = campaignDetails;
   const adsetName = adsetDetails.adset_name;
   const targeting = adsetDetails.targeting_analysis;
   const ageRange =
      targeting?.age_min && targeting?.age_max
         ? `${targeting.age_min} - ${targeting.age_max}`
         : 'N/A';
   const placements = targeting.publisher_platforms;
   const locations = targeting?.geo_locations?.countries?.join(', ') || 'N/A';
   const optimizationGoal = adsetDetails.optimization_goal || 'N/A';

   return (
      <div className='w-full max-w-4xl mx-auto bg-[#F8F9FB] rounded-2xl border border-[#E6E8EC] p-4  shadow-sm'>
         <div className='mb-4'>
            <h2 className='text-2xl font-semibold text-gray-900 mb-1 text-center'>
               Launching Campaign
            </h2>
            <p className='text-base text-gray-500 text-center'>
               1 campaign, 1 ad set with 1 ad
            </p>
         </div>
         <div className='mb-6'>
            <div className='flex items-center gap-3 bg-white rounded-xl border border-[#E6E8EC] px-4 py-3 mb-2'>
               <BiSolidPackage size={26} />
               <div>
                  <div className='text-xs text-[#6B7280] font-medium mb-1'>
                     Campaign / Ad Set Name
                  </div>
                  <div className='text-base font-medium text-[#23272E]'>
                     {campaignName} / {adsetName}
                  </div>
               </div>
            </div>
         </div>
         <div className='grid grid-cols-1 md:grid-cols-2 gap-4 mb-6'>
            <div className='flex items-center bg-white rounded-xl border border-[#E6E8EC] px-4 py-3'>
               <PiCoinsFill size={26} />
               <div>
                  <div className='text-xs text-[#6B7280] font-medium mb-1 ml-2'>
                     Total Daily Spend
                  </div>
                  <div className='text-base font-medium text-[#23272E] ml-2'>
                     {daily_budget}
                  </div>
               </div>
            </div>
            <div className='flex items-center bg-white rounded-xl border border-[#E6E8EC] px-4 py-3'>
               <img src={locIcon} alt='Age Range' className='w-6 h-6 mr-3' />
               <div>
                  <div className='text-xs text-[#6B7280] font-medium mb-1'>
                     Location
                  </div>
                  <div className='text-base font-medium text-[#23272E]'>
                     {locations}
                  </div>
               </div>
            </div>
            <div className='flex items-center bg-white rounded-xl border border-[#E6E8EC] px-4 py-3'>
               <img src={listIcon} alt='Age Range' className='w-6 h-6 mr-3' />
               <div>
                  <div className='text-xs text-[#6B7280] font-medium mb-1'>
                     Age Range
                  </div>
                  <div className='text-base font-medium text-[#23272E]'>
                     {ageRange}
                  </div>
               </div>
            </div>
            <div className='flex items-center bg-white rounded-xl border border-[#E6E8EC] px-4 py-3'>
               <PiTargetFill size={25} />
               <div>
                  <div className='text-xs text-[#6B7280] font-medium mb-1 ml-2'>
                     Optimization Goal
                  </div>
                  <div className='text-base font-medium text-[#23272E] ml-2'>
                     {optimizationGoal}
                  </div>
               </div>
            </div>
            <div className='flex items-center bg-white rounded-xl border border-[#E6E8EC] px-4 py-3'>
               <img
                  src={GenderIcon}
                  alt='Optimization Goal'
                  className='w-6 h-6 mr-3'
               />
               <div>
                  <div className='text-xs text-[#6B7280] font-medium mb-1'>
                     Gender
                  </div>
                  <div className='text-base font-medium text-[#23272E]'>
                     Male ♂️ Female ♀️
                  </div>
               </div>
            </div>
            <div className='flex items-center bg-white rounded-xl border border-[#E6E8EC] px-4 py-3'>
               <img
                  src={atIcon}
                  alt='Optimization Goal'
                  className='w-6 h-6 mr-3'
               />
               <div>
                  <div className='text-xs text-[#6B7280] font-medium mb-1'>
                     Publisher Platforms
                  </div>
                  <div className='flex gap-4'>
                     {placements.map((platform) => (
                        <div
                           key={platform}
                           className='flex items-center gap-1 text-base font-medium text-[#23272E]'
                        >
                           {platform == 'instagram' ? (
                              <FaInstagram />
                           ) : (
                              <FaFacebook />
                           )}
                           <span className='capitalize'>{platform}</span>
                        </div>
                     ))}
                  </div>
               </div>
            </div>
         </div>
         <button
            className='w-full py-3 bg-[#377DFF] hover:bg-[#2563EB] text-white text-base font-medium rounded-xl transition mt-2 cursor-pointer'
            style={{ boxShadow: '0px 2px 8px 0px #377DFF1A' }}
            onClick={() =>
               window.open(
                  'https://adsmanager.facebook.com/adsmanager/manage/campaigns?',
                  '_blank',
               )
            }
         >
            Review on Meta
         </button>
      </div>
   );
};

export default CampaignSummaryCard;
