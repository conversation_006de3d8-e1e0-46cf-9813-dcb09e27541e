import { CustomAlertForm } from '@/store/reducer/custom-alerts-reducer';
import { toast } from 'sonner';
import {
   ArrowDownIcon,
   ArrowUpIcon,
   ChevronLeftIcon,
   ChevronRightIcon,
} from '@chakra-ui/icons';
import ShopifyImage from '../../../../assets/icons/kpi/shopify.png';
import MetaImage from '../../../../assets/icons/kpi/meta.png';
import WebImage from '../../../../assets/icons/kpi/web.png';
import GAdImage from '../../../../assets/icons/kpi/gads.png';
import ASPImage from '../../../../assets/icons/kpi/amazon-seller.png';
import AAdsImage from '../../../../assets/icons/kpi/amazon-ads.png';

export const CHANNEL_MAP = {
   facebookads: 'Meta Ads',
   googleads: 'Google Ads',
   amazon_ads: 'Amazon Ads',
   store: 'Shopify',
   web: 'Web',
   amazon_selling_partner: 'Amazon Selling Partner',
};

export const COMPARISON_MAP = {
   more_than: 'more than',
   less_than: 'less than',
   equal_to: 'exactly',
};

export const TREND_MAP = {
   increasing: 'increased',
   decreasing: 'decreased',
};

export const TIME_PERIODS = [
   { label: 'Yesterday', value: '1' },
   { label: 'Last 3 Days', value: '3' },
   { label: 'Last 7 Days', value: '7' },
   { label: 'Last 14 Days', value: '14' },
   { label: 'Last 30 Days', value: '30' },
   { label: 'Last 60 Days', value: '60' },
   { label: 'Last 90 Days', value: '90' },
   { label: 'Last 120 Days', value: '120' },
   { label: 'Last 150 Days', value: '150' },
   { label: 'Last 180 Days', value: '180' },
];

export const splitAndUppercase = (str: string): string => {
   return str
      .split('_')
      .map((word) => word.toUpperCase())
      .join(' ');
};

export const verifyFields = (form: CustomAlertForm) => {
   if (
      !form.alert_name ||
      !form.alert_description ||
      !form.email_recipients.length
   ) {
      toast.error(
         'Alert name, description, and at least one email recipient are required',
      );
      return;
   }

   if (form.email_recipients.some((recipient) => recipient.valid === false)) {
      toast.error('Please enter valid email addresses for all recipients');
      return;
   }

   if (!form.channel) {
      toast.error('Please select a channel for the alert');
      return;
   }

   if (!form.metrics.length) {
      toast.error('Please select at least one metric for the alert');
      return;
   }

   if (!form.target_period.value || !form.reference_period.value) {
      toast.error('Please select both target and reference periods');
      return;
   }

   if (
      form.kpi_rules.some((rule) =>
         Object.values(rule).some(
            (value) => value === '' || value === null || value === undefined,
         ),
      )
   ) {
      toast.error('Please fill all fields in the KPI rules');
      return;
   }

   const formattedCustomAlert = {
      alert_name: form.alert_name,
      alert_description: form.alert_description,
      email_recipients: form.email_recipients.map(
         (recipient) => recipient.email,
      ),
      alert_time: form.alert_time,
      alert_status: form.alert_status,
      alert_conditions: {
         channel: form.channel,
         campaigns: form.campaigns,
         metrics: form.metrics,
         target_period: form.target_period,
         reference_period: form.reference_period,
         kpi_rules: form.kpi_rules,
      },
   };

   return formattedCustomAlert;
};

export const areConditionsFilled = (alert: CustomAlertForm) => {
   return (
      alert.channel &&
      alert.metrics.length > 0 &&
      alert.target_period.value &&
      alert.reference_period.value &&
      alert.kpi_rules.every(
         (rule) =>
            rule.metric &&
            rule.trend &&
            rule.value &&
            rule.value_type &&
            rule.comparison,
      )
   );
};

export const formConditionDescription = (alert: CustomAlertForm) => {
   if (!areConditionsFilled(alert))
      return 'Please fill in all fields to preview when the alert will trigger.';

   const {
      channel,
      campaigns,
      metrics,
      target_period,
      reference_period,
      kpi_rules,
   } = alert;

   const channelName =
      CHANNEL_MAP[channel as keyof typeof CHANNEL_MAP] || channel;
   const multipleCampaigns = campaigns.length > 1;
   const multipleMetrics = metrics.length > 1;
   const allCampaigns = campaigns?.map((c) => c.name).join(', ') || '';
   const allMetrics = kpi_rules
      .map(
         (rule) =>
            `${splitAndUppercase(rule.metric)} has ${TREND_MAP[rule.trend as keyof typeof TREND_MAP]} 
               by ${COMPARISON_MAP[rule.comparison as keyof typeof COMPARISON_MAP]} ${rule.value}${rule.value_type === 'percentage' ? '%' : ''}`,
      )
      .join(', ');

   return `This alert will trigger when the ${multipleMetrics ? 'metrics' : 'metric'} ${allMetrics} 
            ${campaigns.length > 0 ? (multipleCampaigns ? `for the campaigns ${allCampaigns}` : `for the campaign ${allCampaigns}`) : ''} 
            in the channel ${channelName} within ${target_period.label} compared to the ${reference_period.label}.`;
};

export const CHANNELS_WITH_ICONS = {
   googleads: {
      option: 'Google Ads',
      icon: GAdImage,
   },
   facebookads: {
      option: 'Meta Ads',
      icon: MetaImage,
   },
   web: {
      option: 'Web',
      icon: WebImage,
   },
   store: {
      option: 'Shopify',
      icon: ShopifyImage,
   },
   amazon_selling_partner: {
      option: 'Amazon Selling Partner',
      icon: ASPImage,
   },
   amazon_ads: {
      option: 'Amazon Ads',
      icon: AAdsImage,
   },
};

export const TRENDS = {
   decreasing: {
      option: 'Decreasing',
      icon: ArrowDownIcon,
      color: '#B42318',
      background: '#FEF3F2',
   },
   increasing: {
      option: 'Increasing',
      icon: ArrowUpIcon,
      color: '#027A48',
      background: '#ECFDF3',
   },
};

export const COMPARISONS = {
   more_than: {
      option: 'More than',
      icon: ChevronRightIcon,
      color: '#027A48',
      background: '#ECFDF3',
   },
   less_than: {
      option: 'Less than',
      icon: ChevronLeftIcon,
      color: '#B42318',
      background: '#FEF3F2',
   },
   equal_to: {
      option: 'Equal to',
      icon: ChevronRightIcon,
      color: '#0F172A',
      background: '#F1F5F9',
   },
};

export const ALERT_STATUS = {
   active: {
      option: 'Active',
      color: '#027A48',
      background: '#ECFDF3',
   },
   inactive: {
      option: 'Inactive',
      color: '#B42318',
      background: '#FEF3F2',
   },
};

export const TABLE_COLUMNS = [
   { key: 'alert_name', label: 'Alert Name', align: 'start' },
   { key: 'channel', label: 'Data Source', align: 'start' },
   { key: 'campaigns', label: 'Campaigns', align: 'start' },
   { key: 'metrics', label: 'Metrics', align: 'start' },
   { key: 'target_period', label: 'Target Period', align: 'center' },
   { key: 'reference_period', label: 'Reference Period', align: 'center' },
   { key: 'alert_status', label: 'Alert Status', align: 'center' },
];
