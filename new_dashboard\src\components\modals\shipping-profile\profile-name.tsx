import {
   Button,
   Checkbox,
   <PERSON>lapse,
   Flex,
   Heading,
   Input,
   InputGroup,
   InputLeftElement,
   Radio,
   RadioGroup,
   Text,
} from '@chakra-ui/react';
import {
   continents,
   countries,
   ICountry,
   TContinentCode,
} from 'countries-list';
import {
   ChangeEvent,
   Dispatch,
   SetStateAction,
   useEffect,
   useState,
} from 'react';
import { getContinentAgg } from '../../../pages/settings/cost-setting/helpers';
import { FaChevronDown, FaChevronUp } from 'react-icons/fa';
import { IoSearchSharp } from 'react-icons/io5';
import { useAppSelector } from '../../../store/store';
import { useDispatch } from 'react-redux';
import { setProfileName, setzones } from '../../../store/reducer/cfo-reducer';
import { useForm } from 'react-hook-form';
export interface ShippingProfileProps {
   setstep: Dispatch<SetStateAction<string>>;
}
function ProfileName(props: ShippingProfileProps) {
   const { setstep } = props;
   const { name, zones } = useAppSelector((state) => state.cfo.shippingProfile);
   const dispatch = useDispatch();
   const {
      register,
      trigger,
      formState: { errors },
      getValues,
   } = useForm({
      defaultValues: { profileName: name },
      mode: 'onChange',
   });
   const [zone, setzone] = useState<string>(
      Array.isArray(zones) && zones.length > 0
         ? '2'
         : zones == 'worldwide'
           ? '1'
           : '',
   );
   const [searchCountry, setsearchCountry] = useState<string>('');
   const [errorCountry, seterrorCountry] = useState<string>('');
   const [filteredCountries, setCountries] = useState<ICountry[]>(
      Object.values(countries),
   );
   const [checkedCountries, setcheckedCountries] = useState<ICountry[]>(
      Array.isArray(zones) && zones.length > 0 ? zones : [],
   );
   const [collapseContinent, setcollapseContinent] = useState<string>('');
   const handleZoneChange = (nextValue: string) => {
      setzone(nextValue);
      if (nextValue == '1') {
         dispatch(setzones('worldwide'));
      }
   };
   useEffect(() => {
      if (zone == '2') dispatch(setzones(checkedCountries));
   }, [checkedCountries]);
   const handleSearchChange = (e: ChangeEvent<HTMLInputElement>) => {
      setsearchCountry(e.target.value);
      setCountries(
         Object.values(countries).filter((c) =>
            c.name.toLowerCase().includes(e.target.value.toLowerCase()),
         ),
      );
   };
   const handleComponentCollapse = (continent: string) => {
      if (continent == collapseContinent) setcollapseContinent('');
      else setcollapseContinent(continent);
   };
   const handleContinentCheck = (
      e: ChangeEvent<HTMLInputElement>,
      continent: string,
   ) => {
      if (!e.target.checked) {
         setcheckedCountries((prev) =>
            prev.filter((x) => x.continent != continent),
         );
      } else {
         setcheckedCountries((prev) =>
            prev
               .filter((x) => x.continent != continent)
               .concat(
                  Object.values(countries).filter(
                     (x) => x.continent == continent,
                  ) || [],
               ),
         );
      }
      seterrorCountry('');
   };
   const handleCountryCheck = (
      e: ChangeEvent<HTMLInputElement>,
      country: ICountry,
   ) => {
      if (!e.target.checked) {
         setcheckedCountries((prev) =>
            prev.filter((x) => x.name != country.name),
         );
      } else {
         setcheckedCountries((prev) => prev.concat([country]));
      }
      seterrorCountry('');
   };
   const handleStep = () => {
      if (zone == '2' && checkedCountries.length == 0) {
         seterrorCountry('Please select atleast one country');
         return;
      }
      trigger()
         .then((valid) => {
            if (valid) {
               dispatch(setProfileName(getValues('profileName')));
               if (zone == '2') dispatch(setzones(checkedCountries));
               setstep('2');
            }
         })
         .catch((e) => console.log(e));
   };
   return (
      <>
         <Flex direction={'column'} gap={4} width={'100%'}>
            <Text>Profile Name</Text>
            <Input
               {...register(`profileName`, {
                  required: 'Profile name is required',
               })}
               placeholder='Please enter profile name'
            />
            {errors.profileName && (
               <p className='err-message' role='alert'>
                  {'  '}
                  {errors.profileName?.message}
               </p>
            )}
            <RadioGroup
               value={zone}
               defaultValue={zone}
               onChange={handleZoneChange}
               display={'flex'}
               flexDirection={'column'}
               gap={3}
            >
               <Radio value='1'>Worldwide</Radio>
               <Radio value='2'>Specific countries/regions</Radio>
            </RadioGroup>
            <Collapse in={zone === '2'}>
               <InputGroup>
                  <InputLeftElement>
                     <IoSearchSharp />
                  </InputLeftElement>
                  <Input
                     onChange={handleSearchChange}
                     value={searchCountry}
                     placeholder='Search Countries'
                  />
               </InputGroup>

               <Flex direction={'column'} gap={4} pt={5}>
                  {Object.entries(getContinentAgg(filteredCountries)).map(
                     ([continent, countries]) => {
                        return (
                           <Flex gap={4} direction={'column'} key={continent}>
                              <Flex gap={4}>
                                 <Checkbox
                                    isChecked={
                                       Object.values(countries).filter(
                                          (c) => c.continent == continent,
                                       ).length ==
                                       checkedCountries.filter(
                                          (c) => c.continent == continent,
                                       ).length
                                    }
                                    onChange={(e) =>
                                       handleContinentCheck(e, continent)
                                    }
                                 ></Checkbox>
                                 <Heading
                                    display={'flex'}
                                    gap={3}
                                    alignItems={'center'}
                                    fontSize={'16px'}
                                    fontWeight={'400'}
                                    onClick={() =>
                                       handleComponentCollapse(continent)
                                    }
                                 >
                                    {continents[continent as TContinentCode] ||
                                       continent}{' '}
                                    {collapseContinent == continent ? (
                                       <FaChevronUp className='cursor-pointer' />
                                    ) : (
                                       <FaChevronDown className='cursor-pointer' />
                                    )}
                                 </Heading>
                              </Flex>
                              <Collapse in={continent == collapseContinent}>
                                 <Flex
                                    className='countries'
                                    maxHeight={'450px'}
                                    overflowY={'auto'}
                                    gap={3}
                                    pl={4}
                                    direction={'column'}
                                 >
                                    {countries
                                       .sort((a, b) =>
                                          a.name.localeCompare(b.name),
                                       )
                                       .map((c) => (
                                          <Checkbox
                                             key={c.name}
                                             fontSize={'smaller'}
                                             isChecked={
                                                checkedCountries.findIndex(
                                                   (x) => x.name == c.name,
                                                ) > -1
                                             }
                                             onChange={(e) =>
                                                handleCountryCheck(e, c)
                                             }
                                          >
                                             {c.name}
                                          </Checkbox>
                                       ))}
                                 </Flex>
                              </Collapse>
                           </Flex>
                        );
                     },
                  )}
                  {errorCountry && (
                     <p className='err-message' role='alert'>
                        {errorCountry}
                     </p>
                  )}
               </Flex>
            </Collapse>
         </Flex>
         <Flex
            borderTop={'1px solid #C2CBD4'}
            pt={4}
            mt={5}
            width={'100%'}
            justifyContent={'flex-end'}
            gap={4}
         >
            <Button
               onClick={() => handleStep()}
               color={'white'}
               _hover={{
                  backgroundColor: '#437EEBBB',
               }}
               backgroundColor={'#437EEB'}
               py={4}
               px={6}
               border={'1px solid #437EEB'}
               borderRadius={'7px'}
            >
               Next
            </Button>
         </Flex>
      </>
   );
}

export default ProfileName;
