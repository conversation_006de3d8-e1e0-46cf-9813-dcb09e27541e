import {
   Box,
   Text,
   Divider,
   Select,
   useToast,
   Skeleton,
   InputGroup,
   InputLeftElement,
} from '@chakra-ui/react';
import { useApiMutation } from '../../hooks/react-query-hooks';
import { Keys, LocalStorageService } from '../../utils/local-storage';
import settingsService, {
   IndustryOptions,
} from '../../api/service/settings/index';
import moment from 'moment-timezone';
import './setting.scss';
// Get all timezone names (includes both standard and daylight savings time zone names)
const timezones = moment.tz.names();

import { SettingsQueryKeys } from '../dashboard/utils/query-keys';
import { settingsPreferences } from '../../utils/strings/settings-strings';
import { useAppSelector } from '../../store/store';
import {
   retrieveOptions,
   splitIndustryCategories,
} from '../onboarding/utils/helper';
import {
   AutoComplete,
   AutoCompleteInput,
   AutoCompleteItem,
   AutoCompleteList,
} from '@choc-ui/chakra-autocomplete';
import { IoSearchSharp } from 'react-icons/io5';

function Preferences() {
   const toast = useToast();

   const client_id = LocalStorageService.getItem(Keys.ClientId) as string;
   const email_address = LocalStorageService.getItem(Keys.UserName) as string;

   const { masterList } = useAppSelector((state) => state.onboarding);
   const { generalSettings } = useAppSelector((state) => state.settings);

   const annualRevenueOptions: string[] | undefined = retrieveOptions(
      masterList,
      'Annual Revenue',
   );

   const currencyOptions: string[] | undefined = retrieveOptions(
      masterList,
      'Currency',
   )?.sort((a, b) => a.localeCompare(b));

   const industryOptions: IndustryOptions | undefined = splitIndustryCategories(
      retrieveOptions(masterList, 'Industry'),
   );

   const languageTimezoneService = useApiMutation({
      mutationFn: settingsService.updateLanguageTimezone,
      onError(msg) {
         if (msg) showToast('Error', msg, 'error');
      },
      invalidateCacheQuery: [SettingsQueryKeys.generalSettings],
   });

   const accountDetailsService = useApiMutation({
      mutationFn: settingsService.updateAccountDetails,
      onError(msg) {
         if (msg) showToast('Error', msg, 'error');
      },
      invalidateCacheQuery: [SettingsQueryKeys.generalSettings],
   });

   const handleLanguageTimezoneChange = (
      e: React.ChangeEvent<HTMLSelectElement>,
   ) => {
      const { name, value } = e.target;
      languageTimezoneService.mutate({
         client_id,
         email_address,
         language: name === 'language' ? value : generalSettings?.language,
         timezone: name === 'timezone' ? value : generalSettings?.timezone,
      });
   };
   const handleTimezoneChange = (value: string) => {
      languageTimezoneService.mutate({
         client_id,
         email_address,
         language: generalSettings?.language,
         timezone: moment.tz(value).format('z'),
         timezone_name: value,
      });
   };

   const handleAccountDetailsChange = (
      e: React.ChangeEvent<HTMLSelectElement>,
   ) => {
      const { name, value } = e.target;
      accountDetailsService.mutate({
         client_id,
         email_address,
         industry: name === 'industry' ? value : generalSettings?.industry,
         currency: name === 'currency' ? value : generalSettings?.currency,
         annual_revenue:
            name === 'annual_revenue' ? value : generalSettings?.annual_revenue,
      });
   };

   const showToast = (
      title: string,
      description: string,
      status: 'success' | 'error' | 'warning',
   ) => {
      toast({
         title,
         description,
         status,
         duration: 5000,
         isClosable: true,
      });
   };

   return (
      <Box p='2rem' width='100%' className='settings-preferences'>
         <Text as='h5' fontSize='larger' fontWeight='700' mt={0}>
            {settingsPreferences.title}
         </Text>
         <Divider orientation='horizontal' marginY='1em' borderWidth='1px' />
         <Box width='40%' className='settings-preferences-box'>
            <Text>{settingsPreferences.language || ''}</Text>
            <Skeleton
               isLoaded={
                  !languageTimezoneService.isPending &&
                  !accountDetailsService.isPending
               }
            >
               <Select
                  mt='1em'
                  size='lg'
                  name='language'
                  value={generalSettings?.language}
                  onChange={handleLanguageTimezoneChange}
               >
                  <option hidden></option>
                  <option value='en-us'>English (en-us)</option>
                  <option value='de' disabled>
                     German (de) - Coming soon...
                  </option>
               </Select>
            </Skeleton>

            <Text>{settingsPreferences.timezone}</Text>
            <Skeleton
               isLoaded={
                  !languageTimezoneService.isPending &&
                  !accountDetailsService.isPending
               }
            >
               <AutoComplete
                  onChange={handleTimezoneChange}
                  openOnFocus
                  suggestWhenEmpty
                  value={generalSettings?.timezone_name || ''}
               >
                  <InputGroup
                     backgroundColor={'white'}
                     border={'1px solid rgb(226, 232, 240)'}
                     borderRadius={'0.475rem'}
                  >
                     <InputLeftElement pointerEvents='none'>
                        {' '}
                        <IoSearchSharp />
                     </InputLeftElement>
                     <AutoCompleteInput
                        variant='subtle'
                        placeholder='Select Timezone'
                        name='timezone'
                     />
                  </InputGroup>
                  <AutoCompleteList>
                     {timezones.map((timezone, oid) => {
                        const now = moment.tz(timezone);
                        const offset = now.format('Z'); // UTC offset, e.g., +05:30
                        const abbreviation = now.format('z'); // Timezone abbreviation, e.g., IST, CET
                        return (
                           <AutoCompleteItem
                              key={`option-${oid}`}
                              value={timezone}
                              textTransform='capitalize'
                           >
                              {`${timezone} (${abbreviation} ${offset})`}
                           </AutoCompleteItem>
                        );
                     })}
                  </AutoCompleteList>
               </AutoComplete>
            </Skeleton>

            <Text>{settingsPreferences.industry}</Text>
            <Skeleton
               isLoaded={
                  !languageTimezoneService.isPending &&
                  !accountDetailsService.isPending
               }
            >
               <Select
                  mt='1em'
                  size='lg'
                  name='industry'
                  value={generalSettings?.industry || ''}
                  onChange={handleAccountDetailsChange}
               >
                  <option hidden></option>
                  {industryOptions &&
                     Object.keys(industryOptions).map((industry) => (
                        <optgroup key={industry} label={industry}>
                           {industryOptions?.[industry].map((category) => (
                              <option key={category} value={category}>
                                 {category}
                              </option>
                           ))}
                        </optgroup>
                     ))}
               </Select>
            </Skeleton>

            <Text>{settingsPreferences.annualRevenue}</Text>
            <Skeleton
               isLoaded={
                  !languageTimezoneService.isPending &&
                  !accountDetailsService.isPending
               }
            >
               <Select
                  mt='1em'
                  size='lg'
                  name='annual_revenue'
                  value={generalSettings?.annual_revenue || ''}
                  onChange={handleAccountDetailsChange}
               >
                  <option hidden></option>
                  {annualRevenueOptions?.map((annualRevenue) => (
                     <option key={annualRevenue} value={annualRevenue}>
                        {annualRevenue}
                     </option>
                  ))}
               </Select>
            </Skeleton>

            <Text>{settingsPreferences.currency}</Text>
            <Skeleton
               isLoaded={
                  !languageTimezoneService.isPending &&
                  !accountDetailsService.isPending
               }
            >
               <Select
                  mt='1em'
                  size='lg'
                  name='currency'
                  value={generalSettings?.currency || ''}
                  onChange={handleAccountDetailsChange}
               >
                  <option hidden></option>
                  {currencyOptions?.map((currency) => (
                     <option key={currency} value={currency}>
                        {currency}
                     </option>
                  ))}
               </Select>
            </Skeleton>
         </Box>
      </Box>
   );
}

export default Preferences;
