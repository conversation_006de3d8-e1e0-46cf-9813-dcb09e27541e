import moment from 'moment-timezone';

export const formatDate = (
   dateString: string,
   timezone: string | null | undefined,
) => {
   const date = moment.utc(dateString);

   timezone = timezone || moment.tz.guess();
   const dateInUserTimezone = date.tz(timezone);
   const now = moment().tz(timezone);
   const formattedDate = dateInUserTimezone.format('MMM D, YYYY h:mm A');
   const isToday = dateInUserTimezone.isSame(now, 'day');
   if (isToday) {
      const formattedTime = dateInUserTimezone.format('h:mm A');
      return `Today ${formattedTime}`;
   }
   return formattedDate;
};
