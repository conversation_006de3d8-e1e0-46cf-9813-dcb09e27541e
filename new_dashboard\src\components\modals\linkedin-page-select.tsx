import {
   Button,
   CheckboxGroup,
   Flex,
   useToast,
   Checkbox,
   Skeleton,
} from '@chakra-ui/react';
import ModalWrapper from './modal-wrapper';

import './card-view-details.scss';
import { useAppDispatch, useAppSelector } from '../../store/store';
import { useEffect, useState } from 'react';
import { PageInfo } from '../../types/social-watch';
import { useApiMutation } from '../../hooks/react-query-hooks';
import {
   loadingStateChannel,
   SocialWatchQueries,
} from '../../pages/dashboard/utils/query-keys';
import socialWatchEndpoints, {
   LinkedinPageDetails,
   LinkedinProfile,
} from '../../api/service/social-watch/apis';

import { closeModal } from '../../store/reducer/modal-reducer';
import { connectToLinkedinSentiment } from '../../pages/social-listening/utils';
import {
   setIsConnecting,
   setLinkedinConnection,
} from '../../store/reducer/integration-reducer';
function LinkedinPageSelect() {
   const currentModal = useAppSelector((state) => state.modal);
   const { accessToken, refreshToken, clientId } = currentModal.payload
      ?.modalProps as {
      accessToken: string;
      refreshToken: string;
      clientId: string;
   };
   const [selectedPage, setSelectedPage] = useState<PageInfo[]>([]);
   const [linkedinPages, setlinkedinPages] = useState<PageInfo[]>([]);
   const [userDetails, setUserDetails] = useState<LinkedinProfile | null>(null);
   const toast = useToast();
   const dispatch = useAppDispatch();

   const handlePageSelect = (value: string[]) => {
      if (!value.length) return setSelectedPage([]);
      setSelectedPage(linkedinPages.filter((x) => value.includes(x.id)));
   };
   const handleSave = async () => {
      if (!selectedPage.length || !userDetails) {
         return toast({
            title: 'Error saving the page.',
            description: 'Please select any Page to go ahead.',
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      }

      const updatedSelectedPage = selectedPage.some(
         (page) => page.page === false,
      )
         ? selectedPage
         : [
              ...selectedPage,
              {
                 id: userDetails.id,
                 name: userDetails.localizedFirstName,
                 page: false,
              },
           ];

      try {
         dispatch(setIsConnecting(loadingStateChannel.LINKEDIN));
         await connectToLinkedinSentiment({
            isConnect: true,
            client_id: clientId,
            accessToken,
            refreshToken,
            pages: updatedSelectedPage,
         });
         dispatch(
            setLinkedinConnection({
               user: {
                  access_token: accessToken,
                  screenName: userDetails.localizedFirstName,
                  userId: userDetails.id,
               },
               pages: selectedPage,
            }),
         );

         // TODO: need to save all these details in backend and redux

         setTimeout(() => {
            window.location.href = `${window.location.origin}/integrations`;
         }, 1000);
      } catch (err) {
         console.log('ERROR IN LINKEDIN SENTIMENT', err);
      } finally {
         dispatch(setIsConnecting(null));
         dispatch(closeModal());
         handleMetaSuccess();
      }
   };
   const handleMetaSuccess = () => {
      toast({
         title: 'Success',
         description: 'Pages selected successfully.',
         status: 'success',
         duration: 5000,
         isClosable: true,
      });
   };

   useEffect(() => {
      getUser({ token: accessToken });
   }, []);
   const handlePages = (data: LinkedinPageDetails[]) => {
      const pages = data.map((page) => {
         return {
            name: page.localizedName,
            id: page.id.toString(),
            page: true,
         };
      });
      userDetails &&
         pages.push({
            id: userDetails.id,
            name: userDetails.localizedFirstName,
            page: false,
         });
      setlinkedinPages(pages);
   };

   const handleUser = (user: { linkedinUser: LinkedinProfile }) => {
      const { id, localizedFirstName } = user.linkedinUser;
      setUserDetails({
         id,
         localizedFirstName,
      });

      // call page api
      getPages({ token: accessToken });
   };

   const {
      mutate: getPages,
      errorMessage: errorPageMessage,
      isPending,
   } = useApiMutation({
      queryKey: [SocialWatchQueries.linkedinPages],
      mutationFn: socialWatchEndpoints.getLinkedinPages,
      onSuccessHandler: handlePages,
   });

   const {
      mutate: getUser,
      errorMessage: errorUserMessage,
      isPending: isUserPending,
   } = useApiMutation({
      queryKey: [SocialWatchQueries.linkedinUser],
      mutationFn: socialWatchEndpoints.getLinkedinUserDetails,
      onSuccessHandler: handleUser,
   });

   if (errorUserMessage || errorPageMessage) {
      toast({
         title: 'Error',
         description: errorUserMessage || errorPageMessage,
         status: 'error',
         duration: 5000,
         isClosable: true,
      });
   }
   return (
      <ModalWrapper
         heading={'Select Linkedin Page'}
         parentClassName='kpi-more-details'
         closeOnEsc={false}
         closeOnOverlayClick={false}
         noCloseBtn={true}
      >
         <Flex justifyContent={'space-between'} direction={'column'} gap={4}>
            {isUserPending || isPending ? (
               <Flex height={'24px'} gap={3}>
                  <Skeleton flex={1} />
                  <Skeleton flex={1} />
                  <Skeleton flex={1} />
               </Flex>
            ) : (
               <CheckboxGroup onChange={handlePageSelect} colorScheme='green'>
                  <Flex gap={3}>
                     {linkedinPages.map((page) => (
                        <Checkbox key={page.id} value={page.id}>
                           {' '}
                           {page.name}
                           {`${!page.page ? '  (Self)' : ''}`}
                        </Checkbox>
                     ))}
                  </Flex>
               </CheckboxGroup>
            )}
            <Flex justifyContent={'flex-end'}>
               <Button colorScheme='blue' onClick={() => void handleSave()}>
                  Save
               </Button>
            </Flex>
         </Flex>
      </ModalWrapper>
   );
}

export default LinkedinPageSelect;
