import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
   CampaignDetails,
   AdsetDetails,
   AdsetData,
   ChatLevels,
} from '../../api/service/agentic-workflow/meta-ads-manager';
import { TableSection } from '../../pages/marco/utils/meta-ads-manager-auto/extractTargetTable';
import { clearAnimationCache } from '../../pages/marco/utils/meta-ads-manager-auto/type-write-text';

export interface AutomationStep {
   step: number;
   name: string;
   status: 'pending' | 'in-progress' | 'completed' | 'error';
   chatLevel: ChatLevels;
}

export interface StreamingMessage {
   id: string;
   type:
      | 'campaign'
      | 'adset-analysis'
      | 'adset-creation'
      | 'ad-creative'
      | 'ad';
   title: string;
   content: string;
   isStreaming: boolean;
   timestamp: string;
   data?: unknown;
   tableData?: TableSection[];
   imageUrl?: string;
   isComplete: boolean;
}

export interface MetaAdsAutoAgentState {
   sessionId: string;
   userPrompt: string;
   isCreating: boolean;
   currentStep: number;
   streamingMessages: StreamingMessage[];
   steps: AutomationStep[];
   stepStatuses: AutomationStep[];
   campaignDetails: CampaignDetails | null;
   adsetDetails: AdsetDetails | null;
   adsetData: AdsetData | null;
   adCreativeId: string;
   adId: string;
   summaryLoading: boolean;
   summaryContent: string | null;
}

const initialSteps: AutomationStep[] = [
   {
      step: 1,
      name: 'Campaign Creation',
      status: 'pending',
      chatLevel: 'campaign',
   },
   {
      step: 2,
      name: 'Audience Analysis',
      status: 'pending',
      chatLevel: 'adset-initial',
   },
   {
      step: 3,
      name: 'Adset Creation',
      status: 'pending',
      chatLevel: 'adset-final',
   },
   {
      step: 4,
      name: 'Ad Creative Generation',
      status: 'pending',
      chatLevel: 'ad-creative',
   },
   { step: 5, name: 'Ad Generation', status: 'pending', chatLevel: 'ad' },
];

const initialState: MetaAdsAutoAgentState = {
   sessionId: new Date().getTime().toString(),
   userPrompt: '',
   isCreating: false,
   currentStep: 0,
   streamingMessages: [],
   steps: initialSteps,
   stepStatuses: initialSteps,
   campaignDetails: null,
   adsetDetails: null,
   adsetData: null,
   adCreativeId: '',
   adId: '',
   summaryLoading: false,
   summaryContent: null,
};

const metaAdsAutoAgentSlice = createSlice({
   name: 'metaAdsAutoAgent',
   initialState,
   reducers: {
      startNewAutoAgentChat: (state) => {
         // Clear animation cache from localStorage
         clearAnimationCache();

         state.sessionId = new Date().getTime().toString();
         state.userPrompt = '';
         state.isCreating = false;
         state.currentStep = 0;
         state.streamingMessages = [];
         state.stepStatuses = initialSteps;
         state.campaignDetails = null;
         state.adsetDetails = null;
         state.adsetData = null;
         state.adCreativeId = '';
         state.adId = '';
         state.summaryLoading = false;
         state.summaryContent = null;
      },
      setUserPrompt: (state, action: PayloadAction<string>) => {
         state.userPrompt = action.payload;
      },
      setIsCreating: (state, action: PayloadAction<boolean>) => {
         state.isCreating = action.payload;
      },
      setCurrentStep: (state, action: PayloadAction<number>) => {
         state.currentStep = action.payload;
      },
      addStreamingMessage: (state, action: PayloadAction<StreamingMessage>) => {
         state.streamingMessages.push(action.payload);
      },
      updateStreamingMessage: (
         state,
         action: PayloadAction<{
            id: string;
            updates: Partial<StreamingMessage>;
         }>,
      ) => {
         const { id, updates } = action.payload;
         const messageIndex = state.streamingMessages.findIndex(
            (msg) => msg.id === id,
         );
         if (messageIndex !== -1) {
            state.streamingMessages[messageIndex] = {
               ...state.streamingMessages[messageIndex],
               ...updates,
            };
         }
      },
      setStreamingMessages: (
         state,
         action: PayloadAction<StreamingMessage[]>,
      ) => {
         state.streamingMessages = action.payload;
      },
      updateStepStatus: (
         state,
         action: PayloadAction<{
            stepIndex: number;
            status: 'pending' | 'in-progress' | 'completed' | 'error';
         }>,
      ) => {
         const { stepIndex, status } = action.payload;
         state.stepStatuses = state.stepStatuses.map((step, index) =>
            index === stepIndex ? { ...step, status } : step,
         );
      },
      setStepStatuses: (state, action: PayloadAction<AutomationStep[]>) => {
         state.stepStatuses = action.payload;
      },
      setCampaignDetails: (
         state,
         action: PayloadAction<CampaignDetails | null>,
      ) => {
         state.campaignDetails = action.payload;
      },
      setAdsetDetails: (state, action: PayloadAction<AdsetDetails | null>) => {
         state.adsetDetails = action.payload;
      },
      setAdsetData: (state, action: PayloadAction<AdsetData | null>) => {
         state.adsetData = action.payload;
      },
      setAdCreativeId: (state, action: PayloadAction<string>) => {
         state.adCreativeId = action.payload;
      },
      setAdId: (state, action: PayloadAction<string>) => {
         state.adId = action.payload;
      },
      setSummaryLoading: (state, action: PayloadAction<boolean>) => {
         state.summaryLoading = action.payload;
      },
      setSummaryContent: (state, action: PayloadAction<string | null>) => {
         state.summaryContent = action.payload;
      },
      setCurrentSessionID: (state, action: PayloadAction<string>) => {
         state.sessionId = action.payload;
      },
   },
});

export const {
   startNewAutoAgentChat,
   setUserPrompt,
   setIsCreating,
   setCurrentStep,
   addStreamingMessage,
   updateStreamingMessage,
   setStreamingMessages,
   updateStepStatus,
   setStepStatuses,
   setCampaignDetails,
   setAdsetDetails,
   setAdsetData,
   setAdCreativeId,
   setAdId,
   setSummaryLoading,
   setSummaryContent,
   setCurrentSessionID,
} = metaAdsAutoAgentSlice.actions;

export default metaAdsAutoAgentSlice.reducer;
