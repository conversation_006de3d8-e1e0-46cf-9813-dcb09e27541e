import ModalWrapper from './modal-wrapper';
import { useToast } from '@chakra-ui/react';
import { useDispatch } from 'react-redux';
import { setLoading } from '../../store/reducer/configReducer';
import { decode, encode } from '../../api/service/social-watch';
import {
   GetTextImagePayload,
   GetTextImageResponse,
} from '../../api/service/contentideation/interface';
import { getContent } from '../../api/service/contentideation';
import { setPersonalisedData } from '../../store/reducer/content-ideation-reducer';
import { closeModal } from '../../store/reducer/modal-reducer';
import { contentIdeationStrings } from '../../utils/strings/content-ideation';
import { setUploadToSocialMedia } from '../../store/reducer/user-details-reducer';

import { useState } from 'react';
import { Spinner } from '@chakra-ui/react';
import { Keys, LocalStorageService } from '../../utils/local-storage';
import { AuthUser } from '../../types/auth';
import './upload-image-modal.scss';

function UploadImageModal() {
   const dispatch = useDispatch();
   const toast = useToast();

   const userDetails = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   );
   const [loader, setLoader] = useState<boolean>(false);

   const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = event.target.files;
      if (files && files.length > 0) {
         void handleUpload(files[0]);
      }
   };

   const handleUpload = async (selectedFile: File) => {
      if (!selectedFile) {
         showToast(
            'No File Selected',
            'Please select an image file to upload.',
            'warning',
         );
         return;
      }
      setLoader(true);
      dispatch(setLoading(true));
      try {
         const response = await encodeImage(selectedFile);
         if (response) {
            const encodedUri = response.uri;
            const decodedUri = await decodeImage({ image_url: encodedUri });

            if (decodedUri) {
               const isSuccess = await fetchImageIdeas(decodedUri.uri);
               if (isSuccess) {
                  showToast('Success', 'Image Uploaded Succesfully', 'success');

                  dispatch(
                     setUploadToSocialMedia({
                        file: selectedFile,
                        encodedUri: encodedUri,
                        decodedUri: decodedUri.uri,
                     }),
                  );
                  setLoader(false);
               }
            }
         } else {
            showToast('Error', 'Failed to upload image.', 'error');
         }
      } catch (error) {
         console.error('Error uploading image:', error);
         showToast(
            'Error',
            'An error occurred while uploading image.',
            'error',
         );
      } finally {
         dispatch(setLoading(false));
      }
   };

   const encodeImage = (selectedFile: File): Promise<{ uri: string }> => {
      return new Promise<{ uri: string }>((resolve, reject) => {
         const reader = new FileReader();
         reader.readAsDataURL(selectedFile);
         reader.onloadend = async () => {
            const base64String = (reader.result as string).replace(
               /^data:.+;base64,/,
               '',
            );
            console.log('BASE 64', base64String);
            const formData = new FormData();
            formData.append('media', selectedFile);
            try {
               const response = await encode(formData);
               if (response) resolve(response);
               return response?.uri;
            } catch (error) {
               return error;
            }
         };
         reader.onerror = (error) => reject(error);
      });
   };

   const decodeImage = async (payload: { image_url: string }) => {
      try {
         return await decode(payload);
      } catch (error) {
         console.error('Error decoding image:', error);
         showToast('Error', 'An error occurred while decoding image.', 'error');
         return null;
      }
   };
   const fetchImageIdeas = async (decodedUri: string): Promise<boolean> => {
      const payload: GetTextImagePayload = {
         client_id: userDetails!.client_id,
         data: 'Image Upload',
         image_url: decodedUri,
         context: contentIdeationStrings.ImageTextPrompt,
         baseEncode: false,
      };
      try {
         const response = (await getContent(payload)) as GetTextImageResponse;
         if (response.data) {
            dispatch(setPersonalisedData(response.data));
            dispatch(closeModal());
            return true;
         }
         return false;
      } catch (error) {
         showToast(
            'Error',
            'An error occurred while fetching image ideas.',
            'error',
         );
         return false;
      }
   };
   const showToast = (
      title: string,
      description: string,
      status: 'success' | 'error' | 'warning',
   ) => {
      toast({
         title,
         description,
         status,
         duration: 5000,
         isClosable: true,
      });
   };

   return (
      <ModalWrapper heading='Upload Image' overlayBgcolor='#2424241c'>
         <div className='upload-image-modal'>
            <div className='upload-description'>
               {contentIdeationStrings.Imageformat}
            </div>
            <div className='file-input'>
               {loader ? (
                  <div className='loader'>
                     <Spinner />
                     Uploading..
                  </div>
               ) : (
                  <>
                     <label className='upload-btn'>
                        <input
                           type='file'
                           accept='.jpg,.jpeg,.png'
                           onChange={handleFileChange}
                        />
                        Upload From System
                     </label>
                  </>
               )}
            </div>
         </div>
      </ModalWrapper>
   );
}

export default UploadImageModal;
