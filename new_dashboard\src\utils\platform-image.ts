import facebookImage from '../assets/icons/pulse/meta.png';
import instagramImage from '../assets/icons/pulse/instagram.png';
import twitterImage from '../assets/icons/pulse/twitter.png';
import youtubeImage from '../assets/icons/pulse/youtube.png';
import googleImage from '../assets/icons/pulse/google.png';
import whatsappImage from '../assets/icons/pulse/whatsapp.png';
import linkedinImage from '../assets/icons/pulse/linkedin.png';

const platformImages: { [key: string]: string } = {
   facebook: facebookImage,
   instagram: instagramImage,
   twitter: twitterImage,
   youtube: youtubeImage,
   google: googleImage,
   whatsapp: whatsappImage,
   linkedin: linkedinImage,
};

const getPlatformImage = (text: string): string | undefined => {
   for (const platform in platformImages) {
      if (text.toLowerCase().includes(platform)) {
         return platformImages[platform];
      }
   }
   return undefined;
};

export default getPlatformImage;
