import { useState, useCallback, useEffect } from 'react';
import {
   Calendar,
   momentLocalizer,
   Event as BigCalendarEvent,
} from 'react-big-calendar';
import moment from 'moment';
import { Box, Card, Text, useToast } from '@chakra-ui/react';

import { deleteContentCalendar } from '../../api/service/social-watch/index';
import Popup from '../calenderDialog/calender-dialog';
import './calenderview.scss';
import {
   dialogMessage,
   toastMessage,
} from '../../utils/strings/content-manager';
import EditModal from './edit-modal';
import DynamicIcons from '../dynamicIcon/dynamic-icon';

const localizer = momentLocalizer(moment);
import 'react-big-calendar/lib/css/react-big-calendar.css';
import Swal from 'sweetalert2';
import socialWatchEndpoints from '../../api/service/social-watch/apis';
import { CalendarItem } from '../../types/social-watch';
import { Keys, LocalStorageService } from '../../utils/local-storage';
import { AuthUser } from '../../types/auth';
import useIntegrationConnectionDetails from '../../hooks/use-integration-connection-details';

export const formatDate = (date: string, time: string) => {
   return moment(date + ' ' + time, 'DD/MM/YYYY HH:mm').toDate();
};

const CalendarView: React.FC = () => {
   const [calenderData, setCalenderData] = useState<CalendarItem[]>([]);
   const [dialogData, setDialogData] = useState<CalendarItem | null>(null);
   const [openPopup, setOpenPopup] = useState<boolean>(false);
   const [onEdit, setOnEdit] = useState<boolean>(false);
   const [popupPosition, setPopupPosition] = useState<{
      x: number;
      y: number;
   } | null>(null);

   const { mediaTypes } = useIntegrationConnectionDetails();
   const toast = useToast();

   const clientId = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   )?.client_id;

   const fetchAndProcessCalendarData = useCallback(async () => {
      if (clientId) {
         const payload = {
            client_id: clientId,
            social_media_types: mediaTypes,
         };
         try {
            const {
               data: { contentCalendarData },
            } = await socialWatchEndpoints.getContentCalendarData(payload);

            const modifiedContentCalendarData = contentCalendarData.map(
               (item) => {
                  return {
                     uuid: item.uuid,
                     title: item.post_data,
                     start: formatDate(item.date, item.time),
                     end: formatDate(item.date, item.time),
                     social_media_type: item.social_media_type,
                     media: item.media,
                     client_id: item.client_id,
                     is_schedule: item.is_schedule,
                     _id: item._id,
                  };
               },
            );
            setCalenderData(modifiedContentCalendarData);
         } catch (err: unknown) {
            const error = err as { response: { data: { message: string } } };
            console.log(error);
            toast({
               title: 'Data not found',
               description: 'Please schedule a post',
               status: 'error',
               duration: 3000,
               isClosable: true,
            });
         }
      }
   }, [clientId]);

   useEffect(() => {
      void fetchAndProcessCalendarData();
   }, [fetchAndProcessCalendarData]);

   const modifiedData = calenderData.map((item) => {
      return {
         ...item,
         title: (
            <Card key={item.uuid} className='card' data-event-id={item.uuid}>
               <Box className='card__top'>
                  <Box className='card__top__time'>
                     <DynamicIcons item={item} />
                  </Box>
               </Box>
               <Box className='card__bottom'>
                  <Text>
                     {item.title?.length > 100
                        ? item.title.substring(0, 100) + '...'
                        : item.title}
                  </Text>
               </Box>
            </Card>
         ),
      };
   });

   const handleEventClick = (event: CalendarItem): void => {
      const clickedEvent = calenderData.find(
         (item) => item.uuid === event.uuid,
      );
      if (clickedEvent) {
         const eventElement = document.querySelector(
            `[data-event-id='${event.uuid}']`,
         );

         if (eventElement) {
            const rect = eventElement.getBoundingClientRect();
            setPopupPosition({ x: rect.left, y: rect.bottom });
         }

         setDialogData(clickedEvent);
         setOpenPopup(true);
      }
   };

   const handleDelete = async (): Promise<void> => {
      setOpenPopup(false);
      try {
         const result = await Swal.fire({
            title: dialogMessage.delete.title,
            text: dialogMessage.delete.description,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: dialogMessage.delete.buttonMessage,
         });
         if (result.isConfirmed) {
            const uuid = dialogData?.uuid;
            if (uuid) {
               await deleteContentCalendar({
                  uuid: uuid,
               });
            }

            await Swal.fire({
               title: dialogMessage.deleteSuccess.title,
               text: dialogMessage.deleteSuccess.description,
               icon: 'success',
            });
            await fetchAndProcessCalendarData();
         }
      } catch (error) {
         await Swal.fire({
            title: toastMessage.error.title,
            text: toastMessage.error.description,
            icon: 'error',
         });
      }
   };

   const handleEdit = () => {
      if (!dialogData) return;
      setOpenPopup(false);
      setOnEdit(true);
   };

   return (
      <Box className='calenderview'>
         <EditModal
            onEdit={onEdit}
            setOnEdit={setOnEdit}
            EditContent={dialogData}
            handleUpdate={() => void fetchAndProcessCalendarData()}
         />
         <Popup
            openPopup={openPopup}
            setOpenPopup={setOpenPopup}
            setDialogData={setDialogData}
            dialogData={dialogData}
            handleDelete={() => void handleDelete()}
            handleEdit={() => void handleEdit()}
            position={popupPosition}
         />
         <Calendar<CalendarItem>
            localizer={localizer}
            events={
               modifiedData as Partial<BigCalendarEvent>[] as CalendarItem[]
            }
            startAccessor='start'
            endAccessor='end'
            style={{ height: 500 }}
            onSelectEvent={handleEventClick}
         />
      </Box>
   );
};

export default CalendarView;
