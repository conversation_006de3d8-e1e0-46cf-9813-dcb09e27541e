export const CAMPAIGN_PROMPTS = [
   'Create a Meta Ads campaign named BoostCampaign, targeting the outcome Sales.',
   'Create a Meta Ads campaign named SpringBlitz, targeting the outcome Leads.',
   'Set up an ad campaign called BrandSales2025 with a focus on the outcome Sales.',
   'Create a new ad campaign called SummerPromo with the objective outcome Traffic.',
];

export const ADSET_PROMPTS = [
   'Create an ad set called Kids-Hoodies.',
   'Launch a new ad set Holiday Sale Deals.',
];

export const PROMPTS = {
   campaign: CAMPAIGN_PROMPTS,
   'adset-initial': ADSET_PROMPTS,
};

export const CAMPAIGN_LOADER_MESSAGES = [
   'Analyzing historical campaigns...',
   'Identifying top-performing campaign objectives...',
   'Evaluating daily budget effectiveness...',
   'Reviewing conversion trends across time...',
   'Fetching product details and URL metadata...',
   'Mapping objective to optimization goals...',
   'Selecting appropriate billing events...',
   'Setting campaign delivery strategy...',
   'Checking pixel and tracking readiness...',
   'Aligning campaign with business goals...',
   'Campaign setup is almost ready!',
];

export const ADSET_INITIAL_LOADER_MESSAGES = [
   'Analyzing historical adsets...',
   'Evaluating audience demographics (age & gender)...',
   'Identifying high-converting placements...',
   'Filtering top-performing interest categories...',
   'Reviewing audience behavior patterns...',
   'Assessing geo-level (location) performance...',
   'Prioritizing segments based on ROAS and purchase data...',
   'Curating optimal targeting configuration...',
   'Concluding adset-level targeting analysis...',
];

export const ADSET_FINAL_LOADER_MESSAGES = [
   'Finalizing audience configuration...',
   'Applying targeting preferences...',
   'Setting up daily budget and optimization goal...',
   'Linking adset to campaign objective...',
   'Creating adset on Meta platform...',
   'Verifying adset creation and syncing with system...',
];

export const AD_CREATIVE_LOADER_MESSAGES = [
   'Analyzing product details and landing page...',
   'Extracting product features and benefits...',
   'Understanding audience preferences...',
   'Reviewing historical ad creatives...',
   'Detecting top-performing creative patterns...',
   'Identifying emotional triggers and tone...',
   'Crafting ad headline variations...',
   'Generating persuasive primary text...',
   'Optimizing for platform-specific formats...',
   'Designing visual creative elements...',
   'Aligning creatives with campaign objectives...',
   'Finalizing and assembling ad assets...',
   'Uploading creatives to Meta platform...',
   'Almost there... perfecting your ad creative!',
];

export const AD_LOADER_MESSAGES = ['Generating ad...'];

export const LOADER_MESSAGES = {
   campaign: CAMPAIGN_LOADER_MESSAGES,
   'adset-initial': ADSET_INITIAL_LOADER_MESSAGES,
   'adset-final': ADSET_FINAL_LOADER_MESSAGES,
   'ad-creative': AD_CREATIVE_LOADER_MESSAGES,
   ad: AD_LOADER_MESSAGES,
};

export const CAMPAIGN_STRINGS = {
   camp_details: 'Campaign Details',
   camp_name: 'Campaign Name',
   camp_objective: 'Campaign Objective',
   camp_id: 'Campaign ID',
   prod_url: 'Product URL',
};

export const ADCREATIVE_ANALYSIS_STRINGS = {
   ad_creat_analysis: 'Ad-creative Analysis',
};
