import React, { useState, useEffect, FC, useRef } from 'react';
import './chatbox.scss';
import MySelect from '../customisedselect/select';
import Skeletonloader from '../skeletonloader/skeleton-loader';
import TimeFrameSelection from '../timeframe/time-frame';
import marco from '../../assets/icons/marco.svg';
import thumsup from '../../assets/icons/thumsup-icon.svg';
import thumsdown from '../../assets/icons/thumsdown-icon.svg';
import copy from '../../assets/icons/copy-icon.svg';
// import regenerate from '../../assets/icons/regenrate-icon.svg';
import marcoResponse from '../../assets/icons/marco-response-icon.svg';
import askIcon from '../../assets/icons/ask-icon.svg';
import thumsupclick from '../../assets/icons/thumsupclick-icon.svg';
import thumsdownclick from '../../assets/icons/thumsdown-click.svg';
import askIconDisabled from '../../assets/icons/ask-icon-disabled.svg';
import { LocalStorageService, Keys } from '../../utils/local-storage';
import { formatDate } from '../../utils/date-formate';
import {
   Tooltip,
   Spinner,
   useToast,
   Box,
   Flex,
   Stack,
   Text,
   Image,
   Button,
   Input,
   // useColorMode,
   useColorModeValue,
   useColorMode,
} from '@chakra-ui/react';

import {
   Payload,
   QnABoxProps,
   Question,
   UserDetails,
   ChatAnswers,
   // ChartData,
   PerformanceChartData,
   userFeedback,
   ModeDescription,
} from './interface';
import { chatWithMarco, updateUserFeedback } from '../../api/service/marco';
import SummarizeMenu from '../summarizemenu/summarize-menu';
import { modeOptions, TEXTS } from '../../utils/strings/chatbox-strings';
import TimeFrameChartMenu from '../timeframechart/timeframe-chart';
import { v4 as uuidv4 } from 'uuid';
import MetricMenuSelection from '../metricmenu/metric-menu';
import ObjectiveMenuSelection from '../objectivemenu/objective-menu';
import ChartTypeMenu from '../charttypemenu/chart-type-menu';
import PerformanceChart from '../chart/performancechart/performance-chart';
import { useApiQuery } from '../../hooks/react-query-hooks';
import settingsService from '../../api/service/settings/index';
import { SettingsQueryKeys } from '../../pages/dashboard/utils/query-keys';
import AverageKPIValue from '../chart/performancechart/average-value';
import { useAppSelector } from '../../store/store';

const isChannelError = (
   detail:
      | string
      | { metric?: string[]; objective?: string[]; channel?: string[] },
): detail is { channel: string[] } => {
   return typeof detail === 'object' && detail !== null && 'channel' in detail;
};
const isTopMatchError = (
   detail:
      | string
      | {
           metric?: string[];
           objective?: string[];
           channel?: string[];
           top_match?: string[];
        },
): detail is { top_match: string[] } => {
   return (
      typeof detail === 'object' && detail !== null && 'top_match' in detail
   );
};
const sampleQuestions: Question[] = [
   {
      question: 'Tell me top 5 campaigns of last 90 days',
      mode: 'Performance Copilot',
   },
   {
      question: 'Tell me the best adset in last 100 days',
      mode: 'Performance Copilot',
   },
   {
      question: 'Give me the usage of all devices of last 30 days?',
      mode: 'Console',
   },
   {
      question: 'Tell me traffic from google',
      mode: 'Console',
   },
   {
      question: 'Tell me the best age range of last 90 days',
      mode: 'Performance Copilot',
   },
   {
      question: 'Traffic from google vs facebook over last 30 days',
      mode: 'Console',
   },
];
const modeDescription: ModeDescription = {
   Console: {
      description:
         'You are in Web Analytics mode. MARCO offers valuable web insights, though occasional variations may occur as it continues to learn and adapt to your unique needs.',
   },
   'Performance Copilot': {
      description:
         'You are in Performance mode. MARCO offers valuable ads insights, though occasional variations may occur as it continues to learn and adapt to your unique needs.',
   },
};

const QnABox: FC<QnABoxProps> = ({
   history,
   onSearch,
   sessionId,
   setShowHistoryBar,
   newChatTrigger,
   selectedSession,
   deleteSession,
   onNewChat,
   marcoCustomSelect,
   id,
}) => {
   const [userInput, setUserInput] = useState<string>('');
   const { connectionDetails } = useAppSelector((state) => state.media);
   const [fetchedAnswers, setFetchedAnswers] = useState<ChatAnswers[]>([]);
   const token = LocalStorageService.getItem<string>(Keys.Token);
   const userDetails =
      LocalStorageService.getItem<UserDetails>(Keys.FlableUserDetails) ||
      ({} as UserDetails);
   const [showSampleQuestions, setShowSampleQuestions] =
      useState<boolean>(true);
   const [selectedMode, setSelectedMode] = useState<string>('Console');
   const [inputDisabled, setInputDisabled] = useState<boolean>(false);
   const [askButtonDisabled, setAskButtonDisabled] = useState<boolean>(false);
   const askButtonRef = useRef<HTMLButtonElement>(null);
   const [userInputForTimeFrame, setUserInputForTimeFrame] =
      useState<string>('');
   const messagesEndRef = useRef<null | HTMLDivElement>(null);
   const scrollToBottom = () => {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
   };

   const [metricData, setmetricData] = useState<string[]>([]);
   const [objectiveData, setobjectiveData] = useState<string[]>([]);
   const [channelData, setchannelData] = useState<string[]>([]);

   const toast = useToast();
   const previousFetchedAnswersRef = useRef(fetchedAnswers);

   const { colorMode } = useColorMode();
   const bgColor = useColorModeValue('white', 'var(--side-navbar)');
   // const textColor = useColorModeValue('black', 'white');
   const chatBubbleBg = useColorModeValue('#437eeb', '#2a4a8d');
   const responseBg = useColorModeValue('#f6f6f6', 'gray.700');
   const warningBg = useColorModeValue('#FFF6DC', '#2D2A12');
   const warningBorder = useColorModeValue('#FBBC04', '#785E04');
   const inputBg = useColorModeValue('white', 'gray.700');
   const inputDisabledBg = useColorModeValue('gray.200', 'gray.600');
   const buttonBg = useColorModeValue('white', 'var(--controls)');
   const buttonDisabledBg = useColorModeValue('gray.200', 'gray.600');
   useEffect(() => {
      modeOptions.forEach((opt) => {
         if (opt.value == 'Console') {
            opt.isActive = !!connectionDetails.find(
               (x) => x.channel_name == 'flable_pixel' && x.is_active,
            );
            if (!opt.isActive) setSelectedMode('Performance Copilot');
         } else if (opt.value == 'Performance Copilot') {
            opt.isActive = !!connectionDetails.find(
               (x) =>
                  ['googleads', 'facebookads'].includes(x.channel_name) &&
                  x.is_active,
            );
         }
      });
   }, [connectionDetails]);
   useEffect(() => {
      const previousFetchedAnswers = previousFetchedAnswersRef.current;
      const isNewItemAdded =
         previousFetchedAnswers.length < fetchedAnswers.length;
      const isItemUpdated = fetchedAnswers.some((answer, index) => {
         const prevAnswer = previousFetchedAnswers[index];
         return (
            prevAnswer &&
            (prevAnswer.question !== answer.question ||
               prevAnswer.answer.text !== answer.answer.text ||
               JSON.stringify(prevAnswer.answer.image) !==
                  JSON.stringify(answer.answer.image))
         );
      });

      if (isNewItemAdded || isItemUpdated) {
         scrollToBottom();
      }

      previousFetchedAnswersRef.current = fetchedAnswers;
   }, [fetchedAnswers]);

   const { data } = useApiQuery({
      queryKey: [SettingsQueryKeys.timezone],
      queryFn: () =>
         settingsService.fetchGeneralSettings({
            client_id: userDetails.client_id,
            email_address: userDetails.email,
         }),
      enabled: true,
   });

   useEffect(() => {
      if (selectedSession === deleteSession) {
         setFetchedAnswers([]);
         setShowSampleQuestions(true);
      } else setShowSampleQuestions(false);
      if (selectedSession) {
         const sessionHistory = history.find(
            (item) => item.sessionId === selectedSession,
         );

         if (sessionHistory) {
            const { createdAt } = sessionHistory;
            const { updatedAt } = sessionHistory;
            const fetchedAnswersWithLoading = sessionHistory.chat.map(
               (item) => ({
                  id: item.id,
                  question: item.userask,
                  answer: {
                     text: item.reply.text,
                     image: item.reply.image,
                  },
                  loading: false,
                  thumbsUpClicked: item.feedback === 'like',
                  thumbsDownClicked: item.feedback === 'dislike',
                  showTimeFrameSelection: false,
                  timeFram: null,
                  createdAt: formatDate(
                     item.created_at ? item.created_at : createdAt,
                     data?.timezone_name,
                  ),
                  updatedAt: formatDate(
                     item.updated_at ? item.updated_at : updatedAt,
                     data?.timezone_name,
                  ),
                  summary: false,
                  timeFrameChart: item.reply.timeFrameChart,
                  showMetricMenu: false,
                  showTopMatch: false,
                  showObjectiveMenu: false,
                  metric: null,
                  objective: null,
               }),
            );
            setSelectedMode(sessionHistory.chat[0].reply.selectedMode);
            setFetchedAnswers(fetchedAnswersWithLoading);
         }
      }
   }, [selectedSession, deleteSession]);

   const handleCopy = (response: string | undefined) => {
      if (!response) return;
      navigator.clipboard
         .writeText(response)
         .then(() => {
            toast({
               title: 'Response copied to clipboard',
               status: 'success',
               duration: 3000,
               isClosable: true,
            });
            console.log('Response copied to clipboard:', response);
         })
         .catch((error) => {
            console.error('Error copying response to clipboard:', error);
         });
   };

   const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setUserInput(e.target.value);
      setUserInputForTimeFrame(e.target.value);
   };

   const handleThumbsUp = (
      index: number,
      id: string,
      user_feedback: string,
   ) => {
      let sessionToUse = selectedSession || sessionId;
      if (
         (!selectedSession && newChatTrigger) ||
         (selectedSession && newChatTrigger)
      ) {
         sessionToUse = sessionId;
      }
      setFetchedAnswers((prevAnswers) => {
         const updatedAnswers = [...prevAnswers];
         updatedAnswers[index].thumbsUpClicked = true;
         updatedAnswers[index].thumbsDownClicked = false;
         return updatedAnswers;
      });
      const payload: userFeedback = {
         client_id: userDetails.client_id,
         chat_id: id,
         session_id: sessionToUse,
         user_feedback: user_feedback,
      };
      void updateUserFeedback(payload);
   };

   const handleThumbsDown = (
      index: number,
      id: string,
      user_feedback: string,
   ) => {
      let sessionToUse = selectedSession || sessionId;
      if (
         (!selectedSession && newChatTrigger) ||
         (selectedSession && newChatTrigger)
      ) {
         sessionToUse = sessionId;
      }
      setFetchedAnswers((prevAnswers) => {
         const updatedAnswers = [...prevAnswers];
         updatedAnswers[index].thumbsDownClicked = true;
         updatedAnswers[index].thumbsUpClicked = false;
         return updatedAnswers;
      });
      const payload: userFeedback = {
         client_id: userDetails.client_id,
         chat_id: id,
         session_id: sessionToUse,
         user_feedback: user_feedback,
      };
      void updateUserFeedback(payload);
   };
   // const handleRegenerate = () => {};
   const handleSummarizeWrapper = (id: string) => {
      handleSummarize(id).catch((error) => console.error(error));
   };
   const handleSummarize = async (id: string) => {
      let sessionToUse = selectedSession || sessionId;
      if (
         (!selectedSession && newChatTrigger) ||
         (selectedSession && newChatTrigger)
      ) {
         sessionToUse = sessionId;
      }

      const lastAsked = fetchedAnswers.find((answer) => answer.id === id);
      const idForPresentQuestion = uuidv4();
      setFetchedAnswers((prevAnswers) => {
         if (lastAsked && lastAsked.question == 'Summarize') return prevAnswers;
         return [
            ...prevAnswers,
            {
               id: idForPresentQuestion,
               question: `Summary of "${lastAsked?.question}"`,
               answer: { text: '', image: {} },
               loading: true,
               thumbsUpClicked: false,
               thumbsDownClicked: false,
               showTopMatch: false,
               showTimeFrameSelection: false,
               createdAt: formatDate(
                  new Date().toISOString(),
                  data?.timezone_name,
               ),
               updatedAt: formatDate(
                  new Date().toISOString(),
                  data?.timezone_name,
               ),
               summary: true,
               timeFram: null,
               showMetricMenu: false,
               showObjectiveMenu: false,
               objective: lastAsked?.objective || null,
               metric: lastAsked?.metric || null,
            },
         ];
      });

      if (lastAsked && lastAsked.question == 'Summarize') return;
      const payload: Payload = {
         client_id: userDetails.client_id,
         user: userDetails.email,
         mode: selectedMode,
         text: lastAsked ? lastAsked.question : '',
         bearer_token: token,
         time_period: (lastAsked ? lastAsked.timeFram : '') || null,
         summary: true,
         metric: lastAsked?.metric || null,
         objective: lastAsked?.objective || null,
         dateFrequency: null,
         sessionId: sessionToUse,
      };

      if (lastAsked?.channel) payload.channel = lastAsked.channel;
      const res = await chatWithMarco(payload);
      if (res.success) {
         let answer: string | { text: string | undefined; image: object };
         let image = {};

         if (Array.isArray(res.data)) {
            answer = res.data[0];
            image = res.data[1];
         } else {
            answer = res.data as string;
         }

         setFetchedAnswers((prevAnswers: ChatAnswers[]): ChatAnswers[] => {
            const updatedAnswers = prevAnswers.map((prevAnswer) => {
               if (prevAnswer.id == idForPresentQuestion) {
                  return {
                     ...prevAnswer,
                     answer: { text: answer, image },
                     loading: false,
                  };
               }
               return prevAnswer;
            });
            return updatedAnswers;
         });
         onSearch(sessionToUse, {
            id: idForPresentQuestion,
            userask: `Summary of "${lastAsked?.question}"`,
            reply: { text: answer, image, selectedMode },
         });
      } else {
         console.error('Error fetching answer:', res.data);
         if (res.data && res.status === 422) {
            setFetchedAnswers((prevAnswers) => {
               const updatedAnswers = prevAnswers.map((prevAnswer) => {
                  if (prevAnswer.id === idForPresentQuestion) {
                     return {
                        ...prevAnswer,
                        showTimeFrameSelection: true,
                     };
                  }
                  return prevAnswer;
               });
               return updatedAnswers;
            });
         } else {
            const answer = TEXTS.errorAnswer;
            const image = {};
            setFetchedAnswers((prevAnswers) => {
               const updatedAnswers = prevAnswers.map((prevAnswer) => {
                  if (prevAnswer.id === idForPresentQuestion) {
                     return {
                        ...prevAnswer,
                        answer: { text: answer, image },
                        loading: false,
                     };
                  }
                  return prevAnswer;
               });
               return updatedAnswers;
            });
            onSearch(sessionToUse, {
               id: idForPresentQuestion,
               userask: `Summary of "${lastAsked?.question}"`,
               reply: { text: answer, image, selectedMode },
            });
         }
      }
   };

   const handleAskClick = () => {
      if (!userInput.length) return;
      handleAskResponse().then(console.log).catch(console.log);
   };
   const handleAskResponse = async () => {
      setShowHistoryBar(true);
      setShowSampleQuestions(false);

      let sessionToUse = selectedSession || sessionId;
      if (
         (!selectedSession && newChatTrigger) ||
         (selectedSession && newChatTrigger)
      ) {
         sessionToUse = sessionId;
      }

      const uid = uuidv4();
      const now = new Date().toISOString();
      const formattedCreatedAt = formatDate(now, data?.timezone_name);
      const formattedUpdatedAt = formatDate(now, data?.timezone_name);

      setFetchedAnswers((prevAnswers) => [
         ...prevAnswers,
         {
            id: uid,
            question: userInput,
            answer: { text: '', image: {} },
            loading: true,
            thumbsUpClicked: false,
            thumbsDownClicked: false,
            showTimeFrameSelection: false,
            createdAt: formattedCreatedAt,
            updatedAt: formattedUpdatedAt,
            summary: false,
            timeFram: null,
            showMetricMenu: false,
            showObjectiveMenu: false,
            metric: null,
            objective: null,
            showThumbsdown: true,
            showThumbsup: true,
            showSummarize: false,
            showTopMatch: false,
         },
      ]);
      const payload: Payload = {
         client_id: userDetails.client_id,
         user: userDetails.email,
         mode: selectedMode,
         text: userInput,
         bearer_token: token,
         time_period: null,
         summary: false,
         metric: null,
         objective: null,
         dateFrequency: null,
         sessionId: sessionToUse,
      };
      setUserInput('');
      const res = await chatWithMarco(payload);
      if (res.success) {
         let answer = '';
         let image = {};
         let dateFrequency = '';
         if (Array.isArray(res.data)) {
            answer = res.data[0];
            image = res.data[1];
            dateFrequency = res.data[2];
         } else {
            answer = res.data as string;
         }
         setFetchedAnswers((prevAnswers) => {
            const updatedAnswers = prevAnswers.map((prevAnswer) => {
               if (
                  prevAnswer.question === payload.text &&
                  prevAnswer.id === uid
               ) {
                  return {
                     ...prevAnswer,
                     answer: { text: answer, image },
                     loading: false,
                     timeFrameChart: dateFrequency,
                     showSummarize: true,
                  };
               }
               return prevAnswer;
            });
            return updatedAnswers;
         });
         setmetricData([]);
         setobjectiveData([]);
         onSearch(sessionToUse, {
            id: uid,
            userask: payload.text,
            reply: {
               text: answer,
               image,
               timeFrameChart: dateFrequency,
               selectedMode,
            },
         });
      } else {
         let err: {
            data?: {
               detail: string | { metric?: string[]; objective?: string[] };
            };
         } = {};
         if (typeof res.data === 'object' && res.data) {
            err = res.data as object;
         }
         console.error('Error fetching answer:', res.data);
         const isTimeframeError = (
            detail: string | { metric?: string[]; objective?: string[] },
         ): detail is string =>
            typeof detail === 'string' && detail === 'Timeframe';

         const isMetricError = (
            detail: string | { metric?: string[]; objective?: string[] },
         ): detail is { metric: string[] } =>
            typeof detail === 'object' && detail !== null && 'metric' in detail;

         const isObjectiveError = (
            detail: string | { metric?: string[]; objective?: string[] },
         ): detail is { objective: string[] } =>
            typeof detail === 'object' &&
            detail !== null &&
            'objective' in detail;
         if (res.data && res.status === 422) {
            const detail = err.data?.detail;
            if (
               // res.data &&
               // res.status === 422 &&
               // // err.data?.detail === 'Timeframe'
               // // isTimeframeError()
               detail &&
               isTimeframeError(detail)
            ) {
               setFetchedAnswers((prevAnswers) => {
                  const updatedAnswers = prevAnswers.map((prevAnswer) => {
                     if (
                        prevAnswer.question === payload.text &&
                        prevAnswer.id === uid
                     ) {
                        return {
                           ...prevAnswer,
                           showTimeFrameSelection: true,
                           showMetricMenu: false,
                           showObjectiveMenu: false,
                        };
                     }
                     return prevAnswer;
                  });
                  return updatedAnswers;
               });
            } else if (
               // res.data &&
               // res.status === 422 &&
               // // err.data?.detail === 'Metric'
               detail &&
               isMetricError(detail)
            ) {
               setmetricData(detail.metric);
               setFetchedAnswers((prevAnswers) => {
                  const updatedAnswers = prevAnswers.map((prevAnswer) => {
                     if (
                        prevAnswer.question === payload.text &&
                        prevAnswer.id === uid
                     ) {
                        return {
                           ...prevAnswer,
                           showMetricMenu: true,
                           showObjectiveMenu: false,
                           showTopMatch: false,
                           showTimeFrameSelection: false,
                        };
                     }
                     return prevAnswer;
                  });
                  return updatedAnswers;
               });
            } else if (
               // res.data &&
               // res.status === 422 &&
               // err.data?.detail === 'Objective'
               detail &&
               isObjectiveError(detail)
            ) {
               setobjectiveData(detail.objective);
               setFetchedAnswers((prevAnswers) => {
                  const updatedAnswers = prevAnswers.map((prevAnswer) => {
                     if (
                        prevAnswer.question === payload.text &&
                        prevAnswer.id === uid
                     ) {
                        return {
                           ...prevAnswer,
                           showObjectiveMenu: true,
                           showMetricMenu: false,
                           showTopMatch: false,
                           showTimeFrameSelection: false,
                        };
                     }
                     return prevAnswer;
                  });
                  return updatedAnswers;
               });
            } else if (detail && isChannelError(detail)) {
               setchannelData(detail.channel);
               setFetchedAnswers((prevAnswers) => {
                  const updatedAnswers = prevAnswers.map((prevAnswer) => {
                     if (
                        prevAnswer.question === payload.text &&
                        prevAnswer.id === uid
                     ) {
                        return {
                           ...prevAnswer,
                           showObjectiveMenu: false,
                           showChannel: true,
                           showTopMatch: false,
                           showMetricMenu: false,
                           showTimeFrameSelection: false,
                        };
                     }
                     return prevAnswer;
                  });
                  return updatedAnswers;
               });
            } else if (detail && isTopMatchError(detail)) {
               setmetricData(detail.top_match);
               setFetchedAnswers((prevAnswers) => {
                  const updatedAnswers = prevAnswers.map((prevAnswer) => {
                     if (prevAnswer.id === uid) {
                        return {
                           ...prevAnswer,
                           showChannel: false,
                           showObjectiveMenu: false,
                           showTopMatch: true,
                           showMetricMenu: false,
                           showTimeFrameSelection: false,
                        };
                     }
                     return prevAnswer;
                  });
                  return updatedAnswers;
               });
            }
         } else {
            const answer = TEXTS.errorAnswer;
            const image = {};
            setFetchedAnswers((prevAnswers) => {
               const updatedAnswers = prevAnswers.map((prevAnswer) => {
                  if (
                     prevAnswer.question === payload.text &&
                     prevAnswer.id === uid
                  ) {
                     return {
                        ...prevAnswer,
                        answer: { text: answer, image },
                        loading: false,
                     };
                  }
                  return prevAnswer;
               });
               return updatedAnswers;
            });
            onSearch(sessionToUse, {
               id: uid,
               userask: payload.text,
               reply: { text: answer, image, selectedMode },
            });
         }
      }
   };
   const isTimeframeError = (
      detail: string | { metric?: string[]; objective?: string[] },
   ): detail is string => typeof detail === 'string' && detail === 'Timeframe';

   const isMetricError = (
      detail: string | { metric?: string[]; objective?: string[] },
   ): detail is { metric: string[] } =>
      typeof detail === 'object' && detail !== null && 'metric' in detail;

   const isObjectiveError = (
      detail: string | { metric?: string[]; objective?: string[] },
   ): detail is { objective: string[] } =>
      typeof detail === 'object' && detail !== null && 'objective' in detail;

   const handleTimeFrameSelection = async (
      selectedTimeFrame: number,
      id: string,
   ) => {
      setFetchedAnswers((prevAnswers) => {
         const updatedAnswers = prevAnswers.map((prevAnswer) => {
            if (
               // prevAnswer.question === userInputForTimeFrame &&
               prevAnswer.id === id
            ) {
               return {
                  ...prevAnswer,
                  showTimeFrameSelection: false,
                  timeFram: selectedTimeFrame,
               };
            }
            return prevAnswer;
         });
         return updatedAnswers;
      });
      let sessionToUse = selectedSession || sessionId;
      if (
         (!selectedSession && newChatTrigger) ||
         (selectedSession && newChatTrigger)
      ) {
         sessionToUse = sessionId;
      }
      const question = fetchedAnswers.find((answer) => answer.id === id);
      const payload: Payload = {
         client_id: userDetails.client_id,
         user: userDetails.email,
         mode: selectedMode,
         text: question?.question || userInputForTimeFrame,
         bearer_token: token,
         time_period: selectedTimeFrame,
         summary: false,
         metric: question?.metric || null,
         objective: question?.objective || null,
         dateFrequency: null,
         sessionId: sessionToUse,
      };

      if (question?.channel) payload.channel = question.channel;
      const res = await chatWithMarco(payload);
      if (res.success) {
         let answer = '';
         let image = {};
         let dateFrequency = '';

         if (Array.isArray(res.data)) {
            answer = res.data[0];
            image = res.data[1];
            dateFrequency = res.data[2];
         } else {
            answer = res.data as string;
         }

         setFetchedAnswers((prevAnswers) => {
            const updatedAnswers = prevAnswers.map((prevAnswer) => {
               if (prevAnswer.id === id) {
                  return {
                     ...prevAnswer,
                     answer: { text: answer, image },
                     loading: false,
                     timeFrameChart: dateFrequency,
                     showSummarize: true,
                  };
               }
               return prevAnswer;
            });
            return updatedAnswers;
         });
         setmetricData([]);
         setobjectiveData([]);
         onSearch(sessionToUse, {
            id: id,
            userask: question?.question || userInputForTimeFrame,
            reply: {
               text: answer,
               image,
               timeFrameChart: dateFrequency,
               selectedMode,
            },
         });
      } else if (typeof res.data === 'object' && res.data) {
         const err: {
            data?: {
               detail: string | { metric?: string[]; objective?: string[] };
            };
         } = res.data as object;

         if (res.data && res.status === 422) {
            const detail = err.data?.detail;
            if (detail && isMetricError(detail)) {
               setmetricData(detail.metric);
               setFetchedAnswers((prevAnswers) => {
                  const updatedAnswers = prevAnswers.map((prevAnswer) => {
                     if (prevAnswer.id === id) {
                        return {
                           ...prevAnswer,
                           showMetricMenu: true,
                           showObjectiveMenu: false,
                           showTimeFrameSelection: false,
                        };
                     }
                     return prevAnswer;
                  });
                  return updatedAnswers;
               });
            } else if (detail && isObjectiveError(detail)) {
               setobjectiveData(detail.objective);
               setFetchedAnswers((prevAnswers) => {
                  const updatedAnswers = prevAnswers.map((prevAnswer) => {
                     if (prevAnswer.id === id) {
                        return {
                           ...prevAnswer,
                           showObjectiveMenu: true,
                           showMetricMenu: false,
                           showTimeFrameSelection: false,
                        };
                     }
                     return prevAnswer;
                  });
                  return updatedAnswers;
               });
            } else if (detail && isChannelError(detail)) {
               setchannelData(detail.channel);
               setFetchedAnswers((prevAnswers) => {
                  const updatedAnswers = prevAnswers.map((prevAnswer) => {
                     if (
                        prevAnswer.question === payload.text &&
                        prevAnswer.id === id
                     ) {
                        return {
                           ...prevAnswer,
                           showObjectiveMenu: false,
                           showChannel: true,
                           showMetricMenu: false,
                           showTimeFrameSelection: false,
                        };
                     }
                     return prevAnswer;
                  });
                  return updatedAnswers;
               });
            } else if (detail && isTopMatchError(detail)) {
               setmetricData(detail.top_match);
               setFetchedAnswers((prevAnswers) => {
                  const updatedAnswers = prevAnswers.map((prevAnswer) => {
                     if (prevAnswer.id === id) {
                        return {
                           ...prevAnswer,
                           showChannel: false,
                           showObjectiveMenu: false,
                           showTopMatch: true,
                           showMetricMenu: false,
                           showTimeFrameSelection: false,
                        };
                     }
                     return prevAnswer;
                  });
                  return updatedAnswers;
               });
            }
         }
      } else {
         console.error('Error fetching answer:', res.data);
         const answer = TEXTS.errorAnswer;
         const image = {};
         setFetchedAnswers((prevAnswers) => {
            const updatedAnswers = prevAnswers.map((prevAnswer) => {
               if (prevAnswer.question === payload.text) {
                  return {
                     ...prevAnswer,
                     answer: { text: answer, image },
                     loading: false,
                  };
               }
               return prevAnswer;
            });
            return updatedAnswers;
         });
         onSearch(sessionToUse, {
            id: id,
            userask: payload.text,
            reply: { text: answer, image, selectedMode },
         });
      }
   };
   const handleObjectiveMenuSelection = async (
      selectedObjective: string,
      id: string,
      isChannel: boolean,
   ) => {
      setFetchedAnswers((prevAnswers) => {
         const updatedAnswers = prevAnswers.map((prevAnswer) => {
            if (prevAnswer.id === id) {
               return {
                  ...prevAnswer,
                  showObjectiveMenu: false,
                  showChannel: false,
                  objective: isChannel
                     ? prevAnswer.objective
                     : selectedObjective,
                  channel: isChannel ? selectedObjective : prevAnswer.channel,
               };
            }
            return prevAnswer;
         });
         return updatedAnswers;
      });
      let sessionToUse = selectedSession || sessionId;
      if (
         (!selectedSession && newChatTrigger) ||
         (selectedSession && newChatTrigger)
      ) {
         sessionToUse = sessionId;
      }
      const question = fetchedAnswers.find((answer) => answer.id === id);
      const payload: Payload = {
         client_id: userDetails.client_id,
         user: userDetails.email,
         mode: selectedMode,
         text: userInputForTimeFrame,
         bearer_token: token,
         time_period: question?.timeFram || null,
         summary: false,
         metric: question?.metric || null,
         objective: isChannel ? question?.objective || null : selectedObjective,
         channel: isChannel ? selectedObjective : question?.channel || null,
         dateFrequency: null,
         sessionId: sessionToUse,
      };
      const res = await chatWithMarco(payload);
      // // let err: { data?: { detail: string } } = {};
      // if (typeof res.data === 'object' && res.data) {
      //    err = res.data as object;
      // }
      if (res.success) {
         let answer = '';
         let image = {};
         let dateFrequency = '';

         if (Array.isArray(res.data)) {
            answer = res.data[0];
            image = res.data[1];
            dateFrequency = res.data[2];
         } else {
            answer = res.data as string;
         }

         setFetchedAnswers((prevAnswers) => {
            const updatedAnswers = prevAnswers.map((prevAnswer) => {
               if (prevAnswer.id === id) {
                  return {
                     ...prevAnswer,
                     answer: { text: answer, image },
                     loading: false,
                     timeFrameChart: dateFrequency,
                     showSummarize: true,
                  };
               }
               return prevAnswer;
            });
            return updatedAnswers;
         });
         setmetricData([]);
         setobjectiveData([]);
         onSearch(sessionToUse, {
            id: id,
            userask: question?.question || payload.text,
            reply: { text: answer, image, selectedMode },
         });
      } else if (typeof res.data === 'object' && res.data) {
         const err: {
            data?: {
               detail: string | { metric?: string[]; objective?: string[] };
            };
         } = res.data as object;

         if (res.data && res.status === 422) {
            const detail = err.data?.detail;
            if (detail && isMetricError(detail)) {
               setmetricData(detail.metric);
               setFetchedAnswers((prevAnswers) => {
                  const updatedAnswers = prevAnswers.map((prevAnswer) => {
                     if (prevAnswer.id === id) {
                        return {
                           ...prevAnswer,
                           showMetricMenu: true,
                           showObjectiveMenu: false,
                           showTimeFrameSelection: false,
                        };
                     }
                     return prevAnswer;
                  });
                  return updatedAnswers;
               });
            } else if (detail && isTimeframeError(detail)) {
               setFetchedAnswers((prevAnswers) => {
                  const updatedAnswers = prevAnswers.map((prevAnswer) => {
                     if (prevAnswer.id === id) {
                        return {
                           ...prevAnswer,
                           showTimeFrameSelection: true,
                           showMetricMenu: false,
                           showObjectiveMenu: false,
                        };
                     }
                     return prevAnswer;
                  });
                  return updatedAnswers;
               });
            } else if (detail && isObjectiveError(detail)) {
               setobjectiveData(detail.objective);
               setFetchedAnswers((prevAnswers) => {
                  const updatedAnswers = prevAnswers.map((prevAnswer) => {
                     if (prevAnswer.id === id) {
                        return {
                           ...prevAnswer,
                           showChannel: false,
                           showObjectiveMenu: true,
                           showMetricMenu: false,
                           showTimeFrameSelection: false,
                        };
                     }
                     return prevAnswer;
                  });
                  return updatedAnswers;
               });
            } else if (detail && isTopMatchError(detail)) {
               setmetricData(detail.top_match);
               setFetchedAnswers((prevAnswers) => {
                  const updatedAnswers = prevAnswers.map((prevAnswer) => {
                     if (prevAnswer.id === id) {
                        return {
                           ...prevAnswer,
                           showChannel: false,
                           showObjectiveMenu: false,
                           showTopMatch: true,
                           showMetricMenu: false,
                           showTimeFrameSelection: false,
                        };
                     }
                     return prevAnswer;
                  });
                  return updatedAnswers;
               });
            }
         }
      } else {
         console.error('Error fetching answer:', res.data);
         const answer = TEXTS.errorAnswer;
         const image = {};
         setFetchedAnswers((prevAnswers) => {
            const updatedAnswers = prevAnswers.map((prevAnswer) => {
               if (prevAnswer.question === payload.text) {
                  return {
                     ...prevAnswer,
                     answer: { text: answer, image },
                     loading: false,
                  };
               }
               return prevAnswer;
            });
            return updatedAnswers;
         });
         onSearch(sessionToUse, {
            id: id,
            userask: payload.text,
            reply: { text: answer, image, selectedMode },
         });
      }
   };
   const handleMetricMenuSelection = async (
      selectedMetric: string,
      id: string,
      isTopMatch: boolean,
   ) => {
      setFetchedAnswers((prevAnswers) => {
         const updatedAnswers = prevAnswers.map((prevAnswer) => {
            if (prevAnswer.id === id) {
               return {
                  ...prevAnswer,
                  showMetricMenu: false,
                  metric: isTopMatch ? prevAnswer.metric : selectedMetric,
                  topMatch: isTopMatch ? selectedMetric : prevAnswer.topMatch,
                  showTopMatch: false,
               };
            }
            return prevAnswer;
         });
         return updatedAnswers;
      });
      let sessionToUse = selectedSession || sessionId;
      if (
         (!selectedSession && newChatTrigger) ||
         (selectedSession && newChatTrigger)
      ) {
         sessionToUse = sessionId;
      }
      const question = fetchedAnswers.find((answer) => answer.id === id);
      const payload: Payload = {
         client_id: userDetails.client_id,
         user: userDetails.email,
         mode: selectedMode,
         text: userInputForTimeFrame,
         bearer_token: token,
         time_period: question?.timeFram || null,
         summary: false,
         metric: isTopMatch ? question?.metric || null : selectedMetric,
         objective: question?.objective || null,
         dateFrequency: null,
         channel: question?.channel || null,
         top_match: isTopMatch ? selectedMetric : question?.topMatch,
         sessionId: sessionToUse,
      };

      if (question?.channel) payload.channel = question.channel;
      const res = await chatWithMarco(payload);
      // let err: { data?: { detail: string } } = {};
      // if (typeof res.data === 'object' && res.data) {
      //    err = res.data as object;
      // }
      if (res.success) {
         let answer = '';
         let image = {};
         let dateFrequency = '';

         if (Array.isArray(res.data)) {
            answer = res.data[0];
            image = res.data[1];
            dateFrequency = res.data[2];
            console.log(image, 'image data at 428');
         } else {
            answer = res.data as string;
         }

         setFetchedAnswers((prevAnswers) => {
            const updatedAnswers = prevAnswers.map((prevAnswer) => {
               if (prevAnswer.id === id) {
                  return {
                     ...prevAnswer,
                     answer: { text: answer, image },
                     loading: false,
                     timeFrameChart: dateFrequency,
                     showSummarize: true,
                  };
               }
               return prevAnswer;
            });
            return updatedAnswers;
         });
         setmetricData([]);
         setobjectiveData([]);
         onSearch(sessionToUse, {
            id: id,
            userask: question?.question || payload.text,
            reply: { text: answer, image, selectedMode },
         });
      } else if (typeof res.data === 'object' && res.data) {
         const err: {
            data?: {
               detail: string | { metric?: string[]; objective?: string[] };
            };
         } = res.data as object;
         if (res.data && res.status === 422) {
            const detail = err.data?.detail;
            if (detail && isTimeframeError(detail)) {
               setFetchedAnswers((prevAnswers) => {
                  const updatedAnswers = prevAnswers.map((prevAnswer) => {
                     if (prevAnswer.id === id) {
                        return {
                           ...prevAnswer,
                           showTimeFrameSelection: true,
                           showMetricMenu: false,
                           showObjectiveMenu: false,
                           showChannel: false,
                           showTopMatch: false,
                        };
                     }
                     return prevAnswer;
                  });
                  return updatedAnswers;
               });
            } else if (detail && isMetricError(detail)) {
               setmetricData(detail.metric);
               setFetchedAnswers((prevAnswers) => {
                  const updatedAnswers = prevAnswers.map((prevAnswer) => {
                     if (prevAnswer.id === id) {
                        return {
                           ...prevAnswer,
                           showMetricMenu: true,
                           showObjectiveMenu: false,
                           showTimeFrameSelection: false,
                        };
                     }
                     return prevAnswer;
                  });
                  return updatedAnswers;
               });
            } else if (detail && isObjectiveError(detail)) {
               setobjectiveData(detail.objective);
               setFetchedAnswers((prevAnswers) => {
                  const updatedAnswers = prevAnswers.map((prevAnswer) => {
                     if (prevAnswer.id === id) {
                        return {
                           ...prevAnswer,
                           showChannel: false,
                           showObjectiveMenu: true,
                           showTopMatch: false,
                           showMetricMenu: false,
                           showTimeFrameSelection: false,
                        };
                     }
                     return prevAnswer;
                  });
                  return updatedAnswers;
               });
            } else if (detail && isTopMatchError(detail)) {
               setmetricData(detail.top_match);
               setFetchedAnswers((prevAnswers) => {
                  const updatedAnswers = prevAnswers.map((prevAnswer) => {
                     if (prevAnswer.id === id) {
                        return {
                           ...prevAnswer,
                           showChannel: false,
                           showObjectiveMenu: false,
                           showTopMatch: true,
                           showMetricMenu: false,
                           showTimeFrameSelection: false,
                        };
                     }
                     return prevAnswer;
                  });
                  return updatedAnswers;
               });
            }
         }
      } else {
         console.error('Error fetching answer:', res.data);
         const answer = TEXTS.errorAnswer;
         const image = {};
         setFetchedAnswers((prevAnswers) => {
            const updatedAnswers = prevAnswers.map((prevAnswer) => {
               if (prevAnswer.question === payload.text) {
                  return {
                     ...prevAnswer,
                     answer: { text: answer, image },
                     loading: false,
                  };
               }
               return prevAnswer;
            });
            return updatedAnswers;
         });
         onSearch(sessionToUse, {
            id: id,
            userask: payload.text,
            reply: { text: answer, image, selectedMode },
         });
      }
   };

   const handleModeChange = (mode: string) => {
      if (selectedMode != mode) {
         onNewChat();
      }
      setSelectedMode(mode);
   };

   useEffect(() => {
      if (newChatTrigger) {
         setFetchedAnswers([]);
         setShowSampleQuestions(true);
      }
   }, [newChatTrigger]);

   useEffect(() => {
      if (
         !showSampleQuestions &&
         userInput &&
         askButtonRef.current &&
         !askButtonDisabled
      ) {
         void handleAskClick();
      }
   }, [showSampleQuestions, askButtonRef, askButtonDisabled]);

   useEffect(() => {
      if (selectedMode) {
         setInputDisabled(false);
         setAskButtonDisabled(false);
      } else {
         setInputDisabled(true);
         setAskButtonDisabled(true);
      }
   }, [selectedMode, userInput]);

   const handleSampleQuestionClick = (question: string, mode: string) => {
      setSelectedMode(mode);
      setUserInput(question);
      setUserInputForTimeFrame(question);
      setShowSampleQuestions(false);
   };

   const handleKeyPress = (event: KeyboardEvent) => {
      if (event.key === 'Enter' && askButtonRef.current && !askButtonDisabled) {
         if (userInput.length) askButtonRef.current.click();
      }
   };
   useEffect(() => {
      document.addEventListener('keypress', handleKeyPress);
      return () => {
         document.removeEventListener('keypress', handleKeyPress);
      };
   }, [askButtonRef, askButtonDisabled, userInput]);

   const handleTimeFrameChartSelect = (id: string, timeFrame: string): void => {
      const timeFrameAsyncHandler = async () => {
         let sessionToUse = selectedSession || sessionId;
         if (
            (!selectedSession && newChatTrigger) ||
            (selectedSession && newChatTrigger)
         ) {
            sessionToUse = sessionId;
         }

         const lastAsked = fetchedAnswers.find((answer) => answer.id === id);
         if (!lastAsked) return;
         setFetchedAnswers((prevAnswers) => {
            return prevAnswers.map((prevAnswer) => {
               if (prevAnswer.id === id) {
                  return {
                     ...prevAnswer,
                     answer: {
                        text: `${timeFrame} Chart is loading`,
                        image: 'Loading',
                     },
                     timeFrameChart: timeFrame,
                  };
               }
               return prevAnswer;
            });
         });
         const payload: Payload = {
            client_id: userDetails.client_id,
            user: userDetails.email,
            mode: selectedMode,
            text: lastAsked.question,
            bearer_token: token,
            time_period: lastAsked.timeFram || null,
            summary: false,
            metric: null,
            objective: null,
            dateFrequency: timeFrame,
            sessionId: sessionToUse,
         };
         if (lastAsked.channel) payload.channel = lastAsked.channel;
         const res = await chatWithMarco(payload);
         if (res.success) {
            let answer: string | { text: string | undefined; image: object };
            let image = {};
            let dateFrequency = '';

            if (Array.isArray(res.data)) {
               answer = res.data[0];
               image = res.data[1];
               dateFrequency = res.data[2];
            } else {
               answer = res.data as string;
            }

            setFetchedAnswers((prevAnswers: ChatAnswers[]): ChatAnswers[] => {
               const updatedAnswers = prevAnswers.map((prevAnswer) => {
                  if (prevAnswer.id === id) {
                     return {
                        ...prevAnswer,
                        answer: { text: answer, image },
                        loading: false,
                        timeFrameChart: dateFrequency,
                     };
                  }
                  return prevAnswer;
               });
               return updatedAnswers;
            });
            // onSearch(sessionToUse, {
            //    id: id,
            //    userask: `${lastAsked.question}`,
            //    reply: {
            //       text: answer,
            //       image,
            //       // timeFrameChart: dateFrequency,
            //       selectedMode,
            //    },
            // });
         } else {
            console.error('Error fetching answer:', res.data);
            if (res.data && res.status === 422) {
               setFetchedAnswers((prevAnswers) => {
                  const updatedAnswers = prevAnswers.map((prevAnswer) => {
                     if (
                        prevAnswer.question.includes(`${lastAsked.question}`)
                     ) {
                        return {
                           ...prevAnswer,
                           showTimeFrameSelection: true,
                        };
                     }
                     return prevAnswer;
                  });
                  return updatedAnswers;
               });
            } else {
               const answer = TEXTS.errorAnswer;
               const image = {};
               setFetchedAnswers((prevAnswers) => {
                  const updatedAnswers = prevAnswers.map((prevAnswer) => {
                     if (
                        prevAnswer.question.includes(`${lastAsked.question}`)
                     ) {
                        return {
                           ...prevAnswer,
                           answer: { text: answer, image },
                           loading: false,
                        };
                     }
                     return prevAnswer;
                  });
                  return updatedAnswers;
               });
               onSearch(sessionToUse, {
                  id: id,
                  userask: `${lastAsked.question}`,
                  reply: { text: answer, image, selectedMode },
               });
            }
         }
      };
      void timeFrameAsyncHandler();
   };
   const handleChartTypeSelect = (id: string, chartType: string): void => {
      setFetchedAnswers((prevAnswers) => {
         return prevAnswers.map((prevAnswer) => {
            if (prevAnswer.id === id) {
               return {
                  ...prevAnswer,
                  chartType: chartType,
               };
            }
            return prevAnswer;
         });
      });
   };

   const toShow = (data: unknown): boolean => {
      if ((data as PerformanceChartData).dateFrequency !== undefined) {
         return true;
      }
      return false;
   };

   const isPerformanceChartData = (
      data: unknown,
   ): data is PerformanceChartData => {
      return (
         (data as PerformanceChartData).schema !== undefined ||
         Array.isArray(data)
      );
   };
   const filteredSampleQuestions = sampleQuestions.filter(
      (item) => item.mode === selectedMode,
   );
   const toShowSummarizeMenu = (index: number, check: boolean | undefined) => {
      if (index + 1 === fetchedAnswers.length && check) {
         return true;
      }
      return false;
   };

   return (
      <Flex
         className='chatbox'
         direction='column'
         justifyContent='center'
         alignItems='center'
         w='100%'
         flex={1}
         bg={useColorModeValue('white', 'var(--body)')}
      >
         {fetchedAnswers.length > 0 && (
            <Box
               p={20}
               w='100%'
               maxWidth='1300px'
               overflow='auto'
               maxHeight='calc(100% - 80px)'
            >
               {fetchedAnswers.map((data, index) => (
                  <Box
                     key={data.id}
                     ref={
                        index + 1 === fetchedAnswers.length
                           ? messagesEndRef
                           : null
                     }
                     mb={4}
                  >
                     <Text
                        fontSize={{ base: '8px', md: '10px', lg: '12px' }}
                        textAlign='right'
                     >
                        {data.createdAt}
                     </Text>
                     <Flex direction='row-reverse' mt={2} mb={3}>
                        <Box
                           bg={chatBubbleBg}
                           color='white'
                           p={2}
                           borderRadius='md'
                           maxW='65%'
                           fontSize={{
                              base: '10px',
                              md: '12px',
                              lg: '14px',
                           }}
                        >
                           {data.question}
                        </Box>
                     </Flex>
                     <Flex align='center' gap={4}>
                        <Image src={marcoResponse} alt='pic' boxSize='34px' />
                        <Text
                           fontSize={{ base: '8px', md: '10px', lg: '12px' }}
                        >
                           {data.createdAt}
                        </Text>
                     </Flex>
                     <Flex
                        direction='column'
                        align='flex-start'
                        p={4}
                        bg={responseBg}
                        borderRadius='md'
                        maxW='65%'
                        m={2}
                     >
                        {data.loading &&
                           !data.showTimeFrameSelection &&
                           !data.showObjectiveMenu &&
                           !data.showChannel &&
                           !data.showTopMatch &&
                           !data.showMetricMenu && <Skeletonloader />}
                        {data.showTimeFrameSelection && (
                           <Text>{TEXTS.selectedTimeFrame}</Text>
                        )}
                        {data.showMetricMenu && (
                           <Text>{TEXTS.selectedMetricMenu}</Text>
                        )}
                        {data.showObjectiveMenu && (
                           <Text>{TEXTS.selectedObjectiveMenu}</Text>
                        )}
                        {data.showChannel && <Text>{TEXTS.channel}</Text>}
                        {data.showTopMatch && <Text>{TEXTS.topMatch}</Text>}

                        {!data.showTimeFrameSelection &&
                           !data.showMetricMenu &&
                           !data.showObjectiveMenu && (
                              <Box
                                 textAlign='left'
                                 fontSize={{
                                    base: '10px',
                                    md: '12px',
                                    lg: '14px',
                                 }}
                              >
                                 {data.answer.image !== 'Loading' && (
                                    <ul style={{ listStyleType: 'none' }}>
                                       {data.answer.text
                                          ?.split('\n')
                                          .map((line, idx) => (
                                             <li key={idx}>{line.trim()}</li>
                                          ))}
                                    </ul>
                                 )}
                              </Box>
                           )}

                        {!data.showTimeFrameSelection && (
                           <Box width={'100%'}>
                              {data.answer.image &&
                                 !data.loading &&
                                 data.answer.image !== 'Loading' &&
                                 isPerformanceChartData(data.answer.image) && (
                                    <Box pt={data.answer.text != '' ? 4 : 0}>
                                       <Flex
                                          justify='space-between'
                                          pb={5}
                                          alignItems={'center'}
                                       >
                                          <Flex gap={2} flex={1}>
                                             <TimeFrameChartMenu
                                                handleTimeFrameSelect={
                                                   handleTimeFrameChartSelect
                                                }
                                                defaultValue={data.answer.image}
                                                id={data.id}
                                                showMenu={toShow(
                                                   data.answer.image,
                                                )}
                                             />

                                             <ChartTypeMenu
                                                id={data.id}
                                                defaultValue={data.chartType}
                                                handleChartTypeSelect={
                                                   handleChartTypeSelect
                                                }
                                                performanceData={
                                                   isPerformanceChartData(
                                                      data.answer.image,
                                                   )
                                                      ? data.answer.image
                                                      : undefined
                                                }
                                             />
                                          </Flex>
                                          <Box pr={2}>
                                             <SummarizeMenu
                                                toShowMenu={toShowSummarizeMenu(
                                                   index,
                                                   data.showSummarize,
                                                )}
                                                handleSummarize={
                                                   handleSummarizeWrapper
                                                }
                                                id={data.id}
                                                performanceData={
                                                   isPerformanceChartData(
                                                      data.answer.image,
                                                   )
                                                      ? data.answer.image
                                                      : undefined
                                                }
                                                chartType={data.chartType}
                                             />
                                          </Box>
                                       </Flex>
                                       {data.answer.image
                                          .average_KPI_values && (
                                          <Flex>
                                             <AverageKPIValue
                                                performanceData={
                                                   isPerformanceChartData(
                                                      data.answer.image,
                                                   )
                                                      ? data.answer.image
                                                      : undefined
                                                }
                                             />
                                          </Flex>
                                       )}
                                       <Box maxHeight={'50vh'}>
                                          <PerformanceChart
                                             performanceData={
                                                isPerformanceChartData(
                                                   data.answer.image,
                                                )
                                                   ? data.answer.image
                                                   : undefined
                                             }
                                             chartType={data.chartType}
                                          />
                                       </Box>
                                    </Box>
                                 )}
                              {data.answer.image === 'Loading' && (
                                 <Flex align='center' gap={2}>
                                    <Spinner size='md' />
                                    {data.answer.text}
                                 </Flex>
                              )}
                           </Box>
                        )}
                     </Flex>
                     {data.showTimeFrameSelection && (
                        <TimeFrameSelection
                           onSelect={(selectedTimeFrame) =>
                              void handleTimeFrameSelection(
                                 selectedTimeFrame,
                                 data.id,
                              )
                           }
                        />
                     )}
                     {data.showMetricMenu && (
                        <MetricMenuSelection
                           onSelect={(selectedMetric) =>
                              void handleMetricMenuSelection(
                                 selectedMetric,
                                 data.id,
                                 false,
                              )
                           }
                           dynamicData={metricData}
                        />
                     )}
                     {data.showTopMatch && (
                        <MetricMenuSelection
                           onSelect={(selectedMetric) =>
                              void handleMetricMenuSelection(
                                 selectedMetric,
                                 data.id,
                                 true,
                              )
                           }
                           dynamicData={metricData}
                        />
                     )}
                     {data.showObjectiveMenu && (
                        <ObjectiveMenuSelection
                           onSelect={(selectedObjective) =>
                              void handleObjectiveMenuSelection(
                                 selectedObjective,
                                 data.id,
                                 false,
                              )
                           }
                           dynamicData={objectiveData}
                        />
                     )}
                     {data.showChannel && (
                        <ObjectiveMenuSelection
                           onSelect={(selectedObjective) =>
                              void handleObjectiveMenuSelection(
                                 selectedObjective,
                                 data.id,
                                 true,
                              )
                           }
                           dynamicData={channelData}
                        />
                     )}
                     {!data.loading && (
                        <Flex pl={4} align='baseline'>
                           {data.answer.text && (
                              <Tooltip
                                 label='Copy response'
                                 fontSize={{ base: 'xs', md: 'sm' }}
                                 bg='#437eeb'
                                 color='white'
                                 mt={1}
                                 ml={1}
                              >
                                 <Image
                                    src={copy}
                                    alt='copy_icon'
                                    onClick={() => handleCopy(data.answer.text)}
                                    title='Copy to clipboard'
                                    cursor='pointer'
                                    boxSize='25px'
                                 />
                              </Tooltip>
                           )}
                           <Tooltip
                              label='Good response'
                              fontSize={{ base: 'xs', md: 'sm' }}
                              bg='#437eeb'
                              color='white'
                              mt={1}
                              ml={1}
                           >
                              <Image
                                 src={
                                    data.thumbsUpClicked &&
                                    !data.thumbsDownClicked
                                       ? thumsupclick
                                       : thumsup
                                 }
                                 alt='thumsup_icon'
                                 onClick={() =>
                                    handleThumbsUp(index, data.id, 'like')
                                 }
                                 title='You like the response'
                                 cursor='pointer'
                                 boxSize='25px'
                                 style={{
                                    filter:
                                       colorMode === 'dark'
                                          ? 'invert(1)'
                                          : 'none', // Apply invert for dark mode
                                    transition: 'filter 0.3s ease-in-out', // Smooth transition
                                 }}
                              />
                           </Tooltip>
                           <Tooltip
                              label='Bad response'
                              fontSize={{ base: 'xs', md: 'sm' }}
                              bg='#437eeb'
                              color='white'
                              mt={1}
                              ml={1}
                           >
                              <Image
                                 src={
                                    data.thumbsDownClicked &&
                                    !data.thumbsUpClicked
                                       ? thumsdownclick
                                       : thumsdown
                                 }
                                 alt='thumsdown_icon'
                                 onClick={() =>
                                    handleThumbsDown(index, data.id, 'dislike')
                                 }
                                 title='You do not like the response'
                                 cursor='pointer'
                                 boxSize='25px'
                                 style={{
                                    filter:
                                       colorMode === 'dark'
                                          ? 'invert(1)'
                                          : 'none', // Apply invert for dark mode
                                    transition: 'filter 0.3s ease-in-out', // Smooth transition
                                 }}
                              />
                           </Tooltip>
                        </Flex>
                     )}
                  </Box>
               ))}
            </Box>
         )}

         <Flex
            direction='column'
            w='100%'
            maxWidth='1300px'
            bg={bgColor}
            justifyContent='center'
            p={10}
         >
            {showSampleQuestions && (
               <Stack align='center' pb={5} mb={30}>
                  <Flex
                     align='center'
                     fontSize={{ base: '20px', md: '30px', lg: '40px' }}
                     pb={5}
                  >
                     <Image
                        src={marco}
                        alt='MARCO FlableAI Icon'
                        boxSize={{ base: '30px', md: '46px' }}
                        mr={2}
                        sx={{ fill: 'blue' }}
                        filter={useColorModeValue('none', 'invert(1)')}
                     />
                     {TEXTS.marco}
                  </Flex>
                  <Flex
                     wrap='wrap'
                     align='center'
                     justify='center'
                     gap={2}
                     mb={2}
                     maxW='90%'
                     fontSize={{ base: 'xs', md: 'sm' }}
                  >
                     {TEXTS.sampleQuestionsHeading}
                     <Text as='b'>{`${selectedMode === 'Console' ? 'Web Analytics' : selectedMode}`}</Text>
                     {filteredSampleQuestions.map((item, idx) => (
                        <Box
                           key={idx}
                           border='1px'
                           borderColor='gray.300'
                           padding='5px 15px'
                           borderRadius='50'
                           cursor='pointer'
                           bg={useColorModeValue('white', 'var(--controls)')}
                           _hover={{
                              bg: useColorModeValue(
                                 'gray.100',
                                 'var(--controls-hover)',
                              ),
                              color: useColorModeValue('black', 'white'),
                           }}
                           onClick={() =>
                              handleSampleQuestionClick(
                                 item.question,
                                 item.mode,
                              )
                           }
                        >
                           {item.question}
                        </Box>
                     ))}
                  </Flex>
               </Stack>
            )}
            <Box
               w='100%'
               mb={4}
               backgroundColor={warningBg}
               border={`1px solid ${warningBorder}`}
               borderRadius={'4px'}
            >
               <Text w='100%' pl={'4'} fontSize={'11.5px'} textAlign={'center'}>
                  <b>{modeDescription[selectedMode].description}</b>
               </Text>
            </Box>
            <Flex align='center' justify='center' w='100%' gap={2}>
               <Box h={'40px'}>
                  <MySelect
                     marcoCustomSelect={marcoCustomSelect}
                     options={modeOptions}
                     showSampleQuestions={showSampleQuestions}
                     selectedMode={selectedMode}
                     onModeChange={handleModeChange}
                  />
               </Box>
               <Box flex='3'>
                  <Input
                     type='text'
                     placeholder='Ask MARCO'
                     value={userInput}
                     onChange={handleInputChange}
                     isDisabled={inputDisabled}
                     bg={inputDisabled ? inputDisabledBg : inputBg}
                     borderWidth='1px'
                     borderRadius='md'
                     _hover={{ borderColor: 'gray.500' }}
                     p={2}
                     height={{ base: '30px', md: '40px' }}
                     _placeholder={{ fontSize: '12px' }}
                  />
               </Box>
               <Box>
                  <Button
                     id={id}
                     ref={askButtonRef}
                     onClick={handleAskClick}
                     isDisabled={askButtonDisabled}
                     leftIcon={
                        <Image
                           src={askButtonDisabled ? askIconDisabled : askIcon}
                           alt='askIcon'
                           boxSize={{ sm: '10px', md: '20px' }}
                        />
                     }
                     borderWidth='1px'
                     borderRadius='md'
                     bg={askButtonDisabled ? buttonDisabledBg : buttonBg}
                     color='#437eeb'
                     height={{ base: '30px', md: '40px' }}
                     _hover={{
                        bg: useColorModeValue(
                           'gray.200',
                           'var(--background-surface)',
                        ),
                     }}
                  >
                     <Text fontSize={'12px'}>{TEXTS.askButtonText}</Text>
                  </Button>
               </Box>
            </Flex>
            <p className='text-[12px] text-gray-500 text-center'>
               Marco can make mistakes. Please double-check responses.
            </p>
         </Flex>
      </Flex>
   );
};

export default QnABox;
