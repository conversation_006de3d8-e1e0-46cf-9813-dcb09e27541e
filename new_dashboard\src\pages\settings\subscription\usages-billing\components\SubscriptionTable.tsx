import subsEndPoints from '@/api/service/subscription';
import DefaultCard from '@/components/DefaultCard';
import { Badge } from '@/components/ui/badge';
import { useApiQuery } from '@/hooks/react-query-hooks';
import { UserDetails } from '@/pages/socialwatch/interface';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import { Skeleton } from '@/components/ui/skeleton';
import { format } from 'date-fns';

export default function SubscriptionTable() {
   const userDetails =
      LocalStorageService.getItem<UserDetails>(Keys.FlableUserDetails) ||
      ({} as UserDetails);

   const { data: SubscriptionHistory, isLoading: isSubscriptionLoading } =
      useApiQuery({
         queryKey: ['getSubscriptionHistory', userDetails?.client_id],
         queryFn: () =>
            subsEndPoints.getSubscriptionHistoryRecords(userDetails?.client_id),
         enabled: !!userDetails?.client_id,
         refetchOnWindowFocus: false,
      });

   return (
      <div className='w-full border border-gray-200 bg-white shadow-sm rounded-lg overflow-hidden'>
         <div className='overflow-x-auto min-h-[400px] max-h-[400px] overflow-y-auto scrollable'>
            <table className='w-full min-w-[1200px]'>
               <thead className='bg-gray-50 sticky top-0 z-20'>
                  <tr className='sticky top-0 z-20 bg-navy text-white'>
                     <th
                        colSpan={7}
                        className='py-3 px-6 text-lg font-bold text-left tracking-wide sticky top-0 z-20 bg-navy text-white'
                     >
                        Subscription History
                     </th>
                  </tr>
                  <tr className='border-b border-gray-200 bg-gray-50 sticky top-[48px] z-10'>
                     <th className='font-semibold text-gray-900 py-4 px-6 text-left'>
                        Plan
                     </th>
                     <th className='font-semibold text-gray-900 py-4 px-6 text-left'>
                        Currency
                     </th>
                     <th className='font-semibold text-gray-900 py-4 px-6 text-left'>
                        Amount
                     </th>
                     <th className='font-semibold text-gray-900 py-4 px-6 text-left'>
                        Status
                     </th>
                     <th className='font-semibold text-gray-900 py-4 px-6 text-left'>
                        Remarks
                     </th>
                     <th className='font-semibold text-gray-900 py-4 px-6 text-left'>
                        Start Date
                     </th>
                     <th className='font-semibold text-gray-900 py-4 px-6 text-left'>
                        End Date
                     </th>
                  </tr>
               </thead>
               <tbody>
                  {isSubscriptionLoading ? (
                     Array.from({ length: 4 }).map((_, idx) => (
                        <tr key={idx} className='border-b border-gray-100'>
                           <td className='py-4 px-6'>
                              <Skeleton className='h-4 w-24' />
                           </td>
                           <td className='py-4 px-6'>
                              <Skeleton className='h-4 w-16' />
                           </td>
                           <td className='py-4 px-6'>
                              <Skeleton className='h-4 w-20' />
                           </td>
                           <td className='py-4 px-6'>
                              <Skeleton className='h-6 w-16 rounded-full' />
                           </td>
                           <td className='py-4 px-6'>
                              <Skeleton className='h-4 w-32' />
                           </td>
                           <td className='py-4 px-6'>
                              <Skeleton className='h-4 w-28' />
                           </td>
                           <td className='py-4 px-6'>
                              <Skeleton className='h-4 w-28' />
                           </td>
                        </tr>
                     ))
                  ) : SubscriptionHistory?.history?.length ? (
                     SubscriptionHistory.history.map((record, index) => (
                        <tr
                           key={record.id}
                           className={`border-b border-gray-100 hover:bg-gray-50 transition-colors duration-150 ${
                              index % 2 === 0 ? 'bg-white' : 'bg-gray-25'
                           }`}
                        >
                           <td className='py-4 px-6 font-medium text-gray-900'>
                              {record.plan_name}
                           </td>
                           <td className='py-4 px-6 text-gray-700'>
                              {record.currency}
                           </td>
                           <td className='py-4 px-6 font-semibold text-gray-900'>
                              ₹{record.amount.toLocaleString()}
                           </td>
                           <td className='py-4 px-6'>
                              <Badge
                                 variant={
                                    record.status === 'active'
                                       ? 'success'
                                       : record.status === 'cancelled'
                                         ? 'destructive'
                                         : 'outline'
                                 }
                                 className='capitalize font-medium px-3 py-1'
                              >
                                 {record.status}
                              </Badge>
                           </td>
                           <td className='py-4 px-6 text-gray-700 text-sm'>
                              {record.remarks || '-'}
                           </td>
                           <td className='py-4 px-6 text-gray-700 text-sm'>
                              {record.subs_start_dt
                                 ? format(
                                      new Date(record.subs_start_dt),
                                      'dd MMM yyyy, hh:mm a',
                                   )
                                 : '-'}
                           </td>
                           <td className='py-4 px-6 text-gray-700 text-sm'>
                              {record.created_at
                                 ? format(
                                      new Date(record.created_at),
                                      'dd MMM yyyy, hh:mm a',
                                   )
                                 : '-'}
                           </td>
                        </tr>
                     ))
                  ) : (
                     <tr>
                        <td colSpan={7} className='py-8 px-6'>
                           <DefaultCard
                              banner='inactivePlan'
                              imgClassName='w-28'
                              title='No Subscription History Found'
                              titleClassName='head6'
                           />
                        </td>
                     </tr>
                  )}
               </tbody>
            </table>
         </div>
      </div>
   );
}
