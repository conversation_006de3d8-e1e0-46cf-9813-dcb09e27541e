import dashboardApiAgent from '../../agent';
import { PromiseAxios } from '../common';

export interface KPIRules {
   metric: string;
   trend: 'increasing' | 'decreasing' | '';
   value: string;
   value_type: 'absolute' | 'percentage' | '';
   comparison: 'more_than' | 'less_than' | 'equal_to' | '';
}

export interface AlertConditions {
   channel: string;
   campaigns: { id: string; name: string }[];
   metrics: string[];
   target_period: { value: string; label: string };
   reference_period: { value: string; label: string };
   kpi_rules: KPIRules[];
}

export interface CustomAlert {
   alert_id?: string | null;
   client_id: string;
   user_id: string;
   alert_name: string;
   alert_description: string;
   alert_time: string;
   email_recipients: string[];
   alert_status: 'active' | 'inactive';
   alert_conditions: AlertConditions;
   user_timezone: string;
   emails_sent: number;
   alert_created_at?: Date;
   alert_updated_at?: Date;
}

export interface CustomAlertOptions {
   channels: string[];
   campaigns: {
      meta_ads_campaigns: { id: string; name: string; objective: string }[];
      google_ads_campaigns: { id: string; name: string; objective: string }[];
   };
   metrics: {
      dashboard_metrics: {
         facebookads: string[];
         googleads: string[];
         amazon_ads: string[];
         store: string[];
         web: string[];
         amazon_selling_partner: string[];
      };
      meta_campaigns_metrics: string[];
      google_campaigns_metrics: string[];
   };
}

/** PAYLOADS **/
interface FetchOptionsPayload {
   client_id: string;
   user_id: string;
}

/** RESPONSES **/

interface CommonResponse {
   status: 'success' | 'error';
   message?: string;
}

/** ENDPOINTS **/
interface Endpoints {
   fetchOptions: (
      payload: FetchOptionsPayload,
   ) => PromiseAxios<CustomAlertOptions>;

   getAllAlerts: (payload: FetchOptionsPayload) => PromiseAxios<CustomAlert[]>;

   createOrUpdateAlert: (payload: CustomAlert) => PromiseAxios<CommonResponse>;

   deleteAlert: (payload: {
      client_id: string;
      user_id: string;
      alertId: number;
   }) => PromiseAxios<CommonResponse>;

   deleteMultipleAlerts: (payload: {
      client_id: string;
      user_id: string;
      alertIds: number[];
   }) => PromiseAxios<CommonResponse>;
}

const customAlertsAPI: Endpoints = {
   fetchOptions: (payload) => {
      return dashboardApiAgent.get(
         `/custom-alert/${payload.client_id}/${payload.user_id}/alert-options`,
      );
   },

   getAllAlerts: (payload) => {
      return dashboardApiAgent.get(
         `/custom-alert/${payload.client_id}/${payload.user_id}/alerts`,
      );
   },

   createOrUpdateAlert: (payload) => {
      return dashboardApiAgent.post(
         `/custom-alert/${payload.client_id}/${payload.user_id}/alerts`,
         payload,
      );
   },

   deleteAlert: (payload) => {
      return dashboardApiAgent.delete(
         `/custom-alert/${payload.client_id}/${payload.user_id}/alerts/${payload.alertId}`,
      );
   },

   deleteMultipleAlerts: (payload) => {
      return dashboardApiAgent.post(
         `/custom-alert/${payload.client_id}/${payload.user_id}/alerts/delete-multiple`,
         { alertIds: payload.alertIds },
      );
   },
};

export default customAlertsAPI;
