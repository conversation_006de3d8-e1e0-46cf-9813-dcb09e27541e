import customAlertsAPI, { CustomAlert } from '@/api/service/custom-alerts';
import { useApiMutation, useApiQuery } from '@/hooks/react-query-hooks';
import { AuthUser } from '@/types/auth';
import { Keys, LocalStorageService } from '@/utils/local-storage';

export const useFetchCustomAlertOptions = () => {
   const { client_id, user_id } = LocalStorageService.getItem(
      Keys.FlableUserDetails,
   ) as AuthUser;

   return useApiQuery({
      queryKey: ['custom-alert-options', client_id, user_id],
      queryFn: () => customAlertsAPI.fetchOptions({ client_id, user_id }),
      enabled: !!client_id && !!user_id,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      refetchOnMount: false,
   });
};

export const useFetchAllAlerts = () => {
   const { client_id, user_id } = LocalStorageService.getItem(
      Keys.FlableUserDetails,
   ) as AuthUser;

   return useApiQuery({
      queryKey: ['custom-alerts', client_id, user_id],
      queryFn: () => customAlertsAPI.getAllAlerts({ client_id, user_id }),
      enabled: !!client_id && !!user_id,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      refetchOnMount: false,
   });
};

export const useCreateOrUpdateCustomAlert = () => {
   const { client_id, user_id } = LocalStorageService.getItem(
      Keys.FlableUserDetails,
   ) as AuthUser;

   return useApiMutation({
      queryKey: ['create-or-update-custom-alert', client_id, user_id],
      mutationFn: (payload: CustomAlert) =>
         customAlertsAPI.createOrUpdateAlert({
            ...payload,
            client_id,
            user_id,
         }),
   });
};

export const useDeleteCustomAlert = () => {
   const { client_id, user_id } = LocalStorageService.getItem(
      Keys.FlableUserDetails,
   ) as AuthUser;

   return useApiMutation({
      queryKey: ['delete-custom-alert', client_id, user_id],
      mutationFn: (alertId: number) =>
         customAlertsAPI.deleteAlert({ client_id, user_id, alertId }),
   });
};

export const useDeleteMultipleCustomAlerts = () => {
   const { client_id, user_id } = LocalStorageService.getItem(
      Keys.FlableUserDetails,
   ) as AuthUser;

   return useApiMutation({
      queryKey: ['delete-multiple-custom-alerts', client_id, user_id],
      mutationFn: (alertIds: number[]) =>
         customAlertsAPI.deleteMultipleAlerts({ client_id, user_id, alertIds }),
   });
};
