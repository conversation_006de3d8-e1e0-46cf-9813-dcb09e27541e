@use '../../sass/variable.scss';
.Tourpopup {
   .infoBtn {
      background-color: transparent !important;
      // display: block !important;
      justify-content: left;
      width: 100% !important;
      padding: 10px !important;
      display: flex;
      cursor: pointer !important;
      color: var(--color-grey-300) !important;
      border-radius: 4px !important;
      text-decoration: none !important;
      position: relative !important;
      transition: all 0.3s ease-out !important;
   }
   &:hover {
      color: hsl(241, 51%, 20%);
      background-color: hsl(240, 100%, 98%);
      [data-theme='darl'] & {
         background-color: hsl(240, 12%, 17%);
      }
   }
   .questionMark {
      height: 30px;
      width: 25px;
      margin-left: 0.5px;
      color: hsl(240, 13%, 72%);
   }
   .tourHeading {
      color: hsl(240, 10%, 49%);
      padding-left: 4px;
      font-size: 16px;
      font-weight: 700;
      font-family: sans-serif;
      line-height: 1.2;
   }
}

.PopoverWrapper {
   .startTourBtn {
      display: flex;

      justify-content: end;
      padding: 10px;
   }
   .tourBtns {
      padding: 18px 20px !important;
   }
   .TourPopoverContainer {
      border: 1px solid #242424;
      padding: 20px;
      border-radius: 10px;
   }
   .TourPopoverContainer h1 {
      color: #242424;
      font-family: 'Poppins', sans-serif;
      font-size: 20px;
      font-weight: 600;
      line-height: 28px;
   }
   .TourPopoverContainer p {
      color: #1a202c;
      font-family: 'Poppins', sans-serif;
      font-size: 16px;
      padding-top: 10px;
      padding-bottom: 20px;
   }
}
