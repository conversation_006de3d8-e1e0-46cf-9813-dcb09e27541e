import { FC, useRef } from 'react';
import {
   Button,
   AlertDialog,
   AlertDialogBody,
   AlertDialogFooter,
   AlertDialogHeader,
   AlertDialogContent,
   AlertDialogOverlay,
} from '@chakra-ui/react';

interface ClearHistoryDialogProps {
   isOpen: boolean;
   onClose: () => void;
   onClearHistory: () => Promise<void>;
}

const ClearHistoryDialog: FC<ClearHistoryDialogProps> = ({
   isOpen,
   onClose,
   onClearHistory,
}) => {
   const cancelRef = useRef<HTMLButtonElement | null>(null);
   const handleClearHistoryClick = () => {
      void onClearHistory();
   };
   return (
      <AlertDialog
         isOpen={isOpen}
         leastDestructiveRef={cancelRef}
         onClose={onClose}
      >
         <AlertDialogOverlay>
            <AlertDialogContent>
               <AlertDialogHeader fontSize='lg' fontWeight='bold'>
                  Clear History
               </AlertDialogHeader>

               <AlertDialogBody>
                  Are you sure you want to clear the entire history? This action
                  cannot be undone.
               </AlertDialogBody>

               <AlertDialogFooter>
                  <Button onClick={onClose}>Cancel</Button>
                  <Button
                     colorScheme='red'
                     onClick={handleClearHistoryClick}
                     ml={3}
                  >
                     Clear History
                  </Button>
               </AlertDialogFooter>
            </AlertDialogContent>
         </AlertDialogOverlay>
      </AlertDialog>
   );
};

export default ClearHistoryDialog;
