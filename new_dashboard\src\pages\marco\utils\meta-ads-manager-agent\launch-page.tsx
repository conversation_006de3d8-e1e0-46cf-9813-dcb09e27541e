import {
   Flex,
   <PERSON>ing,
   Image,
   Stack,
   Text,
   Card,
   Button,
   Box,
} from '@chakra-ui/react';
import { MdCampaign } from 'react-icons/md';
import { BsCurrencyDollar } from 'react-icons/bs';
import {
   FaMapMarkerAlt,
   FaUsers,
   FaVenusMars,
   FaShoppingCart,
} from 'react-icons/fa';
import { ChatIcon } from '@chakra-ui/icons';
import { useAppSelector } from '../../../../store/store';
import { ExtendedAdCreativePayload } from '../../agents/meta-ads-manager';
const formatLabel = (label: string) => {
   return label.replace(/_/g, ' ').replace(/\b\w/g, (c) => c.toUpperCase());
};
export const LaunchPage = ({ messageIndex }: { messageIndex: number }) => {
   const adCreativeCardState = useAppSelector(
      (state) => state.metaAdsManager.adCreativeCardState,
   );
   const { currentChat, campaignDetails, adsetData } = useAppSelector(
      (state) => state.metaAdsManager,
   );

   const message = currentChat?.[messageIndex];
   if (!message?.adCreativeData) return null;

   const adCreativeData = message.adCreativeData as ExtendedAdCreativePayload;
   const { object_story_spec } = adCreativeData;
   const { picture } = object_story_spec.link_data;

   return (
      <Box width='100%' mt={6}>
         <Card
            p={4}
            border='1px solid #e2e8f0'
            borderRadius='md'
            mt={4}
            width='100%'
            bg='gray.50'
         >
            <Heading size='md' mb={6} textAlign='center'>
               Launching 1 campaign, 1 ad set with 1 ad 🎉
            </Heading>
            <Stack spacing={5}>
               <Flex>
                  <Box flex='1'>
                     <Stack spacing={5}>
                        <Flex align='center' justify='space-between'>
                           <Flex align='center' gap={3}>
                              <MdCampaign size={20} color='#718096' />
                              <Text color='gray.600'>Campaign name</Text>
                           </Flex>
                           <Text fontWeight='600'>
                              {message.adSettings?.campaignName ||
                                 campaignDetails?.name ||
                                 'N/A'}
                           </Text>
                        </Flex>
                        <Flex align='center' justify='space-between'>
                           <Flex align='center' gap={3}>
                              <BsCurrencyDollar size={20} color='#718096' />
                              <Text color='gray.600'>Ad set name</Text>
                           </Flex>
                           <Text fontWeight='600'>
                              {message.adSettings?.adsetName ||
                                 adsetData?.adset_name ||
                                 'N/A'}
                           </Text>
                        </Flex>
                        <Flex align='center' justify='space-between'>
                           <Flex align='center' gap={3}>
                              <BsCurrencyDollar size={20} color='#718096' />
                              <Text color='gray.600'>Total Daily spend</Text>
                           </Flex>
                           <Text fontWeight='600'>
                              ₹ {Number(campaignDetails?.daily_budget)}
                           </Text>
                        </Flex>

                        <Flex align='center' justify='space-between'>
                           <Flex align='center' gap={3}>
                              <FaMapMarkerAlt size={20} color='#718096' />
                              <Text color='gray.600'>Included locations</Text>
                           </Flex>
                           <Flex align='center' gap={2}>
                              <Image
                                 src='https://flagcdn.com/w20/in.png'
                                 alt='India flag'
                                 width='20px'
                              />
                              <Text fontWeight='600'>
                                 {message.adSettings?.locations || 'India'}
                              </Text>
                           </Flex>
                        </Flex>
                        <Flex align='center' justify='space-between'>
                           <Flex align='center' gap={3}>
                              <FaUsers size={20} color='#718096' />
                              <Text color='gray.600'>Age range</Text>
                           </Flex>
                           <Text fontWeight='600'>22-44</Text>
                        </Flex>
                        <Flex align='center' justify='space-between'>
                           <Flex align='center' gap={3}>
                              <FaVenusMars size={20} color='#718096' />
                              <Text color='gray.600'>Gender</Text>
                           </Flex>
                           <Text fontWeight='600'>
                              {message.adSettings?.gender ||
                                 'Male ♂ , Female ♀'}
                           </Text>
                        </Flex>
                        <Flex align='center' justify='space-between'>
                           <Flex align='center' gap={3}>
                              <FaShoppingCart size={20} color='#718096' />
                              <Text color='gray.600'>Optimization Goal</Text>
                           </Flex>
                           <Text fontWeight='600'>
                              {typeof (
                                 message.adSettings?.optimizationGoal ||
                                 adsetData?.optimization_goal
                              ) === 'string'
                                 ? formatLabel(
                                      message.adSettings?.optimizationGoal ||
                                         adsetData?.optimization_goal ||
                                         'N/A',
                                   )
                                 : ' N/A'}
                           </Text>
                        </Flex>
                        <Flex align='center' gap={3}>
                           <ChatIcon color='#718096' />
                           <Text color='gray.600'>Ad Preview</Text>
                        </Flex>
                     </Stack>
                  </Box>

                  <Box width='200px' position='relative' ml={4}>
                     <Box
                        position='absolute'
                        bottom='0px'
                        right='60px'
                        boxShadow='0px 2px 8px rgba(0, 0, 0, 0.1)'
                        borderRadius='lg'
                        overflow='hidden'
                        width='100px'
                        height='100px'
                     >
                        <Image
                           src={
                              message.adSettings?.adImage ||
                              adCreativeCardState.previewImage ||
                              adCreativeCardState.uploadedImageUri ||
                              picture
                           }
                           alt='Ad creative'
                           width='100%'
                           height='100%'
                           objectFit='cover'
                           fallback={
                              <Box bg='gray.100' width='100%' height='100%' />
                           }
                        />
                     </Box>
                  </Box>
               </Flex>

               <Box mt={4}>
                  <Button
                     colorScheme='blue'
                     size='lg'
                     width='100%'
                     onClick={() =>
                        window.open(
                           'https://adsmanager.facebook.com/adsmanager/manage/campaigns?',
                           '_blank',
                        )
                     }
                     _hover={{
                        transform: 'translateY(-2px)',
                        boxShadow: '0px 6px 20px rgba(0, 0, 0, 0.15)',
                     }}
                     transition='all 0.2s ease-in-out'
                  >
                     Review on Meta
                  </Button>
               </Box>
            </Stack>
         </Card>
      </Box>
   );
};
