export const ALERTING_PROMPTS = [
   'Set an alert for Google Ads conversions dropping by 15% compared to the last 30 days.',
];

export const ALERT_LOADER_MESSAGES = [
   'Analysing prompt...',
   'Creating alert...',
];

export const QUESTION_SUGGESTIONS = {
   web: [
      'Set me an alert for web if the average pages per session drops below 3',
      'Set me an alert if the bounce rate increases by more than 15% in a week',
      'Notify me when total users drop by more than 30% compared to last month.',
   ],
   facebookads: [
      'Notify me if my Meta Ads CPM increases by more than 20% in the last 7 days.',
      'Alert me if my Meta Ads conversion rate drops by more than 10% compared to last 30 days.',
      'Set me an alert if my Meta Ads impressions decrease by 25% in a week.',
   ],
   store: [
      'Set an alert for shopify if average order value drops below 500',
      'Notify me when my shopify total sales decrease by more than 15% in a week',
      'Alert me when my shopify new customers fall by 20% compared to the last 7 days.',
   ],
   googleads: [
      'Notify me if my Google Ads conversion rate drops below 3%.',
      'Alert me if my Google Ads Quality Score for any keyword drops below 5.',
      'Notify me when my Google Ads CPA increases by 10% in the last 14 days.',
   ],
   amazon_selling_partner: [
      'Set me an alert when Amazon sp net sales drop by 10% compared to last month',
      'Alert me if Amazon sp total page views fall by more than 20% in a week.',
      'Notify me when Amazon sp gross sales decrease by 15% compared to the previous 7 days.',
   ],
   amazon_ads: [
      'Set me an alert for Amazon Ads when the CTR drops below 0.5%.',
      'Notify me if Amazon Ads CPC increases by more than 20% compared to the last 7 days.',
      'Alert me when Amazon Ads ROAS falls below 5.',
   ],
};

export const CHANNEL_DISPLAY_NAMES: Record<string, string> = {
   web: 'web',
   facebookads: 'Meta Ads',
   store: 'shopify',
   googleads: 'Google Ads',
   amazon_selling_partner: 'Amazon SP',
   amazon_ads: 'Amazon Ads',
};

export const DATA_SOURCE = {
   web: 'Web',
   facebookads: 'Meta Ads',
   store: 'Store',
   googleads: 'Google Ads',
   amazon_selling_partner: 'Amazon Selling Partner',
   amazon_ads: 'Amazon Ads',
};

export const KPIS = {
   web: [
      'avg_pages_per_session',
      'conversion_rate',
      'avg_session_duration',
      'bounce_rate',
      'total_sessions',
      'total_users',
      'new_users',
      'cost_per_session',
   ],
   facebookads: [
      'cpv',
      'total_clicks',
      '95_percent_video_views',
      'leads',
      'ctr',
      'vtr',
      'cost_per_lead',
      'cpp',
      'cpc',
      'video_view',
      'cpm',
      'total_purchase',
      'roas',
      '100_percent_video_views',
      '50_percent_video_views',
      '25_percent_video_views',
      'total_impressions',
      'total_spent',
      '75_percent_video_views',
   ],
   shopify: [
      'average_order_value',
      'taxes',
      'returning_customers',
      'abandoned_checkout',
      'new_customers',
      'shipping_charges',
      'total_orders',
      'total_sales',
      'discounts',
   ],
   googleads: [
      'google_roas',
      'google_ctr',
      'google_total_impressions',
      'google_total_spend',
      'google_conversion_rate',
      'google_cpm',
      'google_cost_per_acquisition',
      'google_total_conversions',
      'google_cpc',
      'google_total_clicks',
   ],
};

export const COMPARATORS = [
   {
      key: 'Increasing',
      title: '🔼 upwards',
   },
   {
      key: 'Decreasing',
      title: '🔽 downwards',
   },
   {
      key: 'More_than',
      title: '📈 more than',
   },
   {
      key: 'Less_than',
      title: '📉 less than',
   },
];

export const REFERENCE = [
   { key: 'last_1_day', title: 'Last 1 Day', value: '1' },
   { key: 'last_3_days', title: 'Last 3 Days', value: '3' },
   { key: 'last_5_days', title: 'Last 5 Days', value: '5' },
   { key: 'last_7_days', title: 'Last 7 Days', value: '7' },
   { key: 'last_10_days', title: 'Last 10 Days', value: '10' },
   { key: 'last_14_days', title: 'Last 14 Days', value: '14' },
   { key: 'last_21_days', title: 'Last 21 Days', value: '21' },
   { key: 'last_30_days', title: 'Last 30 Days', value: '30' },
   { key: 'last_45_days', title: 'Last 45 Days', value: '45' },
   { key: 'last_60_days', title: 'Last 60 Days', value: '60' },
   { key: 'last_75_days', title: 'Last 75 Days', value: '75' },
   { key: 'last_90_days', title: 'Last 90 Days', value: '90' },
   { key: 'last_120_days', title: 'Last 120 Days', value: '120' },
   { key: 'last_180_days', title: 'Last 180 Days', value: '180' },
   { key: 'last_365_days', title: 'Last 365 Days', value: '365' },
];

export const sortReferenceByValue = (referenceArray: typeof REFERENCE) => {
   return referenceArray.sort((a, b) => Number(a.value) - Number(b.value));
};
