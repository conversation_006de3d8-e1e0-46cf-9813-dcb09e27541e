import { useState, useEffect } from 'react';
import pulseBackendEndpoints, {
   DynamicInsightRankData,
} from '../api/service/pulse';
import { useApiQuery } from './react-query-hooks';
import { Keys, LocalStorageService } from '../utils/local-storage';
import { AuthUser } from '../types/auth';
import { ApiError } from '../pages/social-listening/components/facebook-ads-form';

const userDetails = LocalStorageService.getItem<AuthUser>(
   Keys.FlableUserDetails,
);

const handleTrack = async ({
   insight_text,
   load_date,
}: {
   insight_text: string;
   load_date: string;
}) => {
   if (!userDetails?.client_id) return;

   try {
      await pulseBackendEndpoints.updateTrackWebInsight({
         client_id: userDetails.client_id,
         insight_text,
         load_date,
         tracked: true,
      });
   } catch (err: unknown) {
      const error = err as ApiError;
      console.log(error.response.data.message);
      alert(error.response.data.message);
   }
};

const fetchTrackedInsights = async () => {
   if (!userDetails?.client_id) return;

   try {
      const {
         data: { trackedInsights },
      } = await pulseBackendEndpoints.fetchTrackedWebInsights(
         userDetails.client_id,
      );
      return trackedInsights;
   } catch (err: unknown) {
      const error = err as ApiError;
      console.log(error.response.data.message);
      alert(error.response.data.message);
      return [];
   }
};

const useWebInsights = (clientId: string | undefined, selectedDays: string) => {
   const [insights, setInsights] = useState<DynamicInsightRankData[]>([]);
   const { data, isLoading } = useApiQuery({
      queryKey: ['Rank', selectedDays],
      queryFn: () =>
         pulseBackendEndpoints.fetchDynamicInsightsRank({
            client_id: clientId,
            days: selectedDays,
         }),
   });

   useEffect(() => {
      if (data && data.rankData) {
         setInsights(data.rankData);
      } else {
         setInsights([]);
      }
   }, [data]);

   return { insights, isLoading, handleTrack };
};

export { useWebInsights, handleTrack, fetchTrackedInsights };
