import { Button } from '@/components/ui/button';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { setCurrentSessionID as setAlertSessionID } from '@/store/reducer/alerting-agent-reducer';
import {
   setCurrentSessionID as setAnalyticsSessionID,
   setChunks,
} from '@/store/reducer/analytics-agent-reducer';
import { setCurrentAgent, Agents } from '@/store/reducer/marco-reducer';
import { useAppDispatch, useAppSelector } from '@/store/store';
import { useNavigate } from 'react-router-dom';
import MarcoHistoryPopover from '../popovers/marco-history-popover';
import { resetMetaAdsManagerState } from '@/store/reducer/meta-ads-manager-reducer';
import { startNewAutoAgentChat } from '@/store/reducer/metaAdsAutoAgentReducer';
const AGENTS: { value: Agents; label: string }[] = [
   { value: 'analytics-agent', label: 'Analytics Agent' },
   { value: 'alerting-agent', label: 'Alerting Agent' },
   { value: 'meta-ads-manager-agent', label: 'Meta Manager Agent' },
];

const MarcoHeader = () => {
   const dispatch = useAppDispatch();
   const navigate = useNavigate();

   const { currentAgent } = useAppSelector((state) => state.marco);
   // const { isAnalyticsAgentRunning } = useAppSelector(
   //    (state) => state.analyticsAgent,
   // );

   const AGENT_RESET_ACTIONS: Record<Agents, () => void> = {
      'analytics-agent': () => {
         dispatch(setAnalyticsSessionID(''));
         dispatch(setChunks([]));
      },
      'alerting-agent': () => dispatch(setAlertSessionID('')),
      'meta-ads-manager-agent': () => dispatch(resetMetaAdsManagerState()),
      'meta-ads-manager-auto': () => dispatch(startNewAutoAgentChat()),
   };

   const handleClickNewChat = () => {
      const resetAction = AGENT_RESET_ACTIONS[currentAgent];
      if (resetAction) resetAction();
   };

   return (
      <header className='flex mt-2 md:mt-4 w-full justify-between shrink-0 items-center gap-2 h-[52px]'>
         <Select
            defaultValue={
               currentAgent === 'meta-ads-manager-auto'
                  ? 'meta-ads-manager-agent'
                  : currentAgent
            }
            value={
               currentAgent === 'meta-ads-manager-auto'
                  ? 'meta-ads-manager-agent'
                  : currentAgent
            }
            onValueChange={(value) => {
               dispatch(setCurrentAgent(value as Agents));
               navigate('/marco/' + value);
            }}
         >
            <SelectTrigger className='w-fit max-w-[220px] ml-2 text-[14px] md:text-[16px] text-black font-bold border-0 shadow-none hover:cursor-pointer'>
               <SelectValue placeholder='Agent' />
            </SelectTrigger>
            <SelectContent className='bg-white'>
               {AGENTS.map((agent) => (
                  <SelectItem
                     key={agent.value}
                     className='hover:cursor-pointer hover:bg-gray-50 font-semibold'
                     value={agent.value}
                  >
                     {agent.label}
                  </SelectItem>
               ))}
            </SelectContent>
         </Select>
         <div className='flex gap-2 mr-6'>
            <Button
               className='text-[12px] md:text-[16px] text-black  hover:cursor-pointer hover:bg-[#3c76e1] hover:text-white disabled:cursor-not-allowed'
               variant='outline'
               onClick={handleClickNewChat}
               // disabled={
               //    currentAgent === 'analytics-agent' && isAnalyticsAgentRunning
               // }
            >
               New Chat
            </Button>
            <MarcoHistoryPopover />
         </div>
      </header>
   );
};

export default MarcoHeader;
