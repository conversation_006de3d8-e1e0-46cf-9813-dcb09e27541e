// .youtube {
//   width: 250px;
//   height: 250px;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   padding: 10px;

//   .youtube-btn {
//     background-color: crimson;
//     color: white;
//     padding: 7px;
//     border-radius: 20px;
//     font-size: 14px;
//   }

//   .connected-youtube {
//     padding: 20px;
//     display: flex;
//     flex-direction: column;
//     justify-content: center;
//     align-items: center;

//     .details {
//       margin-bottom: 20px;

//       .avatar {
//         width: 90px;
//         height: 90px;
//         border-radius: 50%;
//       }

//       .channel-title {
//         margin-top: 7px;
//       }

//       .channel-title-value {
//         margin-bottom: 10px;
//       }
//     }

//     .btn-disconnect {
//       background-color: red;
//     }
//   }

.input-container {
   width: 100%;

   .input-buttons-container {
      //  margin-top: 10px;
      display: flex;
      justify-content: center;
      gap: 20px;
      margin-bottom: 10px;
   }
   input {
      padding: 6px;
      border-radius: 4px;
      color: black;
      font-size: 12px;
      width: 80%;
      border: 1px solid white;
      margin-left: 10%;
   }

   .cancel-btn {
      background-color: red;
   }
}

.btn {
   padding: 5px 12px;
   color: white;
   background-color: darkgreen;
   margin-right: 4px;
   border-radius: 15px;
   font-size: 13px;
}
