import { Heading, Skeleton, Text, WrapItem } from '@chakra-ui/react';
import { FiPlusCircle } from 'react-icons/fi';
import { VStack } from '@chakra-ui/react';
import { Wrap } from '@chakra-ui/react';
import KPICard from './card';
import {
   CategoryProps,
   KpiCategoryKeys,
   KPIWrapperProps,
} from '../utils/interface';
import { useDispatch } from 'react-redux';
import { openModal } from '../../../store/reducer/modal-reducer';
import { modalTypes } from '../../../components/modals/modal-types';
import {
   CategoryChannel,
   KPICategory,
} from '../../../utils/strings/kpi-constants';
import { filterVisibleKPI } from '../utils/helpers';
import { BsPinFill } from 'react-icons/bs';
import { GrCircleQuestion } from 'react-icons/gr';
import KPIImage from './kpi-image';
import TooltipContent from './tooltip-content';
import { useColorModeValue } from '@chakra-ui/react';
import { useAppSelector } from '../../../store/store';

function Category(props: CategoryProps) {
   const { head, data, prevData, loading, metaData, id, anomaly } = props;
   if (!data) return null;
   const dispatch = useDispatch();
   const key: KpiCategoryKeys = head.trim() as KpiCategoryKeys;
   const { filteredCat, filteredMeta } = filterVisibleKPI(
      head == 'pinned',
      data,
      metaData,
      head,
   );
   const handleAdd = () => {
      dispatch(
         openModal({
            modalType: modalTypes.ADD_KPI_MODAL,
            modalProps: { metaData: filteredMeta, head },
         }),
      );
   };
   const headingColor = useColorModeValue('gray.800', 'white');
   const imageStyles: object = {
      height: key == 'amazon_ads' ? '20px' : '25px',
      width: key == 'amazon_ads' ? '30px' : '25px',
      position: key == 'amazon_ads' ? 'relative' : '',
      top: key == 'amazon_ads' ? '3px' : '',
   };
   return (
      <VStack alignItems={'flex-start'} gap={5}>
         <Heading
            id={id}
            fontSize={18}
            fontWeight={600}
            textAlign={'left'}
            display={'flex'}
            alignItems={'center'}
            gap={2}
            color={headingColor}
         >
            {head == 'pinned' ? (
               <BsPinFill />
            ) : (
               <KPIImage kpiCat={head} style={imageStyles} />
            )}{' '}
            {KPICategory[key] || head}{' '}
            <TooltipContent category={key.toString()} hasArrow placement='top'>
               <GrCircleQuestion />
            </TooltipContent>
            <TooltipContent label='Add more KPIs' hasArrow placement='top'>
               <FiPlusCircle cursor={'pointer'} onClick={handleAdd} />{' '}
            </TooltipContent>
         </Heading>
         <KPIWrapper
            head={head}
            data={filteredCat}
            prevData={prevData}
            loading={loading}
            anomaly={anomaly!}
         />
      </VStack>
   );
}

function KPIWrapper(props: KPIWrapperProps) {
   const { loading, data, prevData, head, anomaly } = props;
   const { connectionDetails } = useAppSelector((state) => state.media);
   let connected = false;
   connectionDetails.forEach((x) => {
      if (head == 'pinned' || head == 'overall_metrics') connected = true;
      else if (CategoryChannel[head] == x.channel_name) {
         if (x.is_active) connected = true;
      }
   });
   if (!connected)
      return (
         <Text ml={5}> Please integrate {KPICategory[head]} to see data.</Text>
      );
   if (loading?.first) {
      const data = [1, 2, 3, 4, 5, 6];
      const boxShadowColor = useColorModeValue('#cccccc33', '#00000066');
      const bgColor = useColorModeValue('white', 'grey.800');

      return (
         <>
            <Wrap spacing='10px' width={'100%'}>
               {data.map((x) => (
                  <WrapItem
                     key={x}
                     minWidth={100}
                     minHeight={200}
                     boxShadow={`1px 1px 10px 1px ${boxShadowColor}`}
                     padding={5}
                     borderRadius={5}
                     className='kpi-item'
                     position={'relative'}
                     bg={bgColor}
                  >
                     <Skeleton width={'100%'} height={'100%'} />
                  </WrapItem>
               ))}
            </Wrap>
            <Wrap spacing='10px' width={'100%'}>
               {data.slice(0, 3).map((x) => (
                  <WrapItem
                     key={x}
                     minWidth={100}
                     minHeight={200}
                     boxShadow={`1px 1px 10px 1px ${boxShadowColor}`}
                     padding={5}
                     borderRadius={5}
                     className='kpi-item'
                     position={'relative'}
                     bg={bgColor}
                  >
                     <Skeleton width={'100%'} height={'100%'} />
                  </WrapItem>
               ))}
            </Wrap>
         </>
      );
   } else if ((!data || Object.keys(data).length == 0) && head !== 'pinned')
      return <Text ml={5}> No data available for selected date range.</Text>;

   return (
      <Wrap spacing='20px' width={'100%'}>
         {Object.entries(data)
            .sort((a, b) => (a[1].order || 1) - (b[1].order || 1))
            .map((kpi, idx) => {
               if (!kpi[1]) return null;
               return (
                  <KPICard
                     key={idx}
                     head={head}
                     loading={loading}
                     kpiDetails={kpi[1]}
                     anomaly={
                        anomaly &&
                        anomaly['has_data'] &&
                        anomaly['anomalies'] &&
                        anomaly['anomalies'][kpi[1]['kpi_names']]
                     }
                     prevKpi={prevData[kpi[0]] || {}}
                  />
               );
            })}
      </Wrap>
   );
}

export default Category;
