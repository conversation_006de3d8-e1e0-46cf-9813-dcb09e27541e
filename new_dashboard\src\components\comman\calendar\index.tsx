import React, { useState } from 'react';
import { Box, Input, useToast } from '@chakra-ui/react';

import { toastMessage } from '../../../utils/strings/content-manager';

interface CalendarProps {
   selectedDate: Date | string | null;
   setSelectedDate: (date: Date | string) => void;
}

const formatDate = (date: Date) => {
   const year = date.getFullYear();
   const month = String(date.getMonth() + 1).padStart(2, '0');
   const day = String(date.getDate()).padStart(2, '0');
   const hours = String(date.getHours()).padStart(2, '0');
   const minutes = String(date.getMinutes()).padStart(2, '0');
   return `${year}-${month}-${day}T${hours}:${minutes}`;
};

const Calendar: React.FC<CalendarProps> = ({
   selectedDate,
   setSelectedDate,
}) => {
   const [date, setDate] = useState<string | Date>(
      selectedDate ? new Date(selectedDate) : new Date(),
   );
   const toast = useToast();
   const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const currentDate = new Date();
      const selectedDate = new Date(e.target.value);
      if (selectedDate < currentDate) {
         toast({
            title: toastMessage.dateMessage.title,
            description: toastMessage.dateMessage.description,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
         setDate('');
      } else {
         setDate(e.target.value);
         setSelectedDate(e.target.value);
      }
   };

   return (
      <Box width={{ sm: '150px', md: '200px', lg: '250px' }}>
         <Input
            type='datetime-local'
            value={formatDate(typeof date === 'string' ? new Date(date) : date)}
            onChange={handleDateChange}
            min={formatDate(new Date())}
         />
      </Box>
   );
};

export default Calendar;
