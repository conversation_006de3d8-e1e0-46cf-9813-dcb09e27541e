import {
   getFormattedVal,
   getMetricDescription,
   toHHMMSS,
} from '../../pages/dashboard/utils/helpers';
import { PerformanceChartData } from '../chatbox/interface';
import './kpi-table.scss';
interface KPITableProps {
   performanceData: PerformanceChartData;
}
function KPITable(props: KPITableProps) {
   const { performanceData } = props;

   const { fields } = performanceData.schema;
   const xAxisField = fields[1].name;
   const validFields = fields.filter((f) => !f.name.includes('cumulative'));
   return (
      <div style={{ overflow: 'auto', maxHeight: '40vh' }}>
         <table className='kpi-table'>
            <thead>
               <tr>
                  {validFields.slice(1).map((field, i) => (
                     <th
                        key={field.name}
                        style={{ width: i == 0 ? '100px' : '' }}
                     >
                        {getMetricDescription(field.name)}
                     </th>
                  ))}
               </tr>
            </thead>
            <tbody>
               {performanceData.data.map((d, idx) => {
                  return (
                     <tr key={idx}>
                        <td>{d[xAxisField]}</td>
                        {validFields.slice(2).map((field) => {
                           const showVal =
                              field.type == 'number'
                                 ? getFormattedVal(
                                      Math.round(Number(d[field.name]) * 100) /
                                         100,
                                   )
                                 : toHHMMSS(Number(d[field.name]));
                           return <td key={field.name}>{showVal}</td>;
                        })}
                     </tr>
                  );
               })}
            </tbody>
         </table>
      </div>
   );
}

export default KPITable;
