import dashboardApiAgent from '../../agent';

interface deleteContentCalendarData {
   uuid: string;
}

interface deleteResponse {
   acknowledged: boolean;
   deletedCount: number;
}

interface decodePayload {
   image_url: string;
}

interface encodeResponse {
   uri: string;
}

export interface Media {
   id: string;
   oauthToken: string;
   oauthTokenSecret: string;
   screenName: string;
   social_media: string;
   userID: string;
   clientID: string;
}
export interface PostAuthMediaResponse {
   data: Media[];
}

const deleteContentCalendar = async (data: deleteContentCalendarData) => {
   try {
      const response = await dashboardApiAgent.post('/twitter/delete', data);
      if (!response.data) {
         throw new Error('Failed to delete content calendar');
      }
      return response.data as deleteResponse;
   } catch (error) {
      console.error('Error fetching data:', error);
      return null;
   }
};

const decode = async (data: decodePayload) => {
   try {
      const response = await dashboardApiAgent.post('/azureblob/decode', data);
      if (!response.data) {
         throw new Error('Failed to decode image');
      }
      return response.data as encodeResponse;
   } catch (error) {
      console.error('Error fetching data:', error);
      return null;
   }
};

const encode = async (data: FormData) => {
   console.log('data file', data);
   try {
      const response = await dashboardApiAgent.post('/azureblob/encode', data, {
         headers: {
            'Content-Type': 'multipart/form-data',
         },
      });
      if (!response) {
         throw new Error('Failed to encode image');
      }
      return response.data as encodeResponse;
   } catch (error) {
      console.error('Error fetching data:', error);
      return null;
   }
};

export { deleteContentCalendar, decode, encode };
