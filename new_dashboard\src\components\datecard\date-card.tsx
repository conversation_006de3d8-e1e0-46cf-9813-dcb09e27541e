import socialWatchEndpoints from '../../api/service/social-watch/apis';

import { useState, ChangeEvent, useEffect } from 'react';
import {
   Box,
   Select,
   Text,
   Flex,
   Button,
   useToast,
   Switch,
   useColorModeValue,
} from '@chakra-ui/react';

import { resetAiText } from '../../store/reducer/configReducer';
import { useAppSelector } from '../../store/store';
import { v4 as uuid } from 'uuid';
import { useDispatch } from 'react-redux';
import { Keys, LocalStorageService } from '../../utils/local-storage';
import {
   resetMultiMedia,
   resetRawFiles,
} from '../../store/reducer/user-details-reducer';
import { AuthUser } from '../../types/auth';
import { PageInfo } from '../../types/social-watch';
import GetPostBottomSection from './post-bottom';
import { BackendError } from '../../types/common';
import { TooltipPosition } from 'intro.js/src/packages/tooltip';
import { componentNames, setFlag } from '../../store/reducer/tour-reducer';
import introJs from 'intro.js';
export interface ScheduledTime {
   dateTime: Date | string;
   media: string;
   id?: string;
}
function DateCard() {
   const [selected, setSelected] = useState<string>('Specific days and times');
   const [selectedPage, setSelectedPage] = useState<PageInfo | null>(null);
   const [buttonText, setButtonText] = useState<string>('Save to Calendar');
   const [isLoading, setIsLoading] = useState<boolean>(false);
   const [is_schedule, setIsSchedule] = useState(true);
   const dynamicSocialMedia = useAppSelector((state) => state.media);
   const { selectedTab } = useAppSelector((state) => state.media);
   const { connections } = useAppSelector((state) => state.integration);
   const aiText = useAppSelector((state) => state.config.aiText);

   const [scheduledTime, setScheduledTime] = useState<ScheduledTime[]>([
      { media: selectedTab, dateTime: new Date() },
   ]);
   const { contentCreation } = useAppSelector((state) => state.tour);

   const dispatch = useDispatch();
   const toast = useToast();

   function resetFields() {
      setScheduledTime([]);
      dispatch(resetAiText());
      dispatch(resetMultiMedia());
      dispatch(resetRawFiles());
   }

   const userDetails = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   );

   const handleChange = (event: ChangeEvent<HTMLSelectElement>) => {
      if (!event.target.value) return;
      setSelected(event.target.value);
      if (event.target.value === 'Post now') {
         setButtonText('Post Now');
         setIsSchedule(false);
      } else {
         setIsSchedule(true);
         setButtonText('Save to Calendar');
      }
   };

   const saveToCalendar = async ({ media, dateTime }: ScheduledTime) => {
      if (!aiText[media]) {
         return toast({
            title: 'Error!',
            description: `Please add content for ${media}.`,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      }
      if (!dateTime) {
         return toast({
            title: 'Error!',
            description: `Please select date and time for ${media}.`,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      }
      if (media == 'linkedin' && !selectedPage) {
         return toast({
            title: 'Error!',
            description: `Please select where to post for ${media}.`,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      }
      setIsLoading(true);
      try {
         const date = new Date(dateTime);
         const time = new Date(dateTime).toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false,
         });

         const payload = {
            time,
            date: `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`,
            social_media_type: media,
            client_id: userDetails!.client_id,
            post_data: aiText[media],
            media:
               dynamicSocialMedia.multiMedia[media]?.map(
                  ({ image_link, media_id }) => ({ image_link, media_id }),
               ) || [],
            is_schedule,
            uuid: uuid(),
            linkedinPageId: selectedPage?.page ? selectedPage.id : null,
         };

         const { data } =
            await socialWatchEndpoints.postContentCalendar(payload);

         setIsLoading(false);
         resetFields();
         toast({
            title: data.message,
            description: `Operation completed successfully for ${media}.`,
            status: 'success',
            duration: 5000,
            isClosable: true,
         });
      } catch (error) {
         setIsLoading(false);
         console.error(error);
         toast({
            title: 'Error!',
            description: `Operation failed for ${media}.`,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      }
   };

   const postNow = async () => {
      try {
         const { selectedTab } = dynamicSocialMedia;
         if (!aiText[selectedTab]) {
            return toast({
               title: 'Error!',
               description: `Please add content for ${selectedTab}.`,
               status: 'error',
               duration: 5000,
               isClosable: true,
            });
         }
         if (selectedTab == 'linkedin' && !selectedPage) {
            return toast({
               title: 'Error!',
               description: `Please select where to post for ${selectedTab}.`,
               status: 'error',
               duration: 5000,
               isClosable: true,
            });
         }
         let msg;

         setIsLoading(true);

         if (selectedTab === 'twitter' && connections.twitter) {
            const { oauthToken, oauthTokenSecret } = connections.twitter;
            const payload = {
               oauthToken,
               oauthTokenSecret,
               tweets: aiText['twitter'],
               ...(dynamicSocialMedia.multiMedia[selectedTab]?.length > 0
                  ? {
                       mediaId: dynamicSocialMedia.multiMedia[selectedTab]
                          .map((item) => item.media_id)
                          .join(','),
                    }
                  : {}),
            };
            await socialWatchEndpoints.postTweets(payload);
            msg = 'Successfully posted on twitter';
         } else if (selectedTab === 'linkedin' && connections.linkedin) {
            const { user } = connections.linkedin;

            await socialWatchEndpoints.postContentOnLinkedin({
               pageId: selectedPage?.page ? selectedPage.id : null,
               token: user.access_token,
               content: aiText['linkedin'],
               linkedinUserId: user.userId,
               ...(dynamicSocialMedia.multiMedia[selectedTab]?.length > 0
                  ? {
                       mediaId: dynamicSocialMedia.multiMedia[selectedTab]
                          .map((item) => item.media_id)
                          .join(','),
                    }
                  : {}),
            });
            msg = 'Successfully posted on linkedin';
         }

         toast({
            title: msg,
            description: 'Operation completed successfully.',
            status: 'success',
            duration: 5000,
            isClosable: true,
         });
         // Need to check which tab is selected, based on that you have to make the call:

         resetFields();
      } catch (error) {
         const err = error as BackendError;
         toast({
            title: 'Error!',
            description: err.response.data.message,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      } finally {
         setIsLoading(false);
      }
   };

   const handleTweet = async () => {
      if (buttonText === 'Post Now') {
         await postNow();
      } else if (buttonText === 'Save to Calendar') {
         scheduledTime.forEach((scheduled) => {
            saveToCalendar(scheduled).catch(console.log);
         });
      }
   };

   const handlePageSelect = (e: ChangeEvent<HTMLSelectElement>) => {
      if (!e.target.value) return setSelectedPage(null);
      setSelectedPage(
         connections.linkedin?.pages.find(
            (page) => page.id === e.target.value,
         ) || null,
      );
   };
   const intro = introJs();

   const steps: {
      element: string;
      intro: string;
      position: TooltipPosition;
   }[] = [
      {
         element: '#aiAssist',
         intro: 'Regenerate your content with AI Assist.',
         position: 'top',
      },
      {
         element: '#selectdDays',
         intro: 'Plan when you want to post your content.',
         position: 'top',
      },

      {
         element: '#selectPost',
         intro: 'Choose which social platform to post your content.',
         position: 'top',
      },
      {
         element: '#saveCalendarButton',
         intro: 'Save the content to your social media content calendar to auto post on the date and time scheduled.',
         position: 'top',
      },
   ];
   const startTour = () => {
      intro.setOptions({ steps });
      void intro.start();

      dispatch(
         setFlag({ componentName: componentNames.WEB_INSIGHT, flag: false }),
      );
   };
   useEffect(() => {
      if (contentCreation) startTour();
   }, [contentCreation]);
   return (
      <>
         <Box border='1px solid #A0A4A888' borderRadius='md' mt={6}>
            <Flex
               direction='row'
               align='center'
               justifyContent={'center'}
               m={4}
               gap={3}
            >
               <Flex gap={3} flex={1} alignItems={'center'}>
                  <Text
                     fontSize={{ base: 'xs', sm: 'sm', md: 'md', lg: 'lg' }}
                     color={useColorModeValue('#000000', '#FFFFFF')}
                     w='18%'
                  >
                     When to post
                  </Text>
                  <Select
                     placeholder=''
                     width='30%'
                     onChange={handleChange}
                     value={selected}
                     id='selectdDays'
                  >
                     <option value='Specific days and times'>
                        Specific days and times
                     </option>
                     <option value='Post now'>Post now</option>
                  </Select>
                  {selectedTab == 'linkedin' && selected == 'Post now' && (
                     <Select
                        placeholder='Where to Post'
                        width={'30%'}
                        value={selectedPage?.id}
                        onChange={handlePageSelect}
                     >
                        {connections.linkedin?.pages.map((page) => (
                           <option key={page.id} value={page.id}>
                              {page.name}
                              {`${!page.page ? '  (Self)' : ''}`}
                           </option>
                        ))}
                     </Select>
                  )}
               </Flex>
               <Box display='flex' alignItems='center' ml={5}>
                  <Switch
                     disabled={buttonText === 'Post Now'}
                     isChecked={is_schedule}
                     onChange={() => setIsSchedule(!is_schedule)}
                  />
                  <Text ml={2} color={is_schedule ? 'yellow.500' : 'blue.500'}>
                     {is_schedule ? 'Scheduled' : 'In Draft'}
                  </Text>
               </Box>
            </Flex>
            <Box
               mt={4}
               mb={4}
               style={{
                  borderBottom: '1px solid #A0A4A888',
               }}
            />

            <GetPostBottomSection
               id='selectPost'
               selected={selected}
               scheduledTime={scheduledTime}
               setScheduledTime={setScheduledTime}
               selectedPage={selectedPage}
               setSelectedPage={setSelectedPage}
            />
         </Box>
         <Flex justifyContent='flex-end' m={6} mr={0}>
            <Button
               id='saveCalendarButton'
               background='#437EEB'
               color={'white'}
               padding={'6px 30px'}
               fontSize={'14px'}
               fontWeight={'500'}
               onClick={() => {
                  void handleTweet();
               }}
               _hover={{ background: '#437EEBEE', scale: 1.05 }}
               isLoading={isLoading}
               spinnerPlacement='start'
               isDisabled={!selected}
            >
               {buttonText}
            </Button>
         </Flex>
      </>
   );
}

export default DateCard;
