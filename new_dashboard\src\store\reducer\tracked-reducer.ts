import { createSlice, PayloadAction } from '@reduxjs/toolkit';

const data: Card[] = [
   {
      id: 1,
      heading: 'Test1',
      subHeading: 'SubHeading1',
      campaign_name: 'Name1',
      campaign_status: 'status1',
      kpi: 'kpi1',
   },
   {
      id: 2,
      heading: 'Test2',
      subHeading: 'SubHeading2',
      campaign_name: 'Name2',
      campaign_status: 'status2',
      kpi: 'kpi2',
   },
   {
      id: 3,
      heading: 'Test3',
      subHeading: 'SubHeading3',
      campaign_name: 'Name3',
      campaign_status: 'status3',
      kpi: 'kpi3',
   },
];

export interface Card {
   id: number;
   heading: string;
   subHeading?: string;
   campaign_name: string;
   campaign_status: string;
   kpi: string;
}

interface TrackedState {
   trackedIds: number[];
   data: Card[];
}

const initialState: TrackedState = {
   trackedIds: [],
   data,
};

const trackedSlice = createSlice({
   name: 'tracked',
   initialState,
   reducers: {
      addCard: (state, action: PayloadAction<number>) => {
         state.trackedIds.push(action.payload);
      },
      removeCard: (state, action: PayloadAction<number>) => {
         state.trackedIds = state.trackedIds.filter(
            (id) => id !== action.payload,
         );
      },
   },
});

export const { addCard, removeCard } = trackedSlice.actions;
export default trackedSlice.reducer;
