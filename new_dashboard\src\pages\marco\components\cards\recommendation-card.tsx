import { FaLightbulb } from 'react-icons/fa';

import { Card, Flex, Heading, Text } from '@chakra-ui/react';

import { Box } from '@chakra-ui/react';

import React from 'react';

export const RecommendationCard = React.memo(
   ({ recommendation }: { recommendation: string }) => (
      <Box width='100%' maxW='900px'>
         <Card p={6} boxShadow='md' borderRadius='xl' bg='white'>
            <Flex align='center' mb={4}>
               <Box bg='blue.50' p={2} borderRadius='md' mr={3}>
                  <FaLightbulb color='#3182CE' size={20} />
               </Box>
               <Heading size='md' color='blue.600'>
                  Recommendation
               </Heading>
            </Flex>
            <Box
               p={4}
               bg='blue.50'
               borderRadius='lg'
               borderLeft='4px solid'
               borderColor='blue.500'
               position='relative'
            >
               <Box
                  position='absolute'
                  top='-10px'
                  left='-10px'
                  bg='white'
                  p={2}
                  borderRadius='full'
                  boxShadow='md'
               >
                  <FaLightbulb color='#3182CE' size={16} />
               </Box>
               <Text fontSize='md' color='gray.700' lineHeight='1.6' pl={2}>
                  {recommendation}
               </Text>
            </Box>
         </Card>
      </Box>
   ),
);
