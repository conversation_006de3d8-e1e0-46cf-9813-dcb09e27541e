import * as React from 'react';
import * as TooltipPrimitive from '@radix-ui/react-tooltip';

import { cn } from '@/utils/index';

function TooltipProvider({
   delayDuration = 0,
   ...props
}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {
   return (
      <TooltipPrimitive.Provider
         data-slot='tooltip-provider'
         delayDuration={delayDuration}
         {...props}
      />
   );
}

function Tooltip({
   ...props
}: React.ComponentProps<typeof TooltipPrimitive.Root>) {
   return (
      <TooltipProvider>
         <TooltipPrimitive.Root data-slot='tooltip' {...props} />
      </TooltipProvider>
   );
}

function TooltipTrigger({
   ...props
}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {
   return <TooltipPrimitive.Trigger data-slot='tooltip-trigger' {...props} />;
}

function TooltipContent({
   className,
   sideOffset = 0,
   children,
   ...props
}: React.ComponentProps<typeof TooltipPrimitive.Content>) {
   return (
      <TooltipPrimitive.Portal>
         <TooltipPrimitive.Content
            data-slot='tooltip-content'
            sideOffset={sideOffset}
            className={cn(className)}
            {...props}
         >
            {children}
            <TooltipPrimitive.Arrow className='bg-tra shadow-md fill-white z-50 size-2.5 translate-y-[calc(-40%_-_0px)] rotate-0 rounded-[0px]' />
         </TooltipPrimitive.Content>
      </TooltipPrimitive.Portal>
   );
}

export { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider };
