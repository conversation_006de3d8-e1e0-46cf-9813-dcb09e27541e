import { AuthUser } from '@/types/auth';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import { PayloadAction, createSlice } from '@reduxjs/toolkit';

export interface KPIRules {
   metric: string;
   trend: 'increasing' | 'decreasing' | '';
   value: string;
   value_type: 'absolute' | 'percentage' | '';
   comparison: 'more_than' | 'less_than' | 'equal_to' | '';
}

export interface CustomAlertForm {
   alert_id: string | null;
   alert_name: string;
   alert_description: string;
   email_recipients: { email: string; valid: boolean }[];
   alert_time: string;
   alert_status: 'active' | 'inactive';
   channel: string;
   campaigns: { id: string; name: string }[];
   metrics: string[];
   target_period: { value: string; label: string };
   reference_period: { value: string; label: string };
   kpi_rules: KPIRules[];
}

interface InitialState {
   form: CustomAlertForm;
   editMode: boolean;
   deleteMultipleAlerts: boolean;
}

export type FormFields = keyof InitialState['form'];

const { email } = (LocalStorageService.getItem(
   Keys.FlableUserDetails,
) as AuthUser) || { email: '' };

const initialState: InitialState = {
   form: {
      alert_id: null,
      alert_name: '',
      alert_description: '',
      email_recipients: [{ email, valid: true }],
      alert_time: '09:00:00',
      alert_status: 'active',
      channel: '',
      campaigns: [],
      metrics: [],
      target_period: { value: '', label: '' },
      reference_period: { value: '', label: '' },
      kpi_rules: [],
   },
   editMode: false,
   deleteMultipleAlerts: false,
};

const customAlertsSlice = createSlice({
   name: 'customAlerts',
   initialState,
   reducers: {
      handleFormChange: (
         state,
         action: PayloadAction<{
            name: keyof InitialState['form'];
            value:
               | string
               | { id: string; name: string }[]
               | { email: string; valid: boolean }[]
               | string[]
               | { value: string; label: string }
               | KPIRules[];
         }>,
      ) => {
         const { name, value } = action.payload;
         switch (name) {
            case 'alert_name':
            case 'alert_description':
            case 'alert_time':
            case 'alert_id':
               state.form[name] = value as string;
               break;
            case 'email_recipients':
               state.form.email_recipients = value as {
                  email: string;
                  valid: boolean;
               }[];
               break;
            case 'alert_status':
               state.form[name] = value as 'active' | 'inactive';
               break;
            case 'channel':
               state.form[name] = value as string;
               break;
            case 'campaigns':
               state.form[name] = value as { id: string; name: string }[];
               break;
            case 'metrics':
               state.form[name] = value as string[];
               break;
            case 'target_period':
            case 'reference_period':
               state.form[name] = value as { value: string; label: string };
               break;
            case 'kpi_rules':
               state.form[name] = value as KPIRules[];
               break;
            default:
               break;
         }
      },

      resetForm: (state) => {
         state.form = initialState.form;
      },

      setEditMode: (state, action: PayloadAction<boolean>) => {
         state.editMode = action.payload;
      },

      setDeleteMultipleAlerts: (state, action: PayloadAction<boolean>) => {
         state.deleteMultipleAlerts = action.payload;
      },
   },
});

export const {
   handleFormChange,
   resetForm,
   setEditMode,
   setDeleteMultipleAlerts,
} = customAlertsSlice.actions;

export default customAlertsSlice.reducer;
