/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { useNavigate, useLocation } from 'react-router-dom';
import { useEffect, useState } from 'react';
import Card from './Card';
import image from '../images/integrations/shopify.png';
import darkImage from '../../../assets/icons/kpi/shopify.png';

import { channelNames } from '../utils/constant';
import endPoints from '../apis/agent';
import { connectDisconnectToShopify } from '../utils';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { AuthUser } from '../../../types/auth';
import { useColorMode } from '@chakra-ui/react';

interface ConnectionDetails {
   is_active?: boolean;
   store?: string;
}

const Shopify = () => {
   const navigate = useNavigate();
   const location = useLocation();
   const { colorMode } = useColorMode();

   const client_id = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   )?.client_id;
   const [connectionDetails, setConnectionDetails] =
      useState<ConnectionDetails>({});
   const [isFetching, setIsFetching] = useState<boolean>(false);

   function handleNavigation() {
      if (location.pathname === '/onboarding') {
         navigate('/onboarding/shopify');
      } else {
         navigate('/integrations/shopify');
      }
   }

   // Fetch the connection details and show whether it's connected.
   useEffect(() => {
      if (!client_id) return;
      const fetchData = async () => {
         try {
            setIsFetching(true);
            const {
               data: { details },
            } = await endPoints.checkConnectionDetails({
               client_id,
               channel_name: channelNames.SHOPIFY,
            });
            const { is_active, store } = details || {};
            setConnectionDetails({ is_active, store });
         } catch (error) {
            console.error('Error fetching connection details:', error);
         } finally {
            setIsFetching(false);
         }
      };

      void fetchData();
   }, []);

   async function handleDisconnect() {
      if (!client_id) return;
      // Disconnect the user and reset the button status
      try {
         const isConfirmed = confirm('Are you sure?');

         if (!isConfirmed) return;

         await connectDisconnectToShopify({
            channel_name: channelNames.SHOPIFY,
            client_id,
            isConnect: false,
         });
         setConnectionDetails({});
      } catch (err) {
         console.log(err);
      }
   }

   return (
      <Card
         isConnected={connectionDetails.is_active}
         isFetching={isFetching}
         heading={connectionDetails.store ? connectionDetails.store : 'Shopify'}
         src={colorMode === 'dark' ? darkImage : image}
         onButtonClick={
            connectionDetails.is_active ? handleDisconnect : handleNavigation
         }
      />
   );
};

export default Shopify;
