import { AlertingAgentChat } from '@/api/service/agentic-workflow/alerting-agent';
import { UserSocialDetails } from '../../../../api/service/onboarding';
import {
   parseISO,
   isToday,
   isYesterday,
   subDays,
   subMonths,
   subYears,
   isAfter,
} from 'date-fns';

export const splitAndUppercaseString = (str: string) => {
   return str
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
};

export const getValue = (value: string, defaultValue = '') =>
   !value || value === 'missing' ? defaultValue : value;

export const getSuggestionSubset = (suggestions: string[], count: number) => {
   const shuffled = [...suggestions].sort(() => 0.5 - Math.random());
   return shuffled.slice(0, count);
};

export const getSmartSuggestions = (
   connections: UserSocialDetails[],
   suggestionsMap: {
      web: string[];
      facebookads: string[];
      store: string[];
      googleads: string[];
      amazon_selling_partner: string[];
      amazon_ads: string[];
   },
) => {
   const aliasMap: Record<string, string> = {
      shopify: 'store',
      flable_pixel: 'web',
   };

   const available = Array.from(
      new Set(
         connections.map(
            ({ channel_name }) => aliasMap[channel_name] || channel_name,
         ),
      ),
   ).filter(
      (ch) =>
         suggestionsMap[ch as keyof typeof suggestionsMap] &&
         suggestionsMap[ch as keyof typeof suggestionsMap].length > 0,
   );

   const result: string[] = [];

   if (available.length === 1) {
      result.push(
         ...getSuggestionSubset(
            suggestionsMap[available[0] as keyof typeof suggestionsMap],
            3,
         ),
      );
   } else if (available.length === 2) {
      result.push(
         ...getSuggestionSubset(
            suggestionsMap[available[0] as keyof typeof suggestionsMap],
            2,
         ),
      );
      result.push(
         ...getSuggestionSubset(
            suggestionsMap[available[1] as keyof typeof suggestionsMap],
            2,
         ),
      );
   } else if (available.length === 3) {
      result.push(
         ...getSuggestionSubset(
            suggestionsMap[available[0] as keyof typeof suggestionsMap],
            1,
         ),
      );
      result.push(
         ...getSuggestionSubset(
            suggestionsMap[available[1] as keyof typeof suggestionsMap],
            1,
         ),
      );
      result.push(
         ...getSuggestionSubset(
            suggestionsMap[available[2] as keyof typeof suggestionsMap],
            1,
         ),
      );

      const extraSource = getSuggestionSubset(available, 1)[0];
      result.push(
         ...getSuggestionSubset(
            suggestionsMap[extraSource as keyof typeof suggestionsMap],
            1,
         ),
      );
   } else {
      for (let i = 0; i < Math.min(4, available.length); i++) {
         result.push(
            ...getSuggestionSubset(
               suggestionsMap[available[i] as keyof typeof suggestionsMap],
               1,
            ),
         );
      }
   }

   return result;
};

export const groupAlertChatsByDate = (chatList: AlertingAgentChat[]) => {
   const grouped: { [key: string]: AlertingAgentChat[] } = {
      today: [],
      yesterday: [],
      last7Days: [],
      lastMonth: [],
      lastYear: [],
      older: [],
   };

   const now = new Date();

   for (const chat of chatList) {
      const date = parseISO(chat.updated_at);

      if (isToday(date)) {
         grouped.today.push(chat);
      } else if (isYesterday(date)) {
         grouped.yesterday.push(chat);
      } else if (isAfter(date, subDays(now, 7))) {
         grouped.last7Days.push(chat);
      } else if (isAfter(date, subMonths(now, 1))) {
         grouped.lastMonth.push(chat);
      } else if (isAfter(date, subYears(now, 1))) {
         grouped.lastYear.push(chat);
      } else {
         grouped.older.push(chat);
      }
   }

   for (const key in grouped) {
      grouped[key].sort((a, b) => {
         const dateA = parseISO(a.updated_at).getTime();
         const dateB = parseISO(b.updated_at).getTime();
         return dateB - dateA; // Descending
      });
   }

   return grouped;
};
