import axios, { AxiosResponse, AxiosError } from 'axios';
import { Payload } from '../../../components/chatbox/interface';
import Config from '../../../config';

export interface UserDetails {
   client_id: string;
   email: string;
}

export interface ChatMessage {
   id: string;
   userask: string;
   reply: { text: string; image: string | object; selectedMode: string };
   created_at?: string;
   updated_at?: string;
   feedback?: string;
}

export interface Session {
   session_id: string;
   summary: string;
   title: string;
   chat: ChatMessage[];
   created_at: string;
   updated_at: string;
}

export interface PostMarcoPayload {
   client_id: string;
   user: string;
   sessionId: string;
   chat: ChatMessage[];
}

export interface MarcoHistoryResponse {
   total_items: number;
   page: number;
   limit: number;
   history: Session[];
}

export interface UpdateTitlePayload {
   client_id: string;
   user: string;
   session_id: string;
   new_title: string;
}

export interface UpdateTitleResponse {
   status: number;
   message: string;
}

export interface userFeedback {
   client_id: string;
   session_id: string;
   chat_id: string;
   user_feedback: string;
}

type PostChatResponse = string | string[];

export const marcoClient = axios.create({
   baseURL: Config.VITE_MARCO_API,
   headers: {
      'Content-Type': 'application/json',
   },
   timeout: 1000 * 60,
});

export const deleteMarcoHistory = async (
   sessionId: string,
   userDetails: UserDetails,
) => {
   try {
      const response = await marcoClient.delete(
         `/history/${sessionId}?client_id=${userDetails.client_id}&user=${userDetails.email}`,
      );
      if (!response.data) {
         throw new Error(`Failed to delete session ${sessionId}`);
      }
      return 'Deleted';
   } catch (error) {
      console.error('Error fetching data:', error);
      return null;
   }
};

export const getMarcoHistory = async (
   userDetails: UserDetails,
   page: number,
   limit = 15,
): Promise<MarcoHistoryResponse | null | string> => {
   try {
      const response: AxiosResponse<MarcoHistoryResponse> =
         await marcoClient.get('/history', {
            params: {
               client_id: userDetails.client_id,
               user: userDetails.email,
               page,
               limit,
            },
         });

      if (!response.data) {
         throw new Error('Failed to fetch data');
      }
      return response.data;
   } catch (error) {
      console.error('Error fetching data:', error);
      return null;
   }
};
export const postMarcoHistory = async (payload: PostMarcoPayload) => {
   try {
      const response = await marcoClient.post('/history', payload);
      return response;
   } catch (error) {
      console.error('Error fetching data:', error);
      return null;
   }
};

export const chatWithMarco = async (payload: Payload) => {
   try {
      const response: AxiosResponse<PostChatResponse> = await marcoClient.post(
         '/chat',
         payload,
      );
      return { data: response.data, success: true };
   } catch (err: unknown) {
      const error = err as AxiosError;
      console.error('Error fetching data:', error);
      return {
         data: error.response,
         success: false,
         status: error.response?.status,
      };
   }
};
export const renameHistoryTitle = async (payload: UpdateTitlePayload) => {
   try {
      const response: AxiosResponse<UpdateTitleResponse> =
         await marcoClient.put(`/history/${payload.session_id}`, payload);
      return response;
   } catch (error) {
      console.error('Error fetching data:', error);
      return null;
   }
};
export const updateUserFeedback = async (payload: userFeedback) => {
   try {
      const res: AxiosResponse<UpdateTitleResponse> = await marcoClient.put(
         '/history/user-feedback',
         payload,
      );
      return res;
   } catch (error) {
      console.error('Error fetching data:', error);
      return null;
   }
};

export const clearallHistory = async (userDetails: UserDetails) => {
   try {
      const response = await marcoClient.delete(
         `/history_clear_all?client_id=${userDetails.client_id}&user=${userDetails.email}`,
      );
      if (!response.data) {
         throw new Error(`Failed to delete entire history`);
      }
      return 'Deleted';
   } catch (error) {
      console.error('Error fetching data:', error);
      return null;
   }
};
