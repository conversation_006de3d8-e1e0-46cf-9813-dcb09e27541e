import { Text, useColorModeValue } from '@chakra-ui/react';
import { ViewDetailsProp } from '../utils/interface';
import { openModal } from '../../../store/reducer/modal-reducer';
import { useDispatch } from 'react-redux';
import { modalTypes } from '../../../components/modals/modal-types';

function ViewDetails(props: ViewDetailsProp) {
   const { kpiDetails, prevKpi, isAnomaly } = props;
   const dispatch = useDispatch();
   const linkColor = useColorModeValue('#337CDF', '#63B3ED');
   if (!kpiDetails?.allData?.length) return null;
   const handleViewOpen = () => {
      dispatch(
         openModal({
            modalType: modalTypes.KPI_VIEW_DETAILS,
            modalProps: { kpiDetails, prevKpi, isAnomaly },
         }),
      );
   };

   return (
      <>
         <Text
            cursor={'pointer'}
            onClick={handleViewOpen}
            fontSize={14}
            textDecoration={'underline'}
            textAlign={'left'}
            color={linkColor}
         >
            More Details
         </Text>
      </>
   );
}

export default ViewDetails;
