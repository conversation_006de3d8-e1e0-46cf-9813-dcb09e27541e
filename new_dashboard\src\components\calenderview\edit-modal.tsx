import Calendar from '../comman/calendar';
import socialWatchEndpoints from '../../api/service/social-watch/apis';

import React, { useState, useEffect } from 'react';
import {
   Modal,
   ModalOverlay,
   ModalContent,
   ModalHeader,
   ModalCloseButton,
   ModalBody,
   ModalFooter,
   Button,
   Switch,
   Text,
   Box,
   FormControl,
   FormLabel,
   useToast,
   Textarea,
} from '@chakra-ui/react';
import { FaCloudUploadAlt as CloudUploadIcon } from 'react-icons/fa';
import { useDropzone } from 'react-dropzone';
import { CalendarItem } from '../../types/social-watch';

import 'react-datepicker/dist/react-datepicker.css';
import { useAppDispatch, useAppSelector } from '../../store/store';
import {
   removeMultiMedia,
   removeRawFile,
   resetMultiMedia,
   resetRawFiles,
   setMultiMedia,
   setRawFiles,
} from '../../store/reducer/user-details-reducer';
import { ImageWithDeleteIcon } from '../tabContent/tab-content';

interface EditModalProps {
   onEdit: boolean;
   setOnEdit: (open: boolean) => void;
   EditContent: CalendarItem | null;
   handleUpdate: () => void;
}

const EditModal: React.FC<EditModalProps> = ({
   onEdit,
   setOnEdit,
   EditContent,
   handleUpdate,
}) => {
   const [title, setTitle] = useState<string>('');
   const [imageDecode, setImageDecode] = useState<string[]>([]);
   const [selectedDate, setSelectedDate] = useState<Date | string | null>(null);
   const [switchState, setSwitchState] = useState<boolean>(
      EditContent?.is_schedule || false,
   );
   const toast = useToast();
   const dispatch = useAppDispatch();

   const dynamicSocialMedia = useAppSelector((state) => state.media);
   const {
      connections: { linkedin, twitter },
   } = useAppSelector((state) => state.integration);

   useEffect(() => {
      async function decodeImageAsync() {
         if (!EditContent || !EditContent.media) return;
         try {
            const imageLinks = EditContent.media.map((item) =>
               socialWatchEndpoints
                  .decodeAzureMediaUrl({
                     image_url: item.image_link!,
                  })
                  .then((res) => res.data),
            );
            const res = await Promise.all(imageLinks);
            setImageDecode(res.map((res) => res.uri));
         } catch (err) {
            console.log('[ERROR] Image decode error ', JSON.stringify(err));
         }
      }

      void decodeImageAsync();
   }, [EditContent?.media]);

   console.log('IMAGE DECODE ', imageDecode);

   useEffect(() => {
      if (
         EditContent?.social_media_type &&
         (dynamicSocialMedia.multiMedia[EditContent.social_media_type] || [])
            .length > 0
      ) {
         dispatch(resetMultiMedia());
         dispatch(resetRawFiles());
      }

      if (EditContent) {
         const { title, is_schedule, start, media, social_media_type } =
            EditContent;
         setTitle(title);
         setSwitchState(is_schedule);
         setSelectedDate(start ? new Date(start) : new Date());

         if (media) {
            // dispatch to store it in the redux:

            media.forEach((item) => {
               dispatch(
                  setMultiMedia({
                     media_id: item.media_id,
                     image_link: item.image_link,
                     rawFile: '',
                     socialMedia: social_media_type,
                  }),
               );
            });
         }
      }
   }, [
      EditContent?.title,
      EditContent?.start,
      EditContent?.is_schedule,
      EditContent?.media,
   ]);

   useEffect(() => {
      return () => {
         setTitle('');
         setSelectedDate(new Date());
         setSwitchState(false);
         dispatch(resetMultiMedia());
         dispatch(resetRawFiles());
      };
   }, []);

   const handleFileUpload = async (files: File[]): Promise<void> => {
      if (!files || files.length === 0) return;

      const readFile = (file: File): Promise<string> => {
         return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result as string);
            reader.onerror = () => reject(new Error('File reading error'));
            reader.readAsDataURL(file);
         });
      };

      const uploadFile = async (
         result: string,
         formData: FormData,
         platform: string,
      ): Promise<void> => {
         try {
            let uploadResponse: {
               data: Record<string, string>;
            } | null = null;
            if (platform === 'twitter') {
               uploadResponse =
                  await socialWatchEndpoints.uploadImageToTwitter(formData);
            } else if (platform === 'linkedin') {
               uploadResponse =
                  await socialWatchEndpoints.uploadImageToLinkedin(formData);
            }

            const {
               data: { uri },
            } = await socialWatchEndpoints.uploadMediaToAzure(formData);
            const mediaIdKey = platform === 'twitter' ? 'mediaId' : 'assetId';

            if (!uploadResponse) {
               toast({
                  title: 'Upload error',
                  description: `Media upload failed`,
                  status: 'error',
                  duration: 3000,
                  isClosable: true,
               });
               return;
            }

            dispatch(
               setMultiMedia({
                  media_id: uploadResponse.data[mediaIdKey],
                  image_link: uri,
                  rawFile: result,
                  socialMedia: platform,
               }),
            );
         } catch (error) {
            console.error('Upload error:', error);
            throw error;
         }
      };

      const handleFile = async (file: File): Promise<void> => {
         let fileType: string | undefined;
         try {
            if (!EditContent) return;
            const result = await readFile(file);

            if (file.type.includes('image')) {
               dispatch(
                  setRawFiles({
                     result,
                     socialMedia: EditContent.social_media_type,
                  }),
               );
               fileType = 'image';
            } else if (file.type.includes('video')) {
               fileType = 'video';
            }

            const formData = new FormData();
            const platform = EditContent.social_media_type;

            if (platform === 'twitter' && twitter) {
               const { oauthToken, oauthTokenSecret } = twitter;
               formData.append('media', file);
               formData.append('oauthToken', oauthToken);
               formData.append('oauthTokenSecret', oauthTokenSecret);
            } else if (platform === 'linkedin' && linkedin) {
               const { user } = linkedin;

               formData.append('media', file);
               formData.append('token', user.access_token);
               formData.append('userId', user.userId);
            }

            await uploadFile(result, formData, platform);
         } catch (error) {
            toast({
               title: 'Upload error',
               description: `${fileType} upload failed`,
               status: 'error',
               duration: 3000,
               isClosable: true,
            });
         }
      };

      await Promise.all(Array.from(files).map(handleFile));
   };

   const { getRootProps, getInputProps } = useDropzone({
      accept: {
         'image/*': [],
         'video/*': [],
      }, // Accepts all image and video files
      onDrop: (files) => void handleFileUpload(files),
   });

   const onDelete = (file: string) => {
      if (!EditContent?.social_media_type) return;
      dispatch(
         removeRawFile({ file, socialMedia: EditContent.social_media_type }),
      );
      dispatch(
         removeMultiMedia({ file, socialMedia: EditContent.social_media_type }),
      );
   };

   const renderPreview = () => {
      if (!EditContent?.social_media_type) return null;
      return (
         <div style={{ display: 'flex', gap: 7, alignItems: 'center' }}>
            {(
               dynamicSocialMedia.rawFiles[EditContent?.social_media_type] || []
            ).map((file) => (
               <ImageWithDeleteIcon
                  key={file}
                  src={file}
                  alt='Preview'
                  onDelete={() => onDelete(file)}
               />
            ))}
         </div>
      );
   };

   const handleSave = async () => {
      if (!EditContent) return;

      if (!selectedDate) {
         toast({
            title: 'Error',
            description: 'Please select a valid date and time.',
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
         return;
      }
      // const date = new Date(selectedDate).toLocaleDateString();
      // const time = new Date(selectedDate).toLocaleTimeString('en-US', {
      //    hour: '2-digit',
      //    minute: '2-digit',
      //    hour12: false,
      // });

      const date = new Date(selectedDate);
      const time = new Date(selectedDate).toLocaleTimeString('en-US', {
         hour: '2-digit',
         minute: '2-digit',
         hour12: false,
      });

      const payload = {
         uuid: EditContent.uuid,
         post_data: title,
         date: `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`,
         time,
         client_id: EditContent.client_id,
         social_media_type: EditContent.social_media_type,
         is_schedule: switchState,
         media: (
            dynamicSocialMedia.multiMedia[EditContent.social_media_type] || []
         ).map(({ image_link, media_id }) => ({ image_link, media_id })),
      };

      try {
         const {
            data: { updatedData },
         } = await socialWatchEndpoints.updateContentCalendarData(payload);
         if (updatedData) {
            handleUpdate();
            setOnEdit(false);
            toast({
               title: 'Saved',
               description: 'Your changes have been updated.',
               status: 'success',
               duration: 5000,
               isClosable: true,
            });
            setTitle('');
            setSelectedDate(new Date());
            dispatch(resetRawFiles());
            dispatch(resetMultiMedia());
         }
      } catch (error) {
         toast({
            title: 'Error',
            description: 'An error occurred. Please try again.',
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      }
   };

   const handleClose = () => {
      setOnEdit(false);
      setTitle('');
      setSelectedDate(new Date());
      setSwitchState(false);
   };

   function isSaveDisabled() {
      if (!selectedDate) return true;

      const date = selectedDate ? new Date(selectedDate) : new Date();

      const currentDate = new Date();

      return date < currentDate;
   }

   if (!onEdit) return null;

   return (
      <Modal isOpen={onEdit} onClose={handleClose} size='lg'>
         <ModalOverlay />
         <ModalContent>
            <ModalHeader>Edit Content</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
               <Box
                  display='flex'
                  flexDirection='column'
                  alignItems='center'
                  padding='20px'
               >
                  <div {...getRootProps()}>
                     <input {...getInputProps()} />
                     <CloudUploadIcon
                        style={{ margin: 'auto', fontSize: '3rem' }}
                     />
                     <p>
                        Drag 'n' drop some files here, or click to select files
                     </p>
                  </div>
                  <aside>{renderPreview()}</aside>
               </Box>
               <FormControl mt={4}>
                  <FormLabel>Description</FormLabel>
                  <Textarea
                     placeholder='Description'
                     value={title}
                     onChange={(e) => setTitle(e.target.value)}
                  />
               </FormControl>
               <Box mt={4} display='flex' alignItems='center'>
                  <Switch
                     isChecked={switchState}
                     onChange={() => setSwitchState(!switchState)}
                  />
                  <Text ml={2} color={switchState ? 'yellow.500' : 'blue.500'}>
                     {switchState ? 'Scheduled' : 'In Draft'}
                  </Text>
               </Box>
               <Box mt={4}>
                  <FormLabel>Date & Time</FormLabel>
                  <Calendar
                     setSelectedDate={setSelectedDate}
                     selectedDate={selectedDate}
                  />
               </Box>
            </ModalBody>
            <ModalFooter>
               <Button
                  colorScheme='red'
                  variant='outline'
                  mr={3}
                  onClick={handleClose}
               >
                  Cancel
               </Button>
               <Button
                  isDisabled={isSaveDisabled()}
                  colorScheme='blue'
                  onClick={() => void handleSave()}
               >
                  Save
               </Button>
            </ModalFooter>
         </ModalContent>
      </Modal>
   );
};

export default EditModal;
