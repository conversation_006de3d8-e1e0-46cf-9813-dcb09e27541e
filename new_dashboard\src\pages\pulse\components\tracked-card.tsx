import React from 'react';
import { Link } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { useAppSelector } from '../../../store/store';
import { useColorMode } from '@chakra-ui/react';

import { button } from '../../../utils/strings/pulse-strings';
import { UserDetails, TrackedCampaign } from './interface';
import LineChart from './linechart';
import pulseBackendEndpoints from '../../../api/service/pulse';
import {
   dropdownObjectiveOptions,
   toShowCurrency,
   truncateText,
} from '../utils/helper';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import './tracked-card.scss';
import { useToast, Flex, Image, Tooltip } from '@chakra-ui/react';
import { openModal } from '../../../store/reducer/modal-reducer';
import { modalTypes } from '../../../components/modals/modal-types';
import trackedpin from '../../../assets/icons/tracked-pin.svg';
import LineChartRange from './linechart-range';
import { calculateHelper } from '../../utils/kpiCalculaterHelper';
interface CardProps {
   viewdetailsId?: string;
   untrackBtnId?: string;
   chart_data: TrackedCampaign;
   handleUntrack: () => void;
   range_data: TrackedCampaign[] | null;
}

const TrackedCard: React.FC<CardProps> = ({
   untrackBtnId,
   chart_data,
   handleUntrack,
   range_data,
   viewdetailsId,
}) => {
   const userDetails =
      LocalStorageService.getItem<UserDetails>(Keys.FlableUserDetails) ||
      ({} as UserDetails);
   const toast = useToast();
   const { groupBy } = useAppSelector((state) => state.kpi);
   const { colorMode } = useColorMode();
   const handleUntrackWrapper = () => {
      void handleTrackClick();
   };
   const handleTrackClick = async () => {
      const res = await pulseBackendEndpoints.updateTrackedKpis({
         client_id: userDetails.client_id,
         kpi_name: chart_data.kpi_name,
         objective: chart_data.objective || '',
         campaign_id: String(chart_data.campaign_id),
         tracked: false,
         channel: chart_data.channel || 'meta_ads',
      });
      if (res.status === 200) {
         toast({
            title: 'Untracked successfully',
            status: 'success',
            duration: 3000,
            isClosable: true,
         });
         void handleUntrack();
      }
   };

   const filteredChartData = chart_data?.kpis?.filter(
      (kpi) =>
         kpi.kpi_name === chart_data.kpi_name && !isNaN(Number(kpi.kpi_value)),
   );

   const dispatch = useDispatch();
   const handleViewOpen = () => {
      dispatch(
         openModal({
            modalType: modalTypes.TrackedKpiViewDetailsModal,
            modalProps: { chart_data: chart_data, range_data: range_data },
         }),
      );
   };

   const currentKPIValue = chart_data.total_val[chart_data.kpi_name];
   const previousKPIValue =
      range_data?.find(
         (campaign) => campaign.campaign_id === chart_data.campaign_id,
      )?.total_val[chart_data.kpi_name] || null;
   const { percentage, color, direction, currentValue } = calculateHelper(
      chart_data?.kpi_name,
      currentKPIValue,
      previousKPIValue,
   );
   const arrow = direction === 'is up' ? '↑' : '↓';

   return (
      <div className='tracked-CardWrapper'>
         <div className={`tracked-usercards ${colorMode}`}>
            <div className='tracked-top'>
               <Tooltip
                  label={
                     chart_data?.campaign_name?.length > 25
                        ? chart_data?.campaign_name
                        : ''
                  }
                  placement='top'
                  fontSize='small'
               >
                  <button className='tracked-campaign-name'>
                     <span>
                        {truncateText(chart_data?.campaign_name, false, 25)}
                     </span>
                  </button>
               </Tooltip>
               <button>
                  {
                     dropdownObjectiveOptions.find(
                        (obj) => obj.value === chart_data?.objective,
                     )?.label
                  }
               </button>
               <button className='tracked-campaign-status'>
                  <p
                     className={
                        chart_data?.campaign_status &&
                        chart_data.campaign_status === 'ACTIVE'
                           ? 'campaign-status-active'
                           : 'campaign-status-pause'
                     }
                  >
                     {chart_data?.campaign_status || 'PAUSED'}
                  </p>
               </button>
            </div>
            <div className='tracked-chart-elements'>
               <div className='tracked-elements'>
                  <h6 style={{ color }}>
                     <span>{percentage && `${percentage}% ${arrow}`}</span>
                  </h6>
                  <p>
                     <span>
                        {chart_data.kpi_name.toUpperCase()}{' '}
                        {percentage && direction}
                     </span>
                  </p>
                  <h4>
                     <span>
                        {currentValue !== 'N/A' &&
                           toShowCurrency(
                              chart_data.kpi_name,
                              chart_data.kpis?.find(
                                 (kpi) => kpi.kpi_name === chart_data.kpi_name,
                              )?.currency || '[INR]',
                           )}
                        {currentValue}
                     </span>
                  </h4>
               </div>
               <div className='tracked-chart' style={{ color: 'black' }}>
                  {chart_data &&
                     filteredChartData &&
                     currentValue !== 'N/A' && (
                        <>
                           {groupBy === 'day' ? (
                              <LineChart
                                 kpiDetails={{
                                    displayName: chart_data.campaign_name,
                                    allData: filteredChartData.map((kpi) => ({
                                       date: kpi.kpi_date,
                                       kpi_value: parseFloat(
                                          Number(kpi.kpi_value).toFixed(2),
                                       ),
                                    })),
                                    stat:
                                       chart_data?.campaign_status || 'PAUSED',
                                 }}
                              />
                           ) : (
                              <LineChartRange
                                 kpiDetails={{
                                    displayName: chart_data.campaign_name,
                                    allData: chart_data?.grouped_kpis
                                       ? Object.keys(
                                            chart_data?.grouped_kpis,
                                         ).map((date) => ({
                                            date: date,
                                            kpi_value: chart_data
                                               ?.grouped_kpis?.[date]?.[
                                               chart_data.kpi_name
                                            ]
                                               ? parseFloat(
                                                    Number(
                                                       chart_data
                                                          ?.grouped_kpis?.[
                                                          date
                                                       ]?.[
                                                          chart_data?.kpi_name
                                                       ],
                                                    ).toFixed(2),
                                                 )
                                               : 0,
                                         }))
                                       : [],
                                    stat:
                                       chart_data?.campaign_status || 'PAUSED',
                                 }}
                              />
                           )}
                        </>
                     )}
               </div>
            </div>
            <div className='tracked-bottom'>
               <hr className={`tracked-divider ${colorMode}`} />
               <div className='tracked-bottom-buttons'>
                  <Link to='#' onClick={handleViewOpen} id={viewdetailsId}>
                     View Details
                  </Link>
                  <Flex gap={2}>
                     <Image
                        src={trackedpin}
                        style={{
                           filter: colorMode === 'dark' ? 'invert(1)' : 'none',
                        }}
                     />
                     <button
                        className='untrack-button'
                        id={untrackBtnId}
                        onClick={handleUntrackWrapper}
                        style={{
                           cursor: 'pointer',
                        }}
                     >
                        {button.unTrack}
                     </button>
                  </Flex>
               </div>
            </div>
         </div>
      </div>
   );
};

export default TrackedCard;
