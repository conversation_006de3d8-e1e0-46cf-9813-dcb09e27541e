import { FC } from 'react';
import { <PERSON>u, <PERSON>u<PERSON><PERSON>on, Menu<PERSON>ist, MenuItem } from '@chakra-ui/react';
import {
   FaEllipsisH,
   FaShare,
   FaEdit,
   FaTrash,
   FaUpload,
} from 'react-icons/fa';

interface HistoryMenuProps {
   sessionId: string;
   onOptionClick: (option: string, sessionId: string) => void;
}

const HistoryMenu: FC<HistoryMenuProps> = ({ sessionId, onOptionClick }) => {
   return (
      <Menu>
         <MenuButton>
            <FaEllipsisH className='ellipsis-icon' />
         </MenuButton>

         <MenuList>
            <MenuItem
               onClick={() => onOptionClick('Share', sessionId)}
               isDisabled
            >
               <FaShare />{' '}
               <span style={{ marginLeft: '2.5rem' }}>
                  Share (coming soon..)
               </span>
            </MenuItem>
            <MenuItem onClick={() => onOptionClick('Export', sessionId)}>
               <FaUpload /> <span style={{ marginLeft: '2.5rem' }}>Export</span>
            </MenuItem>
            <MenuItem onClick={() => onOptionClick('Rename', sessionId)}>
               <FaEdit /> <span style={{ marginLeft: '2.5rem' }}>Rename</span>
            </MenuItem>
            <MenuItem onClick={() => onOptionClick('Delete', sessionId)}>
               <FaTrash /> <span style={{ marginLeft: '2.5rem' }}>Delete</span>
            </MenuItem>
         </MenuList>
      </Menu>
   );
};

export default HistoryMenu;
