import { But<PERSON>, Box, Flex, Image, Heading, Text } from '@chakra-ui/react';
import { forwardRef } from 'react';
import { contentIdeationStrings } from '../../utils/strings/content-ideation';
import { ChevronLeftIcon } from '@chakra-ui/icons';
import { useAppDispatch, useAppSelector } from '../../store/store';
import { setClickedPill } from '../../store/reducer/content-ideation-reducer';
import { useEffect, useState } from 'react';
import ContentIdentation from '../../assets/image/contentidentation.svg';
import PillContent from './pill-content';
import CompetitorTable from './competitor-table';
import useDivWidth from '../../hooks/use-div-width';
import { TooltipPosition } from 'intro.js/src/packages/tooltip';
import { componentNames, setFlag } from '../../store/reducer/tour-reducer';

import {
   GetTrendsPayload,
   TrendData,
   PersonalisedData,
   CompetitorData,
} from './interface';
import { getContent } from '../../api/service/contentideation';
import PersonalisedCard from './personalisedCard';

import { openModal } from '../../store/reducer/modal-reducer';
import { modalTypes } from '../../components/modals/modal-types';
import { useDispatch } from 'react-redux';
import {
   resetRawFiles,
   resetMultiMedia,
} from '../../store/reducer/user-details-reducer';
import { resetAiText } from '../../store/reducer/configReducer';
import { Keys, LocalStorageService } from '../../utils/local-storage';
import { AuthUser } from '../../types/auth';

// import './content-ideation.scss';
import SkeletonLoaderTable from '../../components/skeletonloader/skeleton-loader-table';
import introJs from 'intro.js';

interface Pill {
   title: string;
   id?: string;
}

const pills: Pill[] = [
   {
      title: 'Trends',
   },
   {
      title: 'Industry Trends',
   },
   {
      title: 'Personalised',
   },
   {
      title: 'Competitor',
   },
   {
      title: 'Upload Image',
   },
];

interface Props {
   handleBack: () => void;
   handleBackToWrapper: () => void;
}

const userDetails = LocalStorageService.getItem<AuthUser>(
   Keys.FlableUserDetails,
);

const intro = introJs();

const steps: {
   element: string;
   intro: string;
   position: TooltipPosition;
}[] = [
   {
      element: '#pill-0',
      intro: 'Generate content based on what is trending on Google Trends in your region.',
      position: 'top',
   },
   {
      element: '#pill-1',
      intro: ' Generate content based on your industry.',
      position: 'top',
   },
   {
      element: '#pill-2',
      intro: 'Generate content personalized to your brand.',
      position: 'top',
   },
   {
      element: '#pill-3',
      intro: 'Generate content based on your competitor’s best performing content.',
      position: 'top',
   },
   {
      element: '#pill-4',
      intro: 'Generate content based on the image uploaded.',
      position: 'top',
   },
];

function ContentIdeationHeader() {
   return (
      <Box textAlign='center' mb={6} mt={0}>
         <Image
            src={ContentIdentation}
            alt='ContentIdeation'
            boxSize={{ base: '40px', sm: '50px', md: '60px', lg: '80px' }}
            mx='auto'
            filter='invert(0)'
            _dark={{ filter: 'invert(1)' }}
         />
         <Heading
            as='h4'
            size='lg'
            fontWeight={'normal'}
            color='chakra-body-text'
         >
            {contentIdeationStrings.contentIdeationHeader}
         </Heading>
         <Text fontSize='md' color='chakra-body-text'>
            {contentIdeationStrings.contentIdeationDescription}
         </Text>
      </Box>
   );
}

function Pill({ title, id }: { title: string; id?: string }) {
   const dispatch = useAppDispatch();

   function handleClick() {
      if (title === 'Upload Image') {
         dispatch(
            openModal({
               modalType: modalTypes.UPLOAD_IMAGE_MODAL,
            }),
         );
         dispatch(setClickedPill('Image Upload'));
      } else {
         dispatch(setClickedPill(title));
      }
   }

   return (
      <Button
         id={id}
         height='40px'
         border='1px solid #2d64cb'
         borderRadius='12px'
         color='#2d64cb'
         mb={4}
         onClick={handleClick}
         flexGrow={1}
         mx={1}
         backgroundColor={'white'}
         _dark={{ backgroundColor: 'var(--background)', color: 'white' }}
         width={{ base: '80px', sm: '160px', md: '200px', lg: '240px' }}
         fontSize={{ base: '8px', sm: '12px', md: '14px', lg: '16px' }}
         overflowWrap={'normal'}
         whiteSpace='normal'
      >
         {title}
      </Button>
   );
}

type PillsProps = {
   divRef: React.MutableRefObject<HTMLDivElement | null>;
};

const Pills = forwardRef<HTMLDivElement, PillsProps>(({ divRef }) => {
   return (
      <Flex ref={divRef} justifyContent='center' mb={6}>
         {pills.map((pill, index) => (
            <Pill key={pill.title} title={pill.title} id={`pill-${index}`} />
         ))}
      </Flex>
   );
});
function ContentIdeation({ handleBack, handleBackToWrapper }: Props) {
   const { contentIdeation } = useAppSelector((state) => state.tour);

   const { clickedPillId } = useAppSelector((state) => state.contentIdeation);
   const { divRef, width } = useDivWidth();
   const [trends, setTrends] = useState<TrendData[]>([]);
   const [personalDataIdeas, setPersonalDataIDeas] =
      useState<PersonalisedData | null>(null);
   const [competitorData, setCompetitorData] = useState<CompetitorData | null>(
      null,
   );
   const dispatch = useDispatch();
   const { personalisedData } = useAppSelector(
      (state) => state.contentIdeation,
   );
   const [loader, setLoader] = useState<boolean>(false);
   const [showErrorMessage, setShowErrorMessage] = useState<boolean>(false);
   const startTour = () => {
      intro.setOptions({ steps });
      void intro.start();

      dispatch(
         setFlag({ componentName: componentNames.WEB_INSIGHT, flag: false }),
      );
   };
   useEffect(() => {
      if (contentIdeation) startTour();
   }, [contentIdeation]);
   useEffect(() => {
      const fetchContent = async () => {
         setLoader(true);
         if (!userDetails?.client_id) return;

         const payload: GetTrendsPayload = {
            client_id: userDetails?.client_id,
            data: clickedPillId,
         };

         const response = await getContent(payload);

         if (
            clickedPillId === 'Trends' ||
            clickedPillId === 'Industry Trends'
         ) {
            if (response) {
               if ('trends' in response) {
                  setLoader(false);
                  setTrends(response.trends);
                  setShowErrorMessage(false);
               }
            } else {
               setLoader(false);
               setShowErrorMessage(true);
            }
         } else if (clickedPillId === 'Personalised') {
            if (response) {
               if ('personalised_content_ideas' in response) {
                  setLoader(false);
                  setShowErrorMessage(false);
                  setPersonalDataIDeas(response.personalised_content_ideas);
               }
            } else {
               setLoader(false);
               setShowErrorMessage(true);
            }
         } else if (clickedPillId === 'Competitor') {
            if (response) {
               if ('results' in response) {
                  setLoader(false);
                  setShowErrorMessage(false);
                  setCompetitorData(response);
               }
            } else {
               setLoader(false);
               setShowErrorMessage(true);
            }
         }
      };

      void fetchContent();
      if (
         clickedPillId === 'Personalised' ||
         clickedPillId === 'Competitor' ||
         clickedPillId === 'Trends' ||
         clickedPillId === 'Industry Trends'
      ) {
         dispatch(resetRawFiles());
         dispatch(resetMultiMedia());
         dispatch(resetAiText());
      }
   }, [clickedPillId]);
   const pillsMap: Record<string, JSX.Element> = {
      Trends: (
         <PillContent
            width={width}
            trends={trends}
            handleBackToWrapper={handleBackToWrapper}
         />
      ),
      'Industry Trends': (
         <PillContent
            width={width}
            trends={trends}
            handleBackToWrapper={handleBackToWrapper}
         />
      ),
      Personalised: (
         <PersonalisedCard
            width={width}
            data={personalDataIdeas}
            handleBackToWrapper={handleBackToWrapper}
         />
      ),
      Competitor: (
         <CompetitorTable
            width={width}
            data={competitorData}
            handleBackToWrapper={handleBackToWrapper}
         />
      ),
      'Image Upload': personalisedData ? (
         personalisedData && (
            <PersonalisedCard
               width={width}
               data={personalisedData}
               handleBackToWrapper={handleBackToWrapper}
            />
         )
      ) : (
         <></>
      ),
   };
   useEffect(() => {
      if (clickedPillId === 'Image Upload') {
         setLoader(personalisedData === null);
      }
   }, [personalisedData, clickedPillId]);
   return (
      <>
         <Button
            onClick={handleBack}
            backgroundColor='var(--chakra-colors-chakra-body-bg)'
            textAlign='center'
            alignSelf='flex-start'
            mb={2}
            color='var(--chakra-colors-chakra-body-text)'
            _hover={{
               backgroundColor: 'var(--chakra-colors-chakra-subtle-bg)',
            }}
         >
            <ChevronLeftIcon />
            Back
         </Button>
         <Flex direction='column' align='center' p={4} pt={0} ml={12} mr={12}>
            <Box w='full' mt={!clickedPillId ? '100px' : '0'}>
               <ContentIdeationHeader />
               <Pills divRef={divRef} />
               {loader && clickedPillId && (
                  <SkeletonLoaderTable
                     width={width}
                     clickedPillId={clickedPillId}
                  />
               )}
               {!loader && !showErrorMessage && pillsMap[clickedPillId]}
               {showErrorMessage && !loader && (
                  <Text ml='40%' mt='20%'>
                     No Data Found for {clickedPillId}
                  </Text>
               )}
            </Box>
         </Flex>
      </>
   );
}

export default ContentIdeation;
