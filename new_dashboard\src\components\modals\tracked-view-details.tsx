import ModalWrapper from './modal-wrapper';
import { TrackedCampaign } from '../../pages/pulse/components/interface';
import { useAppSelector } from '../../store/store';
import BarChart from '../../pages/pulse/components/barchart';
import { Flex, Box } from '@chakra-ui/react';
import {
   cap,
   formatDateRange,
   removeColumnsWithNoKPIValue,
   getDateRange,
} from '../../pages/pulse/utils/helper';
import './tracked-view-details.scss';
import BarChartRange from '../../pages/pulse/components/barchart-range';
import { calculateHelper } from '../../pages/utils/kpiCalculaterHelper';

function TrackedKpiViewDetailsModal() {
   const currentModal = useAppSelector((state) => state.modal);
   const { channel } = useAppSelector((state) => state.dropdown);
   const { groupBy, dateRange, prevRange } = useAppSelector(
      (state) => state.kpi,
   );

   const { start_date, end_date, prev_start_date, prev_end_date, days } =
      getDateRange(dateRange, prevRange);

   const chart_data = (currentModal.payload?.modalProps
      ?.chart_data as TrackedCampaign) || {
      campaign_id: 0,
      campaign_name: '',
      kpis: [],
   };

   const range_data = (currentModal.payload?.modalProps
      ?.range_data as TrackedCampaign[]) || {
      campaign_id: 0,
      campaign_name: '',
      kpis: [],
   };

   const filteredChartData = chart_data?.kpis?.filter(
      (kpi) =>
         kpi.kpi_name === chart_data.kpi_name && !isNaN(Number(kpi.kpi_value)),
   );
   const headingTitle = `${chart_data.kpi_name.toUpperCase()} for ${chart_data.campaign_name}`;

   const currDateRange = formatDateRange(`${start_date}-${end_date}`);
   const prevDateRange = formatDateRange(`${prev_start_date}-${prev_end_date}`);

   const currentKPIValue = chart_data.total_val?.[chart_data.kpi_name];
   const previousKPIValue = range_data?.find(
      (campaign) => campaign.campaign_id === chart_data.campaign_id,
   )?.total_val?.[chart_data.kpi_name];
   const { currentValue, prevValue } = calculateHelper(
      chart_data?.kpi_name,
      currentKPIValue,
      previousKPIValue,
   );

   return (
      <ModalWrapper
         heading={headingTitle}
         overlayBgcolor='#2424241c'
         parentClassName='tracked-view'
      >
         <Flex flexDirection={'column'}>
            <Flex>
               <div className='top'>
                  <button>{cap(channel)}</button>
                  <button>{days} Days</button>
               </div>
            </Flex>
            <Flex justifyContent='space-between'>
               <Flex flexDirection='column'>
                  <Box fontSize='14'>{currentValue}</Box>
                  <Box fontSize='12'>{currDateRange}</Box>
               </Flex>
               <Flex flexDirection='column'>
                  <Box fontSize='14'>{prevValue}</Box>
                  <Box fontSize='12'>{prevDateRange}</Box>
               </Flex>
            </Flex>

            <Flex minHeight='350px'>
               {groupBy === 'day' ? (
                  <BarChart
                     kpiDetails={{
                        displayName: chart_data.campaign_name,
                        allData:
                           removeColumnsWithNoKPIValue(filteredChartData)?.map(
                              (kpi) => ({
                                 date: kpi.kpi_date,
                                 kpi_value: parseFloat(
                                    Number(kpi.kpi_value).toFixed(2),
                                 ),
                              }),
                           ) || [],
                        stat: chart_data?.campaign_status || 'PAUSED',
                     }}
                  />
               ) : (
                  <BarChartRange
                     kpiDetails={{
                        displayName: chart_data.campaign_name,
                        allData: chart_data?.grouped_kpis
                           ? Object.keys(chart_data?.grouped_kpis).map(
                                (date) => ({
                                   date: date,
                                   kpi_value: chart_data?.grouped_kpis?.[
                                      date
                                   ]?.[chart_data.kpi_name]
                                      ? parseFloat(
                                           Number(
                                              chart_data?.grouped_kpis?.[
                                                 date
                                              ]?.[chart_data?.kpi_name],
                                           ).toFixed(2),
                                        )
                                      : 0,
                                }),
                             )
                           : [],
                        stat: chart_data?.campaign_status || 'PAUSED',
                     }}
                  />
               )}
            </Flex>
         </Flex>
      </ModalWrapper>
   );
}
export default TrackedKpiViewDetailsModal;
