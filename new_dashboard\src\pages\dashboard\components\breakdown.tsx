import { Collapse, Flex, Text } from '@chakra-ui/react';
import { BreakDown } from '../utils/interface';
import { useState } from 'react';
import { FaChevronDown, FaChevronUp } from 'react-icons/fa';
import KPIImage from './kpi-image';
import { startEdornment } from '../../../utils/strings/kpi-constants';
import { getFormattedVal } from '../utils/helpers';

function Breakdown(props: { data: BreakDown | null }) {
   const { data } = props;
   if (!data) return null;
   const [collapseCat, setcollapseCat] = useState<{
      [key: string]: boolean;
   }>({});
   return (
      <Flex direction={'column'} width={'100%'}>
         {Object.entries(data).map(([kpi, item], index) => {
            const imageStyles: object = {
               height: item.category == 'amazon_ads' ? '16px' : '20px',
               width: item.category == 'amazon_ads' ? '25px' : '20px',
               position: item.category == 'amazon_ads' ? 'relative' : '',
               top: item.category == 'amazon_ads' ? '3px' : '',
            };
            return (
               <Flex pt={1} direction={'column'}>
                  <Flex
                     fontSize={'16px'}
                     key={index + item.kpi_display_name}
                     width={'100%'}
                     justifyContent={'space-between'}
                     alignItems={'center'}
                     p={3}
                  >
                     <Text
                        display={'flex'}
                        gap={2}
                        alignItems={'center'}
                        onClick={() =>
                           setcollapseCat((prev) => {
                              return {
                                 ...prev,
                                 [kpi]: !collapseCat[kpi],
                              };
                           })
                        }
                        fontWeight={'600'}
                     >
                        {' '}
                        <KPIImage
                           style={imageStyles}
                           kpiCat={item.category}
                        />{' '}
                        {item.kpi_display_name}{' '}
                        {item.sub_items &&
                           (collapseCat[kpi] ? (
                              <FaChevronUp
                                 fill='gray'
                                 className='cursor-pointer'
                              />
                           ) : (
                              <FaChevronDown
                                 fill='gray'
                                 className='cursor-pointer'
                              />
                           ))}
                     </Text>
                     <Text color={!item.up ? 'red' : ''} fontWeight={'600'}>
                        {' '}
                        {startEdornment[item.kpi_type]}{' '}
                        {getFormattedVal(Math.round(item.value * 100) / 100)}
                     </Text>
                  </Flex>
                  {item.sub_items && (
                     <Collapse
                        style={{ padding: '0 20px' }}
                        in={!!collapseCat[kpi]}
                        animateOpacity
                     >
                        <Breakdown data={item.sub_items} />
                     </Collapse>
                  )}
               </Flex>
            );
         })}
      </Flex>
   );
}

export default Breakdown;
