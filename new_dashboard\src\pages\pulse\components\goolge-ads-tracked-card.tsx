import React from 'react';
import { Link } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { useAppSelector } from '../../../store/store';
import { useColorMode } from '@chakra-ui/react';

import { button } from '../../../utils/strings/pulse-strings';
import { UserDetails } from './interface';
import LineChart from './linechart';
import pulseBackendEndpoints, {
   trackedkpisData,
   trackedPrevkpisData,
} from '../../../api/service/pulse';
import {
   cap,
   StatusTypes,
   toShowCurrency,
   toUpperCase,
   truncateText,
} from '../utils/helper';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import './tracked-card.scss';
import { useToast, Flex, Image, Tooltip } from '@chakra-ui/react';
import { openModal } from '../../../store/reducer/modal-reducer';
import { modalTypes } from '../../../components/modals/modal-types';
import trackedpin from '../../../assets/icons/tracked-pin.svg';
import LineChartRange from './linechart-range';
import { calculateHelper } from '../../utils/kpiCalculaterHelper';
interface CardProps {
   viewdetailsId?: string;
   untrackBtnId?: string;
   channel: string;
   currentCard: trackedkpisData;
   prevCardsData: trackedPrevkpisData[] | null;
   handleUntrack: () => void;
}

const GoogleAdsTrackedCard: React.FC<CardProps> = ({
   untrackBtnId,
   viewdetailsId,
   channel,
   currentCard,
   prevCardsData,
   handleUntrack,
}) => {
   const userDetails =
      LocalStorageService.getItem<UserDetails>(Keys.FlableUserDetails) ||
      ({} as UserDetails);
   const toast = useToast();
   const { groupBy } = useAppSelector((state) => state.kpi);
   const { colorMode } = useColorMode();
   const handleUntrackWrapper = () => {
      void handleTrackClick();
   };
   const handleTrackClick = async () => {
      const res = await pulseBackendEndpoints.updateTrackedKpis({
         client_id: userDetails.client_id,
         kpi_name: currentCard.kpi_name,
         objective: currentCard.channel_type || '',
         campaign_id: String(currentCard.campaign_id),
         tracked: false,
         channel: channel,
      });
      if (res.status === 200) {
         toast({
            title: 'Untracked successfully',
            status: 'success',
            duration: 3000,
            isClosable: true,
         });
         void handleUntrack();
      }
   };

   const chartData = currentCard?.kpis?.filter(
      (kpi) =>
         kpi.kpi_name === currentCard.kpi_name && !isNaN(Number(kpi.kpi_value)),
   );

   const dispatch = useDispatch();
   const handleViewOpen = () => {
      dispatch(
         openModal({
            modalType: modalTypes.TrackedKpiViewDetailsModal,
            modalProps: { chart_data: currentCard, range_data: prevCardsData },
         }),
      );
   };

   const currentKPIValue = currentCard.total_val?.[currentCard.kpi_name];
   const previousKPIValue = prevCardsData?.find(
      (campaign) => campaign.campaign_id === currentCard.campaign_id,
   )?.total_val?.[currentCard.kpi_name];
   const { percentage, color, direction, currentValue } = calculateHelper(
      currentCard?.kpi_name,
      currentKPIValue,
      previousKPIValue,
   );
   const arrow = direction === 'is up' ? '↑' : '↓';
   return (
      <div className='tracked-CardWrapper'>
         <div className={`tracked-usercards ${colorMode}`}>
            <div className='tracked-top'>
               <Tooltip
                  label={
                     currentCard?.campaign_name?.length > 25
                        ? currentCard?.campaign_name
                        : ''
                  }
                  placement='top'
                  fontSize='small'
               >
                  <button className='tracked-campaign-name'>
                     <b>
                        {truncateText(currentCard?.campaign_name, false, 25)}
                     </b>
                  </button>
               </Tooltip>
               <Tooltip
                  label={
                     cap(currentCard?.channel_type?.toLowerCase())?.length > 10
                        ? cap(currentCard?.channel_type?.toLowerCase())
                        : ''
                  }
                  placement='top'
                  fontSize='small'
               >
                  <button className='trcaked-additional-button'>
                     {truncateText(
                        cap(currentCard?.channel_type?.toLowerCase()),
                        false,
                        12,
                     )}
                  </button>
               </Tooltip>
               <button className='tracked-campaign-status'>
                  <p
                     className={
                        currentCard?.campaign_status === StatusTypes.ENABLED
                           ? 'campaign-status-active'
                           : currentCard?.campaign_status ===
                               StatusTypes.REMOVED
                             ? 'campaign-status-removed'
                             : 'campaign-status-pause'
                     }
                  >
                     {currentCard?.campaign_status || 'PAUSED'}
                  </p>
               </button>
            </div>
            <div className='tracked-chart-elements'>
               <div className='tracked-elements'>
                  <h6 style={{ color }}>
                     <b>{percentage && `${percentage}% ${arrow}`}</b>
                  </h6>
                  <p>
                     <b>
                        {toUpperCase(currentCard.kpi_name)}{' '}
                        {percentage && direction}
                     </b>
                  </p>
                  <h4>
                     <b>
                        {currentValue !== 'N/A' &&
                           toShowCurrency(
                              currentCard.kpi_name,
                              currentCard.kpis?.find(
                                 (kpi) => kpi.kpi_name === currentCard.kpi_name,
                              )?.currency || '[INR]',
                           )}
                        {currentValue}
                     </b>
                  </h4>
               </div>
               <div className='tracked-chart' style={{ color: 'black' }}>
                  {currentCard && chartData && currentValue !== 'N/A' && (
                     <>
                        {groupBy === 'day' ? (
                           <LineChart
                              kpiDetails={{
                                 displayName: currentCard.campaign_name,
                                 allData: chartData.map((kpi) => ({
                                    date: kpi.kpi_date,
                                    kpi_value: parseFloat(
                                       Number(kpi.kpi_value).toFixed(2),
                                    ),
                                 })),
                                 stat: currentCard?.campaign_status || 'PAUSED',
                              }}
                           />
                        ) : (
                           <LineChartRange
                              kpiDetails={{
                                 displayName: currentCard.campaign_name,
                                 allData: currentCard?.grouped_kpis
                                    ? Object.keys(
                                         currentCard?.grouped_kpis,
                                      ).map((date) => ({
                                         date: date,
                                         kpi_value: currentCard?.grouped_kpis?.[
                                            date
                                         ]
                                            ? parseFloat(
                                                 Number(
                                                    currentCard?.grouped_kpis?.[
                                                       date
                                                    ]?.[currentCard.kpi_name],
                                                 ).toFixed(2),
                                              )
                                            : 0,
                                      }))
                                    : [],
                                 stat: currentCard?.campaign_status || 'PAUSED',
                              }}
                           />
                        )}
                     </>
                  )}
               </div>
            </div>
            <div className='tracked-bottom'>
               <hr className={`tracked-divider ${colorMode}`} />
               <div className='tracked-bottom-buttons'>
                  <Link to='#' onClick={handleViewOpen} id={viewdetailsId}>
                     View Details
                  </Link>
                  <Flex gap={2}>
                     <Image
                        src={trackedpin}
                        style={{
                           filter: colorMode === 'dark' ? 'invert(1)' : 'none',
                        }}
                     />
                     <button
                        className='untrack-button'
                        id={untrackBtnId}
                        onClick={handleUntrackWrapper}
                     >
                        {button.unTrack}
                     </button>
                  </Flex>
               </div>
            </div>
         </div>
      </div>
   );
};

export default GoogleAdsTrackedCard;
