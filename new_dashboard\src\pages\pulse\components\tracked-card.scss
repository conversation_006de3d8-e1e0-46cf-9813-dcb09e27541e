@use '../../../sass/variable.scss';

.tracked-CardWrapper {
   display: flex;
   flex-direction: column;
   align-items: center;
   width: 100%;

   .tracked-usercards {
      width: 450px;
      min-height: 290px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 13px;
      border-radius: 15px;
      border: 1px solid #ccc;
      background: white;

      .tracked-top {
         display: flex;
         justify-self: flex-start;
         gap: 5px;

         button {
            padding: 6px 8px;
            border: none;
            border-radius: 5px;
            background-color: #e3e2ff;
            color: blue;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;

            .campaign-status-active {
               color: #15994a;
            }

            .campaign-status-pause {
               color: #ffbf00;
            }

            .campaign-status-removed {
               color: #ff0000;
            }
         }
      }

      &.light {
         background: #ffffff;
         border: 1px solid #e2e8f0;
      }

      &.dark {
         // background: $background_surface;
         border: 1px solid #4a5568;
      }

      .tracked-chart-elements {
         display: flex;
         align-items: center;
         justify-content: space-between;
         width: 100%;
         box-sizing: border-box;
         flex-direction: row;

         .tracked-elements {
            display: flex;
            flex-direction: column;
            gap: 10px;
            width: 45%;

            h4 {
               color: #337cdf;
               font-size: x-large;
               margin: 0;
               font-weight: 700;
            }

            &.light {
               color: #000000;
            }

            &.dark {
               color: #ffffff;
            }
         }
      }

      &.light {
         color: #000000;
      }

      &.dark {
         color: #ffffff;
      }

      .tracked-divider {
         height: 1px;
         background-color: #ccc;

         &.light {
            border-color: #e2e8f0;
         }

         &.dark {
            border-color: #4a5568;
         }
      }

      .tracked-bottom-buttons {
         display: flex;
         justify-content: space-between;
         padding: 10px 0px;
         align-items: center;
         font-size: 15px;

         a {
            text-decoration: underline;
            color: #337cdf;
            font-weight: 600;
         }
      }
   }
}
