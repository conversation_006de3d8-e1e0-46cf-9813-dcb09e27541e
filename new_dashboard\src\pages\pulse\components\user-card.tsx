import React from 'react';
import './user-card.scss';
import { TruncatableText } from '../../../components';
import { button } from '../../../utils/strings/pulse-strings';

import trackedpin from '../../../assets/icons/tracked-pin.svg';
import { Image, useColorMode } from '@chakra-ui/react';

interface CardProps {
   src?: string;
   heading: string;
   icon?: string;
   subHeading?: string;
   backgroundColor?: string;
   isTracking?: boolean;
   onTrack?: () => void | Promise<void>;
}

const UserCard: React.FC<CardProps> = ({
   src,
   heading,
   icon,
   subHeading = '',

   isTracking,
   onTrack,
}) => {
   const { colorMode } = useColorMode();

   return (
      <div
         className={`WebInsightCardWrapper ${colorMode === 'dark' ? 'dark-theme' : 'light-theme'}`}
         // style={{ backgroundColor }}
      >
         <div className='card-content'>
            {heading && (
               <div className='heading-container'>
                  <h4 className='heading'>
                     <b>{heading}</b>
                  </h4>
                  {src && (
                     <img
                        className='icon'
                        src={src}
                        alt='User Avatar'
                        style={{
                           filter: colorMode === 'dark' ? 'invert(1)' : 'none',
                        }}
                     />
                  )}
                  {icon && <img className='icon' src={icon} alt='icon ' />}
               </div>
            )}

            <div className='usercard-container'>
               {subHeading && (
                  <TruncatableText text={subHeading} maxLength={50} />
               )}
               {!heading && (
                  <>
                     {src && (
                        <img
                           className='icon'
                           src={src}
                           alt='User Avatar'
                           style={{
                              filter:
                                 colorMode === 'dark' ? 'invert(1)' : 'none',
                           }}
                        />
                     )}
                     {icon && (
                        <img
                           className='icon'
                           src={icon}
                           alt='icon '
                           style={{
                              filter:
                                 colorMode === 'dark' ? 'invert(1)' : 'none',
                           }}
                        />
                     )}
                  </>
               )}
            </div>
         </div>
         <div className='bottom'>
            <Image
               src={trackedpin}
               filter={colorMode === 'dark' ? 'invert(1)' : 'none'}
            />
            <button
               className={`track-button ${isTracking ? 'tracking' : ''}`}
               disabled={isTracking}
               onClick={() => void onTrack?.()}
            >
               {isTracking ? button.tracking : button.track}
            </button>
         </div>
      </div>
   );
};

export default UserCard;
