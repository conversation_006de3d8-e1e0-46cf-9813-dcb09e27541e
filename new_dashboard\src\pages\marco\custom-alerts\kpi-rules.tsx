import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { Input } from '@/components/ui/input';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { useState } from 'react';
import { PiHashBold, PiPercentBold } from 'react-icons/pi';
import { splitAndUppercase } from '../utils/custom-alerts/custom-alerts-helpers';
import { cn } from '@/utils';
import { useAppDispatch, useAppSelector } from '@/store/store';
import { handleFormChange } from '@/store/reducer/custom-alerts-reducer';
import { METRICS_MAP } from '../utils/custom-alerts/custom-alerts-constants';

interface KpiRulesProps {
   metric: string;
   trend: string;
   value: string;
   value_type: string;
   comparison: string;
}

const KpiRules = (props: KpiRulesProps) => {
   const dispatch = useAppDispatch();

   const { metric, trend, value, value_type, comparison } = props;

   const [form, setForm] = useState({
      metric: metric,
      trend: trend,
      value: value,
      value_type: value_type,
      comparison: comparison,
   });

   const { form: customAlertForm } = useAppSelector(
      (state) => state.customAlerts,
   );

   const handleValueChange = (name: string, value: string) => {
      setForm((prev) => ({ ...prev, [name]: value }));
      const currentKpiRules = [...customAlertForm.kpi_rules];
      const index = currentKpiRules.findIndex(
         (rule) => rule.metric === form.metric,
      );
      currentKpiRules[index] = {
         ...currentKpiRules[index],
         [name]: value,
      };
      dispatch(
         handleFormChange({
            name: 'kpi_rules',
            value: [...currentKpiRules],
         }),
      );
   };

   return (
      <div className='grid grid-cols-1 md:grid-cols-8 gap-4 mb-4'>
         <div className='md:col-span-2 flex flex-col gap-2'>
            <p className='text-[14px] font-semibold ml-1'>Metric</p>
            <Input
               value={
                  METRICS_MAP[form.metric] || splitAndUppercase(form.metric)
               }
               readOnly
               className='focus-visible:ring-0 focus-visible:outline-none cursor-not-allowed'
            />
         </div>
         <div className='md:col-span-2 flex flex-col gap-2'>
            <p className='text-[14px] font-semibold ml-1'>
               Trend<span className='text-red-500 ml-1'>*</span>
            </p>
            <Select
               value={form.trend}
               onValueChange={(value) => handleValueChange('trend', value)}
            >
               <SelectTrigger className='w-full cursor-pointer focus-visible:ring-0 focus-visible:outline-none'>
                  <SelectValue placeholder='Select trend' />
               </SelectTrigger>
               <SelectContent className='bg-white'>
                  <SelectItem value='increasing'>Increasing</SelectItem>
                  <SelectItem value='decreasing'>Decreasing</SelectItem>
               </SelectContent>
            </Select>
         </div>
         <div className='md:col-span-2 flex flex-col gap-2'>
            <p className='text-[14px] font-semibold ml-1'>
               Value<span className='text-red-500 ml-1'>*</span>
            </p>
            <div className='flex items-center gap-2 relative'>
               <Input
                  value={form.value}
                  onChange={(e) => handleValueChange('value', e.target.value)}
                  className='focus-visible:ring-0 focus-visible:outline-none'
               ></Input>
               <ToggleGroup
                  variant='outline'
                  type='single'
                  className='absolute right-0'
                  value={form.value_type}
                  onValueChange={(value) =>
                     handleValueChange('value_type', value)
                  }
               >
                  <ToggleGroupItem
                     value='absolute'
                     aria-label='Toggle absolute'
                     className={cn('cursor-pointer !rounded-l-sm', {
                        'bg-gray-800': form.value_type === 'absolute',
                        'text-white': form.value_type === 'absolute',
                     })}
                     onClick={() =>
                        setForm({ ...form, value_type: 'absolute' })
                     }
                  >
                     <PiHashBold className='h-4 w-4' />
                  </ToggleGroupItem>
                  <ToggleGroupItem
                     value='percentage'
                     aria-label='Toggle percentage'
                     className={cn('cursor-pointer ', {
                        'bg-gray-800': form.value_type === 'percentage',
                        'text-white': form.value_type === 'percentage',
                     })}
                     onClick={() =>
                        setForm({ ...form, value_type: 'percentage' })
                     }
                  >
                     <PiPercentBold className='h-4 w-4' />
                  </ToggleGroupItem>
               </ToggleGroup>
            </div>
         </div>
         <div className='md:col-span-2 flex flex-col gap-2'>
            <p className='text-[14px] font-semibold ml-1'>
               Comparison<span className='text-red-500 ml-1'>*</span>
            </p>
            <div className='flex items-center gap-2 relative'>
               <Select
                  value={form.comparison}
                  onValueChange={(value) =>
                     handleValueChange('comparison', value)
                  }
               >
                  <SelectTrigger className='w-full cursor-pointer focus-visible:ring-0 focus-visible:outline-none'>
                     <SelectValue placeholder='Select comparison' />
                  </SelectTrigger>
                  <SelectContent className='bg-white'>
                     <SelectItem value='more_than'>More than</SelectItem>
                     <SelectItem value='less_than'>Less than</SelectItem>
                     <SelectItem value='equal_to'>Equal to</SelectItem>
                  </SelectContent>
               </Select>
            </div>
         </div>
      </div>
   );
};

export default KpiRules;
