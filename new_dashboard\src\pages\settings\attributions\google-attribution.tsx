import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
   ChevronRightIcon,
   ArrowBackIcon,
   CopyIcon,
   CheckIcon,
} from '@chakra-ui/icons';
import googleAdsScreenshot1 from './google-img.png';
import googleAdsScreenshot2 from './gooleImg.png';

const GoogleAttribution: React.FC = () => {
   const [currentStep, setCurrentStep] = useState(1);
   const [copied1, setCopied1] = useState(false);
   const [copied2, setCopied2] = useState(false);
   const navigate = useNavigate();
   const location = useLocation();
   const state = location.state as { from?: string } | null;
   const from = state?.from;

   const goBackToSettings = () => {
      if (from === 'attributions') {
         navigate('/settings?mode=attributions');
      } else {
         navigate('/settings');
      }
   };

   const handleCopy = (
      text: string,
      setCopied: React.Dispatch<React.SetStateAction<boolean>>,
   ) => {
      navigator.clipboard.writeText(text).catch((err) => {
         console.error('Failed to copy:', err);
      });
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
   };

   const goToNextStep = () => {
      setCurrentStep(2);
   };

   const goToPrevStep = () => {
      setCurrentStep(1);
   };

   return (
      <div className='w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6'>
         <div className='flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6'>
            <div className='text-2xl font-bold text-black dark:text-white'>
               Google Ads UTM Guide
            </div>
            <div className='flex items-center text-sm text-gray-500 dark:text-gray-400 mt-2 sm:mt-0'>
               <span
                  className='cursor-pointer hover:text-gray-700 dark:hover:text-gray-300'
                  onClick={() => navigate('/settings')}
               >
                  Settings
               </span>
               <ChevronRightIcon className='mx-1' />
               <span
                  className='cursor-pointer hover:text-gray-700 dark:hover:text-gray-300'
                  onClick={goBackToSettings}
               >
                  Attribution Agent
               </span>
               <ChevronRightIcon className='mx-1' />
               <span>Google Ads</span>
            </div>
         </div>

         <div className='bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden'>
            <div className='p-4'>
               <button
                  onClick={goBackToSettings}
                  className='flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors cursor-pointer'
               >
                  <ArrowBackIcon className='mr-2' />
                  <span>Back to Settings</span>
               </button>
            </div>

            <div className='flex justify-center mb-6 px-4'>
               <div className='flex items-center'>
                  <div
                     className={`w-12 h-12 rounded-full flex items-center justify-center cursor-pointer ${
                        currentStep === 1
                           ? 'text-white'
                           : 'border border-gray-300 text-gray-500'
                     }`}
                     style={
                        currentStep === 1 ? { backgroundColor: '#3C76E1' } : {}
                     }
                     onClick={() => setCurrentStep(1)}
                  >
                     1
                  </div>
                  <div className='w-16 sm:w-24 h-[1px] bg-gray-300'></div>
                  <div
                     className={`w-12 h-12 rounded-full flex items-center justify-center cursor-pointer ${
                        currentStep === 2
                           ? 'text-white'
                           : 'border border-gray-300 text-gray-500'
                     }`}
                     style={
                        currentStep === 2 ? { backgroundColor: '#3C76E1' } : {}
                     }
                     onClick={() => setCurrentStep(2)}
                  >
                     2
                  </div>
               </div>
            </div>

            <div className='px-12 py-8 max-w-5xl mx-auto'>
               <div className='relative mb-8'>
                  <button
                     onClick={goToPrevStep}
                     disabled={currentStep === 1}
                     className={`absolute left-[-80px] top-1/2 transform -translate-y-1/2 w-10 h-10 rounded-full flex items-center justify-center shadow-md z-10 
    ${currentStep === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:opacity-90'}`}
                     style={{
                        backgroundColor: '#8F9BBA',
                     }}
                     aria-label='Previous slide'
                  >
                     <svg
                        xmlns='http://www.w3.org/2000/svg'
                        className='h-6 w-6 text-white'
                        fill='none'
                        viewBox='0 0 24 24'
                        stroke='currentColor'
                     >
                        <path
                           strokeLinecap='round'
                           strokeLinejoin='round'
                           strokeWidth={2}
                           d='M15 19l-7-7 7-7'
                        />
                     </svg>
                  </button>

                  <div className='rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700'>
                     <div className='overflow-hidden'>
                        <div
                           className='transition-transform duration-300 ease-in-out'
                           style={{
                              transform: `translateX(-${(currentStep - 1) * 100}%)`,
                              display: 'flex',
                           }}
                        >
                           <div className='w-full flex-shrink-0'>
                              <img
                                 src={googleAdsScreenshot1}
                                 alt='Google Ads UTM Setup'
                                 className='w-full h-auto object-contain'
                              />
                           </div>
                           <div className='w-full flex-shrink-0'>
                              <img
                                 src={googleAdsScreenshot2}
                                 alt='Google Ads UTM Verification'
                                 className='w-full h-auto object-contain'
                              />
                           </div>
                        </div>
                     </div>
                  </div>

                  <button
                     onClick={goToNextStep}
                     disabled={currentStep === 2}
                     className={`absolute right-[-80px] top-1/2 transform -translate-y-1/2 w-10 h-10 rounded-full flex items-center justify-center shadow-md z-10 
    ${currentStep === 2 ? 'opacity-50 cursor-not-allowed' : 'hover:opacity-90'}`}
                     style={{
                        backgroundColor: '#8F9BBA',
                     }}
                     aria-label='Next slide'
                  >
                     <svg
                        xmlns='http://www.w3.org/2000/svg'
                        className='h-6 w-6 text-white'
                        fill='none'
                        viewBox='0 0 24 24'
                        stroke='currentColor'
                     >
                        <path
                           strokeLinecap='round'
                           strokeLinejoin='round'
                           strokeWidth={2}
                           d='M9 5l7 7-7 7'
                        />
                     </svg>
                  </button>
               </div>

               <div className='flex justify-center mb-6'>
                  <button
                     onClick={() => setCurrentStep(1)}
                     className={`w-2 h-2 mx-1 rounded-full ${currentStep === 1 ? 'bg-blue-400' : 'bg-gray-300'}`}
                     aria-label='Go to slide 1'
                  ></button>
                  <button
                     onClick={() => setCurrentStep(2)}
                     className={`w-2 h-2 mx-1 rounded-full ${currentStep === 2 ? 'bg-blue-400' : 'bg-gray-300'}`}
                     aria-label='Go to slide 2'
                  ></button>
               </div>

               {currentStep === 1 ? (
                  <>
                     <div className='mb-8'>
                        <h2 className='text-xl font-semibold mb-4'>
                           Step 1: Add the UTM URL to Your Google Ads
                        </h2>
                        <ol className='list-decimal pl-5 space-y-3 mb-6 text-gray-700 dark:text-gray-300'>
                           <li>
                              Go to Google Ads:{' '}
                              <a
                                 href='https://ads.google.com'
                                 target='_blank'
                                 rel='noopener noreferrer'
                                 className='text-blue-600 dark:text-blue-400 hover:underline'
                              >
                                 https://ads.google.com
                              </a>
                           </li>
                           <li>
                              Admin Settings &gt; Account Settings &gt; Tracking
                           </li>
                           <li>
                              Paste the UTM-enabled URL in the Final URL or use
                              the "Tracking Template" (for dynamic insertion).
                           </li>
                        </ol>
                     </div>

                     <div className='mb-8'>
                        <h2 className='text-xl font-semibold mb-4'>
                           Optional: Use Tracking Template for all ads
                        </h2>
                        <p className='mb-2'>
                           At the campaign or account level, use:
                        </p>
                        <div className='bg-gray-100 dark:bg-gray-700 p-3 rounded-md flex justify-between items-center mb-2'>
                           <code className='text-sm break-all'>
                              {
                                 '{lpurl}?utm_source=google&utm_medium=cpc&utm_campaign={campaignid}&utm_term={keyword}&utm_content={adgroupid}'
                              }
                           </code>
                           <button
                              onClick={() =>
                                 handleCopy(
                                    '{lpurl}?utm_source=google&utm_medium=cpc&utm_campaign={campaignid}&utm_term={keyword}&utm_content={adgroupid}',
                                    setCopied1,
                                 )
                              }
                              className='text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                           >
                              {copied1 ? <CheckIcon /> : <CopyIcon />}
                           </button>
                        </div>
                     </div>

                     <div className='mb-8'>
                        <h2 className='text-xl font-semibold mb-4'>
                           UTM Parameters Explained
                        </h2>
                        <p className='mb-4'>
                           UTM (Urchin Tracking Module) parameters are tags
                           added to a URL to track the performance of campaigns.
                        </p>

                        <div className='overflow-x-auto'>
                           <table className='min-w-full border border-gray-200 dark:border-gray-700'>
                              <thead>
                                 <tr className='bg-gray-50 dark:bg-gray-800'>
                                    <th className='px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700'>
                                       Parameter
                                    </th>
                                    <th className='px-4 py-3 text-left text-sm font-medium text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700'>
                                       Purpose
                                    </th>
                                 </tr>
                              </thead>
                              <tbody>
                                 <tr>
                                    <td className='px-4 py-3 text-sm font-medium text-green-600 dark:text-green-400 border border-gray-200 dark:border-gray-700'>
                                       utm_source
                                    </td>
                                    <td className='px-4 py-3 text-sm text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700'>
                                       Identifies the source (e.g. google,
                                       newsletter)
                                    </td>
                                 </tr>
                                 <tr>
                                    <td className='px-4 py-3 text-sm font-medium text-green-600 dark:text-green-400 border border-gray-200 dark:border-gray-700'>
                                       utm_medium
                                    </td>
                                    <td className='px-4 py-3 text-sm text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700'>
                                       Identifies the medium (e.g. cpc, email)
                                    </td>
                                 </tr>
                                 <tr>
                                    <td className='px-4 py-3 text-sm font-medium text-green-600 dark:text-green-400 border border-gray-200 dark:border-gray-700'>
                                       utm_campaign
                                    </td>
                                    <td className='px-4 py-3 text-sm text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700'>
                                       Names the campaign (e.g. summer_sale)
                                    </td>
                                 </tr>
                                 <tr>
                                    <td className='px-4 py-3 text-sm font-medium text-green-600 dark:text-green-400 border border-gray-200 dark:border-gray-700'>
                                       utm_term
                                    </td>
                                    <td className='px-4 py-3 text-sm text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700'>
                                       Identifies paid keywords (optional, for
                                       PPC)
                                    </td>
                                 </tr>
                                 <tr>
                                    <td className='px-4 py-3 text-sm font-medium text-green-600 dark:text-green-400 border border-gray-200 dark:border-gray-700'>
                                       utm_content
                                    </td>
                                    <td className='px-4 py-3 text-sm text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700'>
                                       Differentiates ads or links (optional)
                                    </td>
                                 </tr>
                              </tbody>
                           </table>
                        </div>
                     </div>
                  </>
               ) : (
                  <>
                     <div className='mb-8'>
                        <h2 className='text-xl font-semibold mb-4'>
                           Verify UTM Parameters Are Working
                        </h2>
                        <p className='mb-3'>Use any of the below:</p>
                        <ul className='list-disc pl-5 space-y-2 mb-6'>
                           <li>
                              Click your ad and check if UTM tags appear in the
                              URL
                           </li>
                           <li>
                              Use Google Analytics 4:
                              <ul className='list-disc pl-5 mt-2 space-y-1'>
                                 <li>
                                    Go to Reports &gt; Acquisition &gt; Traffic
                                    acquisition
                                 </li>
                                 <li>
                                    Use source/medium or campaign filters to
                                    check traffic
                                 </li>
                              </ul>
                           </li>
                        </ul>
                     </div>

                     <div className='mb-8'>
                        <h2 className='text-xl font-semibold mb-4'>
                           Final URL Suffix
                        </h2>
                        <p className='mb-2'>
                           You can set Final URL Suffix in campaign settings:
                        </p>
                        <div className='bg-gray-100 dark:bg-gray-700 p-3 rounded-md flex justify-between items-center mb-2'>
                           <code className='text-sm break-all'>
                              utm_source=google&utm_medium=cpc&utm_campaign=&#123;campaignid&#125;&utm_term=&#123;keyword&#125;
                           </code>
                           <button
                              onClick={() =>
                                 handleCopy(
                                    'utm_source=google&utm_medium=cpc&utm_campaign={campaignid}&utm_term={keyword}',
                                    setCopied2,
                                 )
                              }
                              className='text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                           >
                              {copied2 ? <CheckIcon /> : <CopyIcon />}
                           </button>
                        </div>
                     </div>

                     <div className='mb-4'>
                        <p className='font-medium'>
                           That's it—you're good to go! 🚀
                        </p>
                        <p className='mt-4 text-blue-700 dark:text-blue-300 bg-blue-50 dark:bg-blue-900/20 p-4 rounded-md border-l-4 border-blue-500'>
                           <strong>Pro Tip:</strong> After implementing UTM
                           parameters, allow 24-48 hours for data to appear in
                           your analytics platform. Check your campaign
                           performance regularly to ensure tracking is working
                           correctly.
                        </p>
                     </div>
                  </>
               )}

               <div className='flex justify-between mt-8'>
                  <button
                     onClick={goToPrevStep}
                     disabled={currentStep === 1}
                     className={`px-4 py-2 rounded-md ${
                        currentStep === 1
                           ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                           : 'text-white hover:opacity-90'
                     }`}
                     style={
                        currentStep === 1 ? {} : { backgroundColor: '#3C76E1' }
                     }
                  >
                     Previous
                  </button>

                  <button
                     onClick={goToNextStep}
                     disabled={currentStep === 2}
                     className={`px-4 py-2 rounded-md ${
                        currentStep === 2
                           ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                           : 'text-white hover:opacity-90'
                     }`}
                     style={
                        currentStep === 2 ? {} : { backgroundColor: '#3C76E1' }
                     }
                  >
                     Next
                  </button>
               </div>
            </div>
         </div>
      </div>
   );
};

export default GoogleAttribution;
