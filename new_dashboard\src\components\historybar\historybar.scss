@use '../../sass/variable.scss';
.history-bar {
   display: flex;
   flex-direction: column;
   flex: 1;
   width: 250px;
   background-color: #ffffff;
   padding: 10px;
   padding-top: 20px;
   height: 100%;
   overflow: scroll;
   &::-webkit-scrollbar {
      width: 8px;
   }
   &::-webkit-scrollbar-track {
      background: #f1f1f1;
      // [data-theme='dark'] & {
      //    background: $background;
      // }
   }
   &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;
      [data-theme='dark'] & {
         background: var(--controls);
      }
   }
   &::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
      [data-theme='dark'] & {
         background: var(--controls-hover);
      }
   }
   .head {
      display: flex;
      justify-content: space-between;
      align-items: baseline;
   }
   transition: all 0.2s ease-in-out;
   // [data-theme='dark'] & {
   //    background-color: $background;
   //    color: $text_color !important;
   //    border-color: #2d3748 !important;
   // }
}

.container {
   display: flex;
   justify-content: space-between;
   font-size: medium;
}
.history-heading {
   font-size: 20px;
   font-weight: 600;
}

.new-chat-button {
   background-color: white;
   color: #437eeb;
   border: 1px solid #437eeb;
   border-radius: 8px;
   padding: 6px 10px;
   margin-bottom: 8px;
   margin-top: 20px;
   cursor: pointer;
   font-size: medium;
   width: 100%;
   height: 27px;
   display: flex;
   align-items: center;
   justify-content: flex-start;
   .newchat-text {
      margin-left: 50px;
   }
   &:hover {
      color: white;
      background-color: #437eeb;
      // [data-theme='dark'] & {
      //    background-color: #437eeb;
      //    color: $text_color;
      // }
   }
   // [data-theme='dark'] & {
   //    background-color: $background;
   //    color: $text_color;
   // }
}

.question-answer-group {
   display: flex;
   flex-direction: column;
   align-items: flex-start;
   gap: 4px;
   font-size: small;
   padding-right: 8px;
}

.history-question {
   font-weight: normal;
   padding-top: 4px;
   padding-bottom: 4px;
   padding-right: 4px;
   padding-left: 4px;
   display: flex;
   justify-content: space-between;
   width: 100%;
   // &:hover {
   //    background-color: $background-surface;
   //    // Dark mode के लिए
   //    [data-theme='dark'] & {
   //       background-color: $background;
   //    }
   // }
   // [data-theme='dark'] & {
   //    background-color: $background;
   // }
   > div {
      flex: 1;
   }
}

.history-question:hover {
   background-color: #437eeb0d;
   cursor: pointer;
}

.formatted-date {
   font-size: 20px;
   text-align: left;
   padding-top: 12px;
   padding-bottom: 12px;
}
// .clear-history-btn {
//    width: 70px;
//    height: 18px;
//    color: black;
//    border: 1px solid #000000;
//    border-radius: 8px;
//    cursor: pointer;
//    font-size: x-small;
// }
.history-bar {
   .load-more-container {
      top: 1%;
      color: #437eeb;
      background-color: #ffffff;
      border: 1px solid #437eeb;
      border-radius: 8px;
      &:hover {
         color: white;
         background-color: #437eeb;
         [data-theme='dark'] & {
            background-color: #437eeb;
         }
      }
      width: 50%;
      margin-left: 25%;
      font-size: small;
      // [data-theme='dark'] & {
      //    background-color: $background;
      // }
   }
   .loading-indicator {
      margin: auto;
      align-items: baseline;
      span {
         margin-left: 20px;
      }
   }
}
