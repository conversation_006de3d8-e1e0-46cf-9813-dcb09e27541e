import { CheckIcon, CloseIcon } from '@chakra-ui/icons';
import { Tag, TagLabel, TagLeftIcon } from '@chakra-ui/react';

interface Props {
   complete: boolean;
   label: 'integrations' | 'flable_pixel' | 'competitors';
   number?: number;
}

interface LabelInfos {
   integrations: {
      completed: string;
      notConnected: string;
   };
   flable_pixel: {
      completed: string;
      notConnected: string;
   };
   competitors: {
      completed: string;
      notConnected: string;
   };
}

const LABEL_INFOS: LabelInfos = {
   integrations: {
      completed: 'Connected',
      notConnected: 'Pending',
   },
   flable_pixel: {
      completed: 'Data Flowing',
      notConnected: 'Not Connected',
   },
   competitors: {
      completed: 'Completed',
      notConnected: 'No Competitors Added',
   },
};

const IntegrationStatusTag = (props: Props) => {
   const { complete, label, number } = props;

   return (
      <Tag size='md' variant='solid' colorScheme={complete ? 'green' : 'red'}>
         <TagLeftIcon as={complete ? CheckIcon : CloseIcon} />
         <TagLabel>
            {complete
               ? LABEL_INFOS[label].completed
               : number
                 ? `${number} ${number === 1 ? 'Connection' : 'Connections'} ${LABEL_INFOS[label].notConnected}`
                 : LABEL_INFOS[label].notConnected}
         </TagLabel>
      </Tag>
   );
};

export default IntegrationStatusTag;
