import { FC } from 'react';
import { useAppSelector } from '../../store/store';
import DateRangeSelect from './date-range';

function PopoverManager() {
   const currentPopover = useAppSelector((state) => state.popover);

   const popoversLookup: Record<string, FC> = {
      DateRangeSelect,
   };

   let renderedPopover;

   if (currentPopover.show && currentPopover.payload) {
      const { popoverProps, popoverType } = currentPopover.payload;

      const ModalComponent = popoversLookup[popoverType];

      if (!ModalComponent) return null;

      renderedPopover = <ModalComponent {...popoverProps} />;
   }
   return renderedPopover;
}
export default PopoverManager;
