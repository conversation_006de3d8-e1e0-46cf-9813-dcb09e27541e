import {
   Flex,
   Heading,
   InputGroup,
   InputLeftElement,
   NumberDecrementStepper,
   NumberIncrementStepper,
   NumberInput,
   NumberInputField,
   NumberInputStepper,
   Text,
   Tooltip,
   useOutsideClick,
   useColorModeValue,
} from '@chakra-ui/react';
import { useRef, useState } from 'react';
import { GrCircleQuestion } from 'react-icons/gr';
import { MdOutlinePercent } from 'react-icons/md';
import { COGS_STRINGS } from '../../../utils/strings/cfo';
import { useAppSelector } from '../../../store/store';
import { useApiMutation } from '../../../hooks/react-query-hooks';
import { CFOKeys } from '../../dashboard/utils/query-keys';
import endPoints, { UpdateCOGSPayload } from '../../../api/service/cfo';
import { useDispatch } from 'react-redux';
import { setCogsFixedRate } from '../../../store/reducer/cfo-reducer';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
function COGS() {
   const { cogsFixedRate } = useAppSelector((state) => state.cfo);
   const [cogsPerc, setcogsPerc] = useState<string>(
      cogsFixedRate.fixed_percent || '',
   );
   const cogsRef = useRef(null);
   const dispatch = useDispatch();

   useOutsideClick({
      ref: cogsRef,
      handler: () => {
         if (cogsPerc != cogsFixedRate.fixed_percent) {
            updateFixedRate({
               clientId: LocalStorageService.getItem(Keys.ClientId) as string,
               fixedPercent: cogsPerc,
               id: cogsFixedRate.id || null,
            });
         }
      },
   });
   const handleSuccess = (data: string, payload?: UpdateCOGSPayload) => {
      if (data) {
         dispatch(
            setCogsFixedRate({
               fixed_percent: payload?.fixedPercent || '',
               id: payload?.id || null,
            }),
         );
      }
   };
   const { mutate: updateFixedRate } = useApiMutation({
      queryKey: [CFOKeys.upsertCogs],
      mutationFn: endPoints.upsertCogs,
      onSuccessHandler: handleSuccess,
   });
   return (
      <Flex
         flexDirection={'column'}
         backgroundColor={useColorModeValue(
            '#F9F9FA',
            'var(--backgroud-surface)',
         )}
         color={useColorModeValue('#000', '#fff')}
         border={'1px solid #C2CBD4'}
         p={4}
         borderRadius={'8px'}
         gap={3}
         m={3}
      >
         <Heading display={'flex'} gap={2} fontSize={'16px'} fontWeight={500}>
            {COGS_STRINGS.fixedTitle}
            <Tooltip
               placement='top'
               hasArrow
               label={COGS_STRINGS.fixedHelpText}
            >
               <span>
                  <GrCircleQuestion />
               </span>
            </Tooltip>
         </Heading>
         <Text fontSize={'16px'}>{COGS_STRINGS.fixedSubTitle}</Text>
         <InputGroup
            width={'50%'}
            backgroundColor={useColorModeValue('white', 'var(--controls)')}
         >
            <InputLeftElement pointerEvents='none'>
               {' '}
               <MdOutlinePercent />
            </InputLeftElement>
            <NumberInput
               ref={cogsRef}
               width={'100%'}
               defaultValue={cogsFixedRate.fixed_percent || ''}
               value={cogsPerc}
               onChange={(value) => setcogsPerc(value)}
            >
               <NumberInputField pl={8} />
               <NumberInputStepper>
                  <NumberIncrementStepper />
                  <NumberDecrementStepper />
               </NumberInputStepper>
            </NumberInput>
         </InputGroup>
      </Flex>
   );
}

export default COGS;
