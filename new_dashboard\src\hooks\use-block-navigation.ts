import { useEffect, useCallback } from 'react';
import { useBlocker } from 'react-router-dom';

const useBlockNavigation = (
   shouldBlock: boolean,
   message: string = 'API call is in progress. Are you sure, you want to leave the page?',
) => {
   useEffect(() => {
      const handleBeforeUnload = (event: BeforeUnloadEvent) => {
         if (shouldBlock) {
            const confirmationMessage = message;
            event.returnValue = confirmationMessage;
            return confirmationMessage;
         }
      };

      window.addEventListener('beforeunload', handleBeforeUnload);

      return () => {
         window.removeEventListener('beforeunload', handleBeforeUnload);
      };
   }, [shouldBlock, message]);

   const handleBlocker = useCallback(() => {
      if (shouldBlock) {
         if (window.confirm(message)) {
            return false;
         }
         return true;
      }
      return false;
   }, [shouldBlock, message]);

   useBlocker(handleBlocker);
};

export default useBlockNavigation;
