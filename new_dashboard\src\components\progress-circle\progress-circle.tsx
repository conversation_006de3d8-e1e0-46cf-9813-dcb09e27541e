import { CircularProgress, Tooltip } from '@chakra-ui/react';

interface Props {
   size: string | number;
   color: string;
   value: number;
   label: string;
}

const ProgressCircle = (props: Props) => {
   const { value, size, color, label } = props;

   return (
      <Tooltip label={label}>
         <CircularProgress size={size} color={color} value={value} />
      </Tooltip>
   );
};

export default ProgressCircle;
