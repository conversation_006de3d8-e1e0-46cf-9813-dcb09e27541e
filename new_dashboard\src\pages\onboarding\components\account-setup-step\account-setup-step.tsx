import {
   Box,
   Heading,
   Text,
   Stack,
   Input,
   Select,
   Button,
   InputGroup,
   InputLeftAddon,
   FormControl,
   FormErrorMessage,
   Flex,
} from '@chakra-ui/react';
import { useAppDispatch, useAppSelector } from '../../../../store/store';
import { useApiMutation } from '../../../../hooks/react-query-hooks';
import { useToast } from '@chakra-ui/react';

import { accountSetupStep } from '../../../../utils/strings/onboarding-strings';
import onboardingEndpoints, {
   ClientDetails,
} from '../../../../api/service/onboarding';
import keys from '../../../../utils/strings/query-keys';
import './account-setup-step.scss';
import {
   AccountDetails,
   setRegisterProgress,
} from '../../../../store/reducer/onboarding-reducer';
import React, { useState } from 'react';
import {
   removeDuplicatesAndSort,
   retrieveOptions,
   splitIndustryCategories,
} from '../../utils/helper';
import { LocalStorageService, Keys } from '../../../../utils/local-storage';
import { closeModal, openModal } from '../../../../store/reducer/modal-reducer';
import { modalTypes } from '../../../../components/modals/modal-types';
import settingsBackendEndpoints from '../../../../api/service/settings';
import { EUROPEAN_COUNTRIES } from '../../utils/onboarding-constants';

interface FormType {
   [key: string]: string;
   agency_name: string;
   agency_url: string;
   company_country: string;
   company_business_type: string;
   company_platform: string;
   company_name: string;
   company_url: string;
   company_traffic: string;
   company_annual_revenue: string;
   company_currency: string;
}

const AccountSetupStep = () => {
   const dispatch = useAppDispatch();
   const toast = useToast();

   const { organizationType, masterList, accountDetails } = useAppSelector(
      (state) => state.onboarding,
   );

   const [form, setForm] = useState<FormType>({
      agency_name: accountDetails?.[0]?.agency_name || '',
      agency_url: accountDetails?.[0]?.agency_url || '',
      company_country: '',
      company_business_type: '',
      company_platform: '',
      company_name: '',
      company_url: '',
      company_traffic: '',
      company_annual_revenue: '',
      company_currency: '',
   });

   const [error, setError] = useState({
      agency_url: '',
      company_url: '',
   });

   const { mutate, isPending } = useApiMutation({
      queryKey: [keys.updateUserDetails],
      mutationFn: onboardingEndpoints.createUserDetails,
      onSuccessHandler: (response) => {
         if (response.status === 'Success') {
            const { accountDetails } = response.details;

            const currentUser = accountDetails.filter(
               (item: AccountDetails) =>
                  item.company_url ===
                  form.company_url.replace(/^www\.|\/+$/g, ''),
            )[0];

            const userDetails = {
               email: LocalStorageService.getItem(Keys.UserName) as string,
               client_id: currentUser.client_id,
            };

            LocalStorageService.setItem(Keys.FlableUserDetails, userDetails);
            LocalStorageService.setItem(Keys.ClientId, currentUser.client_id);

            void settingsBackendEndpoints.updateLanguage({
               client_id: currentUser.client_id,
               language: 'en-us',
            });

            const timezone = EUROPEAN_COUNTRIES.includes(form.company_country)
               ? 'CET'
               : 'IST';

            void settingsBackendEndpoints.updateTimezone({
               client_id: currentUser.client_id,
               timezone: timezone,
            });

            dispatch(setRegisterProgress('Step 3'));
         } else {
            toast({
               title: accountSetupStep.updateUserFailed,
               description: response.message,
               status: 'error',
               duration: 5000,
               isClosable: true,
            });
         }
      },
      onError: (msg) => {
         toast({
            title: accountSetupStep.updateUserFailed,
            description: msg,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      },
   });

   const platformOptions: string[] | undefined = retrieveOptions(
      masterList,
      'Platform',
   );

   const trafficOptions: string[] | undefined = retrieveOptions(
      masterList,
      'Traffic',
   );

   const annualRevenueOptions: string[] | undefined = retrieveOptions(
      masterList,
      'Annual Revenue',
   );

   const countryOptions: string[] | undefined = retrieveOptions(
      masterList,
      'Country',
   );

   const industryOptions: { [key: string]: string[] } | undefined =
      splitIndustryCategories(retrieveOptions(masterList, 'Industry'));

   const currencyOptions: string[] | undefined = removeDuplicatesAndSort(
      retrieveOptions(masterList, 'Currency'),
   );

   const validateURLs = (name: string, value: string) => {
      switch (name) {
         case 'company_url':
            if (!accountSetupStep.urlRegex.test(value)) {
               setError({ ...error, [name]: accountSetupStep.incorrectURL });
            } else {
               setError({ ...error, [name]: '' });
            }
            break;
         case 'agency_url':
            if (!accountSetupStep.urlRegex.test(value)) {
               setError({ ...error, [name]: accountSetupStep.incorrectURL });
            } else {
               setError({ ...error, [name]: '' });
            }
            break;
      }
   };

   const validateForm = () => {
      const commonFields: string[] = ['company_name', 'company_url'];

      const requiredFields: string[] =
         organizationType === 'Individual Business'
            ? commonFields
            : [...commonFields, 'agency_name', 'agency_url'];

      const missingField = requiredFields.find((field: string) => !form[field]);

      if (missingField) {
         toast({
            title: accountSetupStep.missingFieldsHeader,
            description: accountSetupStep.missingFields,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });

         return false;
      }

      return true;
   };

   const handleBack = () => {
      dispatch(setRegisterProgress('Step 1'));
   };

   const handleNext = () => {
      if (validateForm()) {
         dispatch(
            openModal({
               modalType: modalTypes.VERIFY_ACCOUNT_DETAILS,
               modalProps: {
                  organizationType: organizationType,
                  form: form,
                  isPending: isPending,
                  handleConfirmAddAccount: handleConfirmAddAccount,
               },
            }),
         );
      }
   };

   const handleInputChange = (
      event:
         | React.ChangeEvent<HTMLInputElement>
         | React.ChangeEvent<HTMLSelectElement>,
   ) => {
      const inputEvent = event as React.ChangeEvent<HTMLInputElement>;
      const { name, value } = inputEvent.target;
      setForm({ ...form, [name]: value });
      validateURLs(name, value);
   };

   const handleConfirmAddAccount = () => {
      dispatch(closeModal());
      const updateUserPayload: ClientDetails = {
         email_address: LocalStorageService.getItem(Keys.UserName) as string,
         register_progress: 'Step 3',
         organization_type: organizationType,
         agency_name: form.agency_name || null,
         agency_url: form.agency_url.replace(/^www\.|\/+$/g, '') || null,
         company_country: form.company_country,
         company_business_type: form.company_business_type,
         company_platform: form.company_platform,
         company_url: form.company_url.replace(/^www\.|\/+$/g, ''),
         company_traffic: form.company_traffic,
         company_annual_revenue: form.company_annual_revenue,
         company_currency: form.company_currency,
         company_name: form.company_name,
         last_update_date: new Date().toISOString(),
         role_in_organization: 'Admin',
      };
      mutate(updateUserPayload);
   };

   return (
      <>
         <Box
            width='100%'
            height='100%'
            display='flex'
            alignItems='center'
            justifyContent='center'
         >
            <Box
               width='50%'
               height='70%'
               display='flex'
               flexDirection='column'
               alignItems='center'
               justifyContent='center'
               className='add-details-step'
               gap={5}
            >
               <Flex
                  direction='column'
                  alignItems='center'
                  justifyContent='center'
                  width='70%'
               >
                  <Heading fontSize='32px' fontWeight='500'>
                     {accountSetupStep.title}
                  </Heading>
                  <Text
                     fontSize='14px'
                     fontWeight='400'
                     textAlign='center'
                     mt={2}
                     color='gray'
                  >
                     {accountSetupStep.info}
                  </Text>
               </Flex>
               {organizationType === 'Marketing Agency' && (
                  <Box className='agency-details'>
                     <Heading width='100%' fontSize='18px' fontWeight='500'>
                        {accountSetupStep.agency}
                     </Heading>
                     <Stack width='100%' direction='row'>
                        <Input
                           placeholder='Agency Name'
                           fontSize='16px'
                           value={form.agency_name}
                           name='agency_name'
                           onChange={handleInputChange}
                           width='49%'
                           disabled={accountDetails?.length > 0}
                        />
                        <FormControl
                           id='agency_url'
                           isInvalid={!!error.agency_url}
                           width='49%'
                        >
                           <InputGroup size='md'>
                              <InputLeftAddon width='25%' fontSize='14px'>
                                 https://
                              </InputLeftAddon>
                              <Input
                                 placeholder='Agency URL'
                                 fontSize='16px'
                                 value={form.agency_url}
                                 name='agency_url'
                                 onChange={handleInputChange}
                                 disabled={accountDetails?.length > 0}
                              />
                           </InputGroup>
                           {error.agency_url && (
                              <FormErrorMessage ml={1}>
                                 {error.agency_url}
                              </FormErrorMessage>
                           )}
                        </FormControl>
                     </Stack>
                  </Box>
               )}
               <Box className='brand-region-language'>
                  <Box className='brand-info'>
                     <Heading width='100%' fontSize='18px' fontWeight='500'>
                        {accountSetupStep.brand}
                     </Heading>
                     <Stack direction='row' flexWrap='wrap'>
                        <Stack width='100%' direction='row'>
                           <Select
                              fontSize='16px'
                              width='49%'
                              placeholder='Industry'
                              value={form.company_business_type}
                              name='company_business_type'
                              onChange={handleInputChange}
                           >
                              <option hidden></option>
                              {industryOptions &&
                                 Object.keys(industryOptions).map(
                                    (industry) => (
                                       <optgroup
                                          key={industry}
                                          label={industry}
                                       >
                                          {industryOptions[industry]?.map(
                                             (category: string) => (
                                                <option
                                                   key={category}
                                                   value={category}
                                                >
                                                   {category}
                                                </option>
                                             ),
                                          )}
                                       </optgroup>
                                    ),
                                 )}
                           </Select>
                           <Select
                              placeholder='Platform'
                              fontSize='16px'
                              width='49%'
                              value={form.company_platform}
                              name='company_platform'
                              onChange={handleInputChange}
                           >
                              {platformOptions?.map((platform) => (
                                 <option key={platform} value={platform}>
                                    {platform}
                                 </option>
                              ))}
                           </Select>
                        </Stack>
                        <Stack width='100%' direction='row'>
                           <Input
                              placeholder='Company Name'
                              fontSize='16px'
                              value={form.company_name}
                              name='company_name'
                              onChange={handleInputChange}
                              width='49%'
                           />
                           <FormControl
                              id='company_url'
                              isInvalid={!!error.company_url}
                              width='49%'
                           >
                              <InputGroup size='md'>
                                 <InputLeftAddon width='25%' fontSize='14px'>
                                    https://
                                 </InputLeftAddon>
                                 <Input
                                    placeholder='Company URL'
                                    fontSize='16px'
                                    width='100%'
                                    value={form.company_url}
                                    name='company_url'
                                    onChange={handleInputChange}
                                 />
                              </InputGroup>
                              {error.company_url && (
                                 <FormErrorMessage ml={1}>
                                    {error.company_url}
                                 </FormErrorMessage>
                              )}
                           </FormControl>
                        </Stack>

                        <Stack width='100%' direction='row'>
                           <Select
                              placeholder='User Traffic (visits/day)'
                              fontSize='16px'
                              width='49%'
                              value={form.company_traffic}
                              name='company_traffic'
                              onChange={handleInputChange}
                           >
                              {trafficOptions?.map((traffic) => (
                                 <option key={traffic} value={traffic}>
                                    {traffic}
                                 </option>
                              ))}
                           </Select>
                           <Select
                              placeholder='Annual Revenue'
                              fontSize='16px'
                              width='49%'
                              value={form.company_annual_revenue}
                              name='company_annual_revenue'
                              onChange={handleInputChange}
                           >
                              {annualRevenueOptions?.map((annualRevenue) => (
                                 <option
                                    key={annualRevenue}
                                    value={annualRevenue}
                                 >
                                    {annualRevenue}
                                 </option>
                              ))}
                           </Select>
                        </Stack>
                     </Stack>
                  </Box>
                  <Box className='region-language'>
                     <Heading width='100%' fontSize='18px' fontWeight='500'>
                        {accountSetupStep.regionLanguage}
                     </Heading>
                     <Stack direction='row'>
                        <Select
                           placeholder='Country'
                           fontSize='16px'
                           value={form.company_country}
                           name='company_country'
                           onChange={handleInputChange}
                        >
                           {countryOptions?.map((country: string) => (
                              <option key={country} value={country}>
                                 {country}
                              </option>
                           ))}
                        </Select>
                        <Select
                           placeholder='Currency Used'
                           fontSize='16px'
                           value={form.company_currency}
                           name='company_currency'
                           onChange={handleInputChange}
                        >
                           {currencyOptions?.map((currency: string) => (
                              <option key={currency} value={currency}>
                                 {currency}
                              </option>
                           ))}
                        </Select>
                     </Stack>
                  </Box>
               </Box>
               <Flex gap={3} justifyContent='space-between' mt={3}>
                  {!(accountDetails.length > 0) && (
                     <Button colorScheme='gray' onClick={handleBack}>
                        <Text fontSize='16px' fontWeight={500}>
                           Back
                        </Text>
                     </Button>
                  )}
                  <Button
                     colorScheme='blue'
                     onClick={handleNext}
                     disabled={
                        organizationType === 'Marketing Agency'
                           ? !form.agency_url ||
                             !!error.agency_url ||
                             !form.company_url ||
                             !!error.company_url
                           : !form.company_url || !!error.company_url
                     }
                     isLoading={isPending}
                  >
                     <Text fontSize='16px' fontWeight={500}>
                        Next
                     </Text>
                  </Button>
               </Flex>
            </Box>
         </Box>
      </>
   );
};

export default AccountSetupStep;
