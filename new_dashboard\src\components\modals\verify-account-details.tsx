import { useState } from 'react';

import { <PERSON>, <PERSON><PERSON>, Flex, ModalFooter } from '@chakra-ui/react';
import { CheckCircleIcon } from '@chakra-ui/icons';

import ModalWrapper from './modal-wrapper';
import { useAppDispatch, useAppSelector } from '../../store/store';
import { closeModal } from '../../store/reducer/modal-reducer';

interface FormType {
   [key: string]: string;
   agency_name: string;
   agency_url: string;
   company_country: string;
   company_business_type: string;
   company_platform: string;
   company_name: string;
   company_url: string;
   company_traffic: string;
   company_annual_revenue: string;
   company_currency: string;
}

interface Validity {
   text: 'Validate' | 'Confirm';
   colorScheme: 'blue' | 'green';
   validated: boolean;
}

interface Validated {
   agency_url: Validity;
   company_url: Validity;
}

function VerifyAccountDetails() {
   const dispatch = useAppDispatch();

   const currentModal = useAppSelector((state) => state.modal);

   const [validated, setValidated] = useState<Validated>({
      agency_url: { text: 'Validate', colorScheme: 'blue', validated: false },
      company_url: { text: 'Validate', colorScheme: 'blue', validated: false },
   });

   const organizationType: 'Individual Business' | 'Marketing Agency' =
      currentModal.payload?.modalProps?.organizationType as
         | 'Individual Business'
         | 'Marketing Agency';
   const form: FormType = currentModal.payload?.modalProps?.form as FormType;
   const isPending: boolean = currentModal.payload?.modalProps
      ?.isPending as boolean;
   const handleConfirmAddAccount = currentModal.payload?.modalProps
      ?.handleConfirmAddAccount as () => void;

   const handleValidate = (url_type: 'agency_url' | 'company_url') => {
      setValidated((prev: Validated) => {
         prev[url_type].text === 'Validate' &&
            window.open(
               `https://${form[url_type]}`,
               '_blank',
               'noopener,noreferrer',
            );

         return prev[url_type].text === 'Validate'
            ? {
                 ...prev,
                 [url_type]: {
                    text: 'Confirm',
                    colorScheme: 'green',
                    validated: false,
                 },
              }
            : {
                 ...prev,
                 [url_type]: {
                    text: 'Confirmed',
                    colorScheme: 'green',
                    validated: true,
                 },
              };
      });
   };

   const LABELS: string[] = [
      'Organization Type',
      ...(organizationType === 'Marketing Agency'
         ? ['Agency Name', 'Agency URL']
         : []),
      'Industry',
      'Platform',
      'Company Name',
      'Company URL',
      'User Traffic',
      'Annual Revenue',
      'Country',
      'Currency',
   ];

   const returnModalFooter = () => {
      return (
         <ModalFooter>
            <Button
               colorScheme='gray'
               mr={3}
               onClick={() => dispatch(closeModal())}
            >
               Back
            </Button>
            <Button
               colorScheme='blue'
               disabled={
                  organizationType === 'Marketing Agency'
                     ? !validated.agency_url.validated ||
                       !validated.company_url.validated
                     : !validated.company_url.validated
               }
               onClick={handleConfirmAddAccount}
               isLoading={isPending}
            >
               Submit
            </Button>
         </ModalFooter>
      );
   };

   return (
      <ModalWrapper
         size='2xl'
         heading='Verify Account Details'
         footer={returnModalFooter()}
         noCloseBtn
      >
         <>
            <Text>
               {`Please verify your details before proceeding to the next
                    step as this is a crucial part of the onboarding process.
                    Click on `}{' '}
               <span style={{ fontWeight: 'bold' }}>validate</span>
               <Text as='span'>{` to verify the entered url${organizationType === 'Marketing Agency' ? 's' : ''} and
                    click on`}</Text>{' '}
               <span style={{ fontWeight: 'bold' }}>confirm</span>{' '}
               <Text as='span'>{`if they are correct.`}</Text>
            </Text>
            <Flex mt={3} direction='row' gap={3}>
               <Flex direction='column' gap={1}>
                  {LABELS.map((label) => (
                     <Text key={label}>{label}</Text>
                  ))}
               </Flex>
               <Flex direction='column' gap={1}>
                  {LABELS.map((label) => (
                     <Text key={label}>:</Text>
                  ))}
               </Flex>
               <Flex direction='column' fontWeight='bold' gap={1}>
                  <Text>{organizationType}</Text>
                  {organizationType === 'Marketing Agency' && (
                     <>
                        <Text>{form.agency_name}</Text>
                        <Flex alignItems='center' gap={3}>
                           <Text>{`https://${form.agency_url}`} </Text>
                           {!validated.agency_url.validated ? (
                              <Button
                                 size='xs'
                                 name='agency_url'
                                 colorScheme={validated.agency_url.colorScheme}
                                 onClick={() => handleValidate('agency_url')}
                              >
                                 {validated.agency_url.text}
                              </Button>
                           ) : (
                              <CheckCircleIcon color='green' />
                           )}
                        </Flex>
                     </>
                  )}
                  <Text>{form.company_business_type}</Text>
                  <Text>{form.company_platform}</Text>
                  <Text>{form.company_name}</Text>
                  <Flex alignItems='center' gap={3}>
                     <Text>{`https://${form.company_url}`}</Text>
                     {!validated.company_url.validated ? (
                        <Button
                           size='xs'
                           name='company_url'
                           colorScheme={validated.company_url.colorScheme}
                           onClick={() => handleValidate('company_url')}
                        >
                           {validated.company_url.text}
                        </Button>
                     ) : (
                        <CheckCircleIcon color='green' />
                     )}
                  </Flex>
                  <Text>{form.company_traffic}</Text>
                  <Text>{form.company_annual_revenue}</Text>
                  <Text>{form.company_country}</Text>
                  <Text>{form.company_currency}</Text>
               </Flex>
            </Flex>
         </>
      </ModalWrapper>
   );
}

export default VerifyAccountDetails;
