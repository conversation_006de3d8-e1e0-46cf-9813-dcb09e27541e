import { Text } from '@chakra-ui/react';
import {
   endEdornment,
   noZeroKPI,
   startEdornment,
} from '../../../utils/strings/kpi-constants';
import { getFormattedVal, toHHMMSS } from '../utils/helpers';
import { ValueProps } from '../utils/interface';

function Value(props: ValueProps) {
   const { kpi, totalVal, fontSize, rightAlign } = props;
   let value =
      kpi.kpi_unit == 'time'
         ? toHHMMSS(totalVal)
         : getFormattedVal(Math.round(totalVal * 100) / 100);

   if (!totalVal && noZeroKPI.includes(kpi.kpi_names)) value = 'N/A';
   const showEndornment = value !== 'N/A';
   return (
      <Text
         fontSize={fontSize || 24}
         textAlign={rightAlign ? 'right' : 'left'}
         fontWeight={700}
         display={'flex'}
         justifyContent={rightAlign ? 'flex-end' : 'flex-start'}
         gap={1}
      >
         {showEndornment && (startEdornment[kpi.kpi_type] || '')}
         <span>{value}</span>
         {showEndornment && (endEdornment[kpi.kpi_type] || '')}
      </Text>
   );
}

export default Value;
