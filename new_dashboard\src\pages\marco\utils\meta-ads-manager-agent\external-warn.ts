import { useEffect } from 'react';

const usePageUnloadWarning = (isCampaignInProgress: boolean) => {
   useEffect(() => {
      const handleBeforeUnload = (event: BeforeUnloadEvent) => {
         if (isCampaignInProgress) {
            event.preventDefault();
            event.returnValue =
               'You have an active campaign session. Leaving will cause data loss.';
         }
      };

      window.addEventListener('beforeunload', handleBeforeUnload);
      return () => {
         window.removeEventListener('beforeunload', handleBeforeUnload);
      };
   }, [isCampaignInProgress]);
};

export default usePageUnloadWarning;
