import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON><PERSON>rovider } from '@chakra-ui/react';
import { Provider } from 'react-redux';
import store from './store/store';
import App from './App';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import 'intro.js/introjs.css';

const queryClient = new QueryClient();

ReactDOM.createRoot(document.getElementById('root')!).render(
   // <React.StrictMode>
   <Provider store={store}>
      <ChakraProvider resetCSS={false}>
         <QueryClientProvider client={queryClient}>
            <App />
         </QueryClientProvider>
      </ChakraProvider>
   </Provider>,
   // </React.StrictMode>,
);
