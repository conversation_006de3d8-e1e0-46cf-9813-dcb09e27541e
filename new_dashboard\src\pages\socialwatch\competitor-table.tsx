import { useAppDispatch } from '../../store/store';
import { openModal } from '../../store/reducer/modal-reducer';
import { modalTypes } from '../../components/modals/modal-types';
import './competitor-table.scss';
import { CompetitorData } from './interface';
import facebook_icon from '../../assets/icons/facebook_logo.svg';
import instagram_icon from '../../assets/icons/instagram_logo.svg';
import twitter_icon from '../../assets/icons/twitter_logo.svg';
import linkedin_icon from '../../assets/icons/linkedin_logo.svg';
import { contentIdeationStrings } from '../../utils/strings/content-ideation';
import useIntegrationConnectionDetails from '../../hooks/use-integration-connection-details';

const checkPlatform = (platform: string) => {
   switch (platform) {
      case 'Instagram':
         return instagram_icon;
         break;
      case 'Linkedin':
         return linkedin_icon;
         break;
      case 'Twitter':
         return twitter_icon;
         break;
      case 'Facebook':
         return facebook_icon;
         break;
      default:
         break;
   }
};

function TableHeading({
   data,
   handleBackToWrapper,
}: {
   data: CompetitorData | null;
   handleBackToWrapper: () => void;
}) {
   const dispatch = useAppDispatch();

   const { mediaTypes } = useIntegrationConnectionDetails();

   function handleOpen(post_caption: string, competitor: string) {
      const text = `competitior name is - ${competitor} ,${contentIdeationStrings.CompetitorPrompt}${post_caption}`;
      dispatch(
         openModal({
            modalType: modalTypes.SOCIAL_MEDIA_CHANNEL_MODAL,
            modalProps:
               mediaTypes.length > 0
                  ? { options: mediaTypes, text, handleBackToWrapper }
                  : { undefined },
         }),
      );
   }
   return (
      <div className='content-ideation'>
         <div className='table-container'>
            <table>
               <thead>
                  <tr>
                     <th>
                        <b>Company</b>
                     </th>
                     <th>
                        <b>Post Title</b>
                     </th>
                     <th>
                        <b>Post Type</b>
                     </th>
                     <th>
                        <b>Engagement</b>
                     </th>
                     <th>
                        <b>Link</b>
                     </th>
                     <th>
                        <b>Action</b>
                     </th>
                  </tr>
               </thead>
               <tbody>
                  {data?.results.map((competitor, index) =>
                     competitor.top_posts.map((post, postIndex) => (
                        <tr key={`${index}-${postIndex}`}>
                           <td>
                              {postIndex === 0 ? competitor.competitor : ''}
                           </td>
                           <td>{post.post_caption.substring(0, 50)}...</td>
                           <td>{post.category}</td>
                           <td>{post.engagementRate}%</td>
                           <td>
                              <a
                                 href={post.url}
                                 target='_blank'
                                 rel='noopener noreferrer'
                              >
                                 <p>View</p>
                                 {
                                    <img
                                       src={checkPlatform(competitor.platform)}
                                    />
                                 }
                              </a>
                           </td>
                           <td>
                              <button
                                 className='btn-generate'
                                 onClick={() =>
                                    handleOpen(
                                       post.post_caption,
                                       competitor.competitor,
                                    )
                                 }
                              >
                                 Generate
                              </button>
                           </td>
                        </tr>
                     )),
                  )}
               </tbody>
            </table>
         </div>
      </div>
   );
}

function CompetitorTable({
   width,
   data,
   handleBackToWrapper,
}: {
   width: number;
   data: CompetitorData | null;
   handleBackToWrapper: () => void;
}) {
   return (
      <div className='competitorTableContainer' style={{ width }}>
         <TableHeading data={data} handleBackToWrapper={handleBackToWrapper} />
      </div>
   );
}
export default CompetitorTable;
