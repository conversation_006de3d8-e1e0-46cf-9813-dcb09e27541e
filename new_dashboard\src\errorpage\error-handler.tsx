import { Component, ErrorInfo, ReactNode } from 'react';
import ErrorPage from './error-page';

interface ErrorHandlerProps {
   children: ReactNode;
}
interface ErrorHandlerState {
   error: string;
}

class ErrorHandler extends Component<ErrorHandlerProps, ErrorHandlerState> {
   constructor(props: ErrorHandlerProps) {
      super(props);
      this.state = { error: '' };
   }

   componentDidCatch(error: Error, errorInfo: ErrorInfo) {
      this.setState({ error: `${error.name}: ${error.message}` });
      console.log(error, errorInfo);
   }
   resetError = () => {
      this.setState({ error: '' });
   };

   render() {
      if (this.state.error) {
         return (
            <ErrorPage err={this.state.error} resetError={this.resetError} />
         );
      }

      return this.props.children;
   }
}

export default ErrorHandler;
