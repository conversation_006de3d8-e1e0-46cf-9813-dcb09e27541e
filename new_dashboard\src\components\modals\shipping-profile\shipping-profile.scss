.shipping-profile,
.delete-record {
   min-width: 40vw;
   .chakra-modal__header {
      font-size: 24px;
      font-weight: 700;
      color: black;
      padding: 10px 20px;
   }
   .chakra-modal__body {
      max-height: 80vh;
      overflow-y: auto;
      padding: 0px 20px 10px 20px;
      overflow-x: hidden;
   }
   .chakra-modal__body::-webkit-scrollbar {
      width: 5px;
   }
   .chakra-modal__body::-webkit-scrollbar-thumb {
      background: #4d4d4daa;
      border-radius: 5px;
   }
   .countries::-webkit-scrollbar {
      width: 5px;
   }
   .countries::-webkit-scrollbar-thumb {
      background: #4d4d4daa;
      border-radius: 5px;
   }
   .cursor-pointer {
      cursor: pointer;
   }
}
.variable-expense,
.fixed-expense {
   .creatable-text {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 5px;
   }
   min-width: 30vw;
   &.chakra-modal__content {
      margin-top: 4rem !important;
   }
   .chakra-modal__header {
      font-size: 24px;
      font-weight: 700;
      color: black;
      padding: 10px 20px;
   }
   .chakra-modal__body {
      max-height: 80vh;
      overflow-y: auto;
      padding: 10px 20px 10px 20px;
      overflow-x: hidden;
      border-top: 1px solid #c2cbd4;
   }
   .chakra-modal__body::-webkit-scrollbar {
      width: 5px;
   }
   .chakra-modal__body::-webkit-scrollbar-thumb {
      background: #4d4d4daa;
      border-radius: 5px;
   }
   .countries::-webkit-scrollbar {
      width: 5px;
   }
   .countries::-webkit-scrollbar-thumb {
      background: #4d4d4daa;
      border-radius: 5px;
   }
   .cursor-pointer {
      cursor: pointer;
   }
   form > div {
      width: 60%;
      padding-top: 10px;
   }
}
.err-message {
   font-size: 12px;
   color: red;
}
.shipping-rate-fields {
   margin-inline-end: 0 !important;
   -webkit-margin-start: 0 !important;
   margin-top: 0;
   margin-bottom: 0;
   border: none;
   th,
   td {
      font-size: 17px;
      font-weight: 400;
      color: black;
      border: none;
      padding: 5px;
   }

   th {
      text-transform: none;
      font-family: inherit;
   }
}
