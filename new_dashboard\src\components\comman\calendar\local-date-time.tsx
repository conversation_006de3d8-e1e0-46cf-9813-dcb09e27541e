import React, { useState, useEffect } from 'react';

interface DateTimeProps {
   selectedDate: string;
   setSelectedDate: (date: string) => void;
}

const LocalDateTime: React.FC<DateTimeProps> = ({
   selectedDate,
   setSelectedDate,
}) => {
   const [date, setDate] = useState<string>(selectedDate);

   useEffect(() => {
      if (!selectedDate) {
         const now = new Date();
         const offset = now.getTimezoneOffset() * 60000;
         const localISOTime = new Date(now.getTime() - offset)
            .toISOString()
            .substring(0, 16);
         setDate(localISOTime);
         setSelectedDate(localISOTime);
      }
   }, [selectedDate, setSelectedDate]);

   const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newDate = e.target.value;
      setDate(newDate);
      setSelectedDate(newDate);
   };

   return (
      <div>
         <label htmlFor='datetime'>Select Date and Time:</label>
         <input
            type='datetime-local'
            id='datetime'
            value={date}
            onChange={handleDateChange}
            min={new Date().toISOString().substring(0, 16)} // Prevent selecting past date and time
         />
      </div>
   );
};

export default LocalDateTime;
