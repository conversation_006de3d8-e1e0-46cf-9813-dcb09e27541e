import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { connectDisconnectToIThinkLogistics } from '../utils';
import { Keys, LocalStorageService } from '../../../utils/local-storage';

import StoreConnectionSteps from './woocommerce-steps';
import CommerceIntegrationLayout from './commerce-integration-layout';
import Input from './Input';
import { AuthUser } from '../../../types/auth';
import image from '../images/integrations/ithinklogistics.jpeg';
import { ithinklogisticsIntegrationSteps } from '../utils/constant';

interface FormFields {
   channelName: string;
   access_token: string;
   secret_key: string;
}

interface ApiError {
   success: boolean;
   message: string;
}

const SellerPanel: React.FC<{
   onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
   trying: boolean;
   onConnect: (e: React.FormEvent<HTMLFormElement>) => void;
   access_token: string;
   secret_key: string;
   apiError: ApiError | null;
}> = ({ onChange, trying, onConnect, access_token, secret_key, apiError }) => (
   <div className='seller-panel'>
      <div className='seller-panel__headings'>
         <h5 className='title'>Seller Panel</h5>
         <p className='description'>
            Please provide the following credentials for iThink Logistics:
         </p>
      </div>
      <form className='seller-panel__form' onSubmit={onConnect}>
         <div className='inputs'>
            <Input
               type='password'
               id='access-token'
               label='API Access Token'
               placeholder='Enter your access token...'
               name='access_token'
               value={access_token}
               onChange={onChange}
            />
            <Input
               type='password'
               id='secret-key'
               label='Secret Key'
               placeholder='Enter your secret key...'
               name='secret_key'
               value={secret_key}
               onChange={onChange}
            />
         </div>
         {apiError && apiError.message && (
            <div
               className={`api-response ${apiError.success ? 'api-response__success' : 'api-response__error'}`}
            >
               <h3 className='api-response__message'>{apiError.message}</h3>
            </div>
         )}
         <button
            disabled={trying}
            className='commerce-integration__button'
            type='submit'
         >
            {trying ? 'Connecting...' : 'Connect'}
         </button>
      </form>
   </div>
);

const IThinkLogisticsForm: React.FC = () => {
   const navigate = useNavigate();
   const [trying, setTrying] = useState<boolean>(false);
   const [apiError, setApiError] = useState<ApiError | null>(null);

   const client_id = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   )?.client_id;

   const [unmounted, setUnmounted] = useState<boolean>(false);

   const defaultState: FormFields = {
      channelName: 'ilogistics',
      access_token: '',
      secret_key: '',
   };

   const [formFields, setFormFields] = useState<FormFields>(defaultState);

   const handleChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
         if (unmounted) return;
         const { name, value } = e.target;
         setFormFields((prev) => ({ ...prev, [name]: value }));
      },
      [unmounted],
   );

   const handleConnect = useCallback(
      (e: React.FormEvent<HTMLFormElement>) => {
         if (!client_id) return;
         e.preventDefault();
         if (unmounted) return;
         const { access_token, secret_key } = formFields;
         void (async () => {
            try {
               setTrying(true);
               setApiError({
                  success: false,
                  message: '',
               });
               await connectDisconnectToIThinkLogistics({
                  channel_name: 'ithinklogistics',
                  client_id,
                  admin_access_token: access_token,
                  channel_secret: secret_key,
                  isConnect: true,
               });
               setFormFields(defaultState);
               setApiError({
                  success: true,
                  message: 'Connection Established, Redirecting...',
               });
               setTimeout(() => {
                  navigate('/integrations');
               }, 3000);
            } catch (err) {
               let errMessage = 'Error connecting to iThink Logistics';
               if (
                  err &&
                  typeof err === 'object' &&
                  'response' in err &&
                  err.response &&
                  typeof err.response === 'object' &&
                  'data' in err.response &&
                  err.response.data &&
                  typeof err.response.data === 'object' &&
                  'message' in err.response.data
               ) {
                  errMessage =
                     (err.response.data as { message?: string }).message ||
                     errMessage;
               }
               setApiError({
                  success: false,
                  message: errMessage,
               });
            } finally {
               setTrying(false);
            }
         })();
      },
      [formFields, defaultState, unmounted],
   );

   useEffect(() => {
      setUnmounted(false);
      return () => {
         setUnmounted(true);
         setFormFields(defaultState);
         setApiError(null);
      };
   }, []);

   const leftContent = (
      <StoreConnectionSteps steps={ithinklogisticsIntegrationSteps} />
   );

   return (
      <CommerceIntegrationLayout leftContent={leftContent} image={image}>
         <SellerPanel
            onChange={handleChange}
            trying={trying}
            onConnect={handleConnect}
            access_token={formFields.access_token}
            secret_key={formFields.secret_key}
            apiError={apiError}
         />
      </CommerceIntegrationLayout>
   );
};

export default IThinkLogisticsForm;
