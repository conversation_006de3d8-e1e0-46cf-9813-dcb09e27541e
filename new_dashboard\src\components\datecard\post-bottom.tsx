import Calendar from '../comman/calendar';

import { ChangeEvent, Dispatch, SetStateAction } from 'react';
import { PageInfo } from '../../types/social-watch';
import { ScheduledTime } from './date-card';
import { Button, Flex, Icon, Select, VStack } from '@chakra-ui/react';
import { FiTrash } from 'react-icons/fi';
import { socialLabel } from '../../utils/strings/socialwatch-strings';
import useIntegrationConnectionDetails from '../../hooks/use-integration-connection-details';
import { useAppSelector } from '../../store/store';

interface GetPostBottomSectionProps {
   id?: string;
   selected: string;
   scheduledTime: ScheduledTime[];
   setScheduledTime: Dispatch<SetStateAction<ScheduledTime[]>>;
   selectedPage: PageInfo | null;
   setSelectedPage: Dispatch<SetStateAction<PageInfo | null>>;
}

export default function GetPostBottomSection(props: GetPostBottomSectionProps) {
   const {
      id,
      selected,
      scheduledTime,
      setScheduledTime,
      selectedPage,
      setSelectedPage,
   } = props;

   const { mediaTypes } = useIntegrationConnectionDetails();
   const { connections } = useAppSelector((state) => state.integration);

   if (selected === 'Post now') return null;
   const handleMediaChange = (
      e: ChangeEvent<HTMLSelectElement>,
      idx: number,
   ) => {
      if (!e.target.value) return;
      setScheduledTime((prev) => {
         const newData = [...prev];
         newData[idx].media = e.target.value;
         newData[idx].dateTime = new Date();
         return newData;
      });
   };
   const handleDelete = (idx: number) => {
      setScheduledTime((prev) => {
         const newData = [...prev];
         newData.splice(idx, 1);
         return newData;
      });
   };
   const handleMedaDateTime = (dateTime: string | Date, idx: number) => {
      setScheduledTime((prev) => {
         const newData = [...prev];
         newData[idx].dateTime = dateTime;
         return newData;
      });
   };
   const addSchedule = () => {
      const availSchedules = mediaTypes.filter((media) => {
         return !scheduledTime.some((scheduled) => scheduled.media === media);
      });
      if (availSchedules.length) {
         setScheduledTime((prev) => {
            const newData = [...prev];
            newData.push({
               media: availSchedules[0],
               dateTime: new Date(),
            });
            return newData;
         });
      }
   };
   const handlePageSelect = (e: ChangeEvent<HTMLSelectElement>) => {
      if (!e.target.value) return setSelectedPage(null);
      setSelectedPage(
         connections.linkedin?.pages.find(
            (page) => page.id === e.target.value,
         ) || null,
      );
   };
   const showAddSchedule = () => {
      return scheduledTime.length !== mediaTypes.length;
   };
   return (
      <VStack alignItems={'flex-start'} padding={5}>
         {scheduledTime.map((scheduled, idx) => (
            <Flex
               align='center'
               justify='space-between'
               width={'-webkit-fill-available'}
               gap={{ sm: 1, lg: 3 }}
               key={idx}
            >
               <Flex gap={{ sm: 1, lg: 3 }}>
                  <Select
                     id={id}
                     placeholder='Select Now'
                     value={scheduled.media}
                     onChange={(e) => handleMediaChange(e, idx)}
                  >
                     {mediaTypes.map((mediaType) => (
                        <option
                           key={mediaType}
                           value={mediaType}
                           disabled={scheduledTime.some(
                              (x) => x.media === mediaType,
                           )}
                        >
                           {socialLabel[mediaType] || mediaType}
                        </option>
                     ))}
                  </Select>
                  {scheduled.media == 'linkedin' && (
                     <Select
                        placeholder='Where to Post'
                        minWidth={'fit-content'}
                        value={selectedPage?.id}
                        onChange={handlePageSelect}
                     >
                        {connections.linkedin?.pages.map((page) => (
                           <option key={page.id} value={page.id}>
                              {page.name}
                              {`${!page.page ? '  (Self)' : ''}`}
                           </option>
                        ))}
                     </Select>
                  )}
                  <Calendar
                     setSelectedDate={(dt: Date | string) =>
                        handleMedaDateTime(dt, idx)
                     }
                     selectedDate={scheduled.dateTime}
                  />
               </Flex>
               {scheduledTime.length > 1 && (
                  <Icon
                     cursor={'pointer'}
                     as={FiTrash}
                     onClick={() => handleDelete(idx)}
                  />
               )}
            </Flex>
         ))}

         {showAddSchedule() && (
            <Button color={'#437EEB'} background={'none'} onClick={addSchedule}>
               + Add more scheduled times
            </Button>
         )}
      </VStack>
   );
}
