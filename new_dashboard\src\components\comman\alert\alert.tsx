import {
   Alert,
   AlertIcon,
   AlertTitle,
   AlertDescription,
   CloseButton,
} from '@chakra-ui/react';

interface AlertProps {
   status: 'success' | 'error' | 'warning' | 'info';
   title: string;
   description: string;
   onClose: () => void;
}

function CustomAlert({ status, title, description, onClose }: AlertProps) {
   return (
      <Alert
         status={status}
         variant='subtle'
         flexDirection='column'
         alignItems='center'
         justifyContent='center'
         textAlign='center'
         rounded='md'
      >
         <AlertIcon boxSize='40px' mr={0} />
         <AlertTitle mt={4} mb={1} fontSize='lg'>
            {title}
         </AlertTitle>
         <AlertDescription maxWidth='sm'>{description}</AlertDescription>
         <CloseButton
            position='absolute'
            right='8px'
            top='8px'
            onClick={onClose}
         />
      </Alert>
   );
}

export default CustomAlert;
