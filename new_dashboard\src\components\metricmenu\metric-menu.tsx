import React from 'react';
import './metric-menu.scss';

interface MetricMenuSelectionProps {
   onSelect: (selectedMetric: string) => void;
   dynamicData: string[];
}

const MetricMenuSelection: React.FC<MetricMenuSelectionProps> = ({
   onSelect,
   dynamicData,
}) => {
   const handleSelect = (metric: string) => {
      onSelect(metric);
   };

   return (
      <div className='metric-menu'>
         {dynamicData.map((metric, index) => (
            <button
               key={index}
               className='metric-menu-btn'
               onClick={() => handleSelect(metric)}
            >
               {metric}
            </button>
         ))}
      </div>
   );
};

export default MetricMenuSelection;
