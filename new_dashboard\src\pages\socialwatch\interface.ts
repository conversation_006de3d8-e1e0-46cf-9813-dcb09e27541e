export interface UserDetails {
   client_id: string;
   fullName: string;
   email: string;
   razorpay_customerid: string | null;
}
export interface GetTrendsPayload {
   client_id: string;
   data: string;
}
export interface TrendData {
   key: string;
   title: string;
   url: string;
   snippet: string;
}
export interface GetTrendsResponse {
   trends: TrendData[];
}
export interface PersonalisedData {
   Instragram: string[];
   Linkedin: string[];
   Twitter: string[];
}

export interface GetPersonalisedResponse {
   personalised_content_ideas: PersonalisedData;
}
interface TopPost {
   category: string;
   post_caption: string;
   url: string;
   engagementRate: number;
}

interface Competitor {
   competitor: string;
   platform: string;
   top_posts: TopPost[];
}

export interface CompetitorData {
   results: Competitor[];
}
export interface ImageTextData {
   idea1: string;
   idea2: string;
   idea3: string;
   idea4: string;
   idea5: string;
   idea6: string;
}
