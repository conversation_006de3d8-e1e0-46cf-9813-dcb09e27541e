import { Button } from '@chakra-ui/react';
import PopoverWrapper from './popover-wrapper';
import { FaChevronDown, FaRegCalendarAlt } from 'react-icons/fa';

function DateRangeSelect() {
   return (
      <PopoverWrapper trigger={<Trigger />}>
         <p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Eum ad</p>
      </PopoverWrapper>
   );
}

function Trigger() {
   return (
      <Button
         color='#437EEB'
         className='btn data-label'
         minWidth={'fit-content'}
         display={'flex'}
         alignItems={'center'}
         gap={4}
      >
         <FaRegCalendarAlt width={'12px'} />{' '}
         {/* <span> {getLabel(initialRange.dateRange[0])}</span>{' '} */}
         <FaChevronDown width={'12px'} />
      </Button>
   );
}

export default DateRangeSelect;
