import React, { useState } from 'react';
import { Text, Button } from '@chakra-ui/react';

interface TruncatableTextProps {
   text: string;
   maxLength: number;
   fontSize?: string | number;
}

const TruncatableText: React.FC<TruncatableTextProps> = ({
   text,
   maxLength,
   fontSize = 'lg',
}) => {
   const [isTruncated, setIsTruncated] = useState(true);

   const toggleTruncation = () => {
      setIsTruncated(!isTruncated);
   };
   const displayText =
      isTruncated && text.length > maxLength
         ? `${text.substring(0, maxLength)}...`
         : text;

   return (
      <div>
         <Text fontSize={fontSize}>{displayText}</Text>
         {text.length > maxLength && (
            <Button size='sm' variant='link' onClick={toggleTruncation}>
               {isTruncated ? 'Show More' : 'Show Less'}
            </Button>
         )}
      </div>
   );
};

export default TruncatableText;
