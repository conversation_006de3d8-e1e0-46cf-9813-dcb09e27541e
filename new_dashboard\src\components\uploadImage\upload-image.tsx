import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { encode } from '../../api/service/social-watch';
import { FaCloudUploadAlt as CloudUploadIcon } from 'react-icons/fa';

interface FileUploadProps {
   setImage_url: (url: string) => void;
}

// interface FileWithPreview extends File {
//     preview: string;
//     name: string;
// }
const FileUpload: React.FC<FileUploadProps> = ({ setImage_url }) => {
   const [acceptedFiles, setAcceptedFiles] = useState<File[]>([]);

   const onDrop = useCallback((acceptedFiles: File[]) => {
      const file = acceptedFiles[0];
      // console.log('file', file);
      if (file) {
         const reader = new FileReader();
         reader.readAsDataURL(file);
         reader.onload = () => {
            setImage_url(reader.result as string);
         };
         reader.onloadend = async () => {
            // console.log('reader', reader.result);
            const base64String = (reader.result as string).replace(
               /^data:.+;base64,/,
               '',
            );

            console.log('BASE 64', base64String);

            try {
               const formData = new FormData();
               formData.append('image_url', acceptedFiles[0]);

               const response = await encode(formData);
               if (response !== null) {
                  // console.log('Upload success:', response);
               }
            } catch (error) {
               console.error('Upload error:', error);
               setAcceptedFiles([]);
            }
         };
         reader.readAsDataURL(acceptedFiles[0]);
         setAcceptedFiles(
            acceptedFiles.map((file) =>
               Object.assign({}, file, { preview: URL.createObjectURL(file) }),
            ),
         );
      }
   }, []);

   const { getRootProps, getInputProps, isDragActive } = useDropzone({
      onDrop,
   });

   return (
      <div {...getRootProps()} className='dropzone'>
         <input {...getInputProps()} />
         <CloudUploadIcon className='cloud-upload-icon' />
         {isDragActive ? (
            <p>Drop the files here...</p>
         ) : (
            <p>Drag 'n' drop some files here, or click to select files</p>
         )}

         {/* preview image */}
         {acceptedFiles.length > 0 && (
            <div>
               <h4>Preview:</h4>
               <img
                  src={URL.createObjectURL(acceptedFiles[0])}
                  alt='preview'
                  style={{ width: '100%' }}
               />
            </div>
         )}
      </div>
   );
};

export default FileUpload;
