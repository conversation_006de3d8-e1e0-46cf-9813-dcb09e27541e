import '../pulse.scss';
import DateRangeSelect from '../../dashboard/components/date-select';
import { Box, Flex, Grid, Heading, Input, Text } from '@chakra-ui/react';
import { useAppSelector, useAppDispatch } from '../../../store/store';

import TrackedCard from '../components/tracked-card';
import { useEffect, useState } from 'react';
import pulseBackendEndpoints, {
   trackedkpisData,
   trackedPrevkpisData,
} from '../../../api/service/pulse';
import { UserDetails, TrackedCampaign } from '../components/interface';
import { Keys, LocalStorageService } from '../../../utils/local-storage';

import TooltipIcon from '../../../components/info-icon-content/tooltip-message';
import { content } from '../../../components/info-icon-content/info-content';
import SkeletonLoaderKpi from '../../../components/skeletonloader/skeleton-loader-kpi';
import OverviewAndTrackedTabs from './overview-tracked-tabs';
import introJs from 'intro.js';
import { TooltipPosition } from 'intro.js/src/packages/tooltip';

import { componentNames, setFlag } from '../../../store/reducer/tour-reducer';
import {
   getDateRange,
   getDropdownChannelOptions,
   StatusTypes,
} from '../utils/helper';
import Dropdown from '../../../components/customDropdown/choose-dropdown';
import { setChannel } from '../../../store/reducer/overview-dropdown-reducer';
import GoogleAdsTrackedCard from '../components/goolge-ads-tracked-card';
import { resetDateRange } from '../../../store/reducer/kpi-reducer';

const Tracked: React.FC = () => {
   const { connectionDetails } = useAppSelector((state) => state.media);
   const { channel } = useAppSelector((state) => state.dropdown);
   const { dateRange, prevRange, groupBy } = useAppSelector(
      (state) => state.kpi,
   );

   const [data, setData] = useState<TrackedCampaign[] | trackedkpisData[]>([]);
   const [prevData, setPrevData] = useState<
      TrackedCampaign[] | trackedPrevkpisData[]
   >([]);
   const [loader, setLoader] = useState<boolean>(false);
   const [searchcampaign, setSearchCampaign] = useState('');

   const userDetails =
      LocalStorageService.getItem<UserDetails>(Keys.FlableUserDetails) ||
      ({} as UserDetails);

   const { start_date, end_date, prev_start_date, prev_end_date } =
      getDateRange(dateRange, prevRange);

   const dropdownChannelOptions = getDropdownChannelOptions(connectionDetails);

   const handleUntrack = () => {
      void fetchTrackedCards(dropdownChannelOptions[0].value);
   };

   const intro = introJs.tour();
   const { tracked } = useAppSelector((state) => state.tour);
   const dispatch = useAppDispatch();

   const fetchTrackedCards = async (channel: string) => {
      setLoader(true);
      try {
         if (channel === 'meta_ads') {
            const res = await pulseBackendEndpoints.fetchTrackedKpis({
               client_id: userDetails.client_id,
               channel: channel,
               start_date: start_date,
               end_date: end_date,
               prev_start_date: prev_start_date,
               prev_end_date: prev_end_date,
               groupBy: groupBy,
            });

            if (res?.data) {
               setData(
                  res?.data?.currentPeriod?.sort((a, b) => {
                     const statusOrder = {
                        [StatusTypes.ACTIVE]: 0,
                        [StatusTypes.PAUSED]: 1,
                     };

                     return (
                        statusOrder?.[a?.campaign_status || ''] -
                        statusOrder?.[b?.campaign_status || '']
                     );
                  }),
               );
               setPrevData(res?.data?.prevPeriod);
            }
         } else {
            const res = await pulseBackendEndpoints.fetchGoogleTrackedKpis({
               client_id: userDetails.client_id,
               channel: channel,
               start_date: start_date,
               end_date: end_date,
               groupBy: groupBy,
            });
            const res1 = await pulseBackendEndpoints.fetchGoogleTrackedPrevKpis(
               {
                  client_id: userDetails.client_id,
                  channel: channel,
                  groupBy: 'day',
                  start_date: start_date,
                  end_date: end_date,
                  prev_start_date: prev_start_date,
                  prev_end_date: prev_end_date,
               },
            );
            if (res.data && res1.data) {
               setData(
                  res.data?.[0]?.fn_get_googleads_campaign_kpis.sort((a, b) => {
                     const statusOrder = {
                        [StatusTypes.ENABLED]: 0,
                        [StatusTypes.PAUSED]: 1,
                        [StatusTypes.REMOVED]: 2,
                     };

                     return (
                        statusOrder[a.campaign_status] -
                        statusOrder[b.campaign_status]
                     );
                  }),
               );
               setPrevData(res1.data?.[0]?.fn_get_googleads_campaign_kpis);
            }
         }
      } catch (error) {
         console.error('Error fetching tracked cards:', error);
      } finally {
         setLoader(false);
      }
   };

   const steps: {
      element: string;
      intro: string;
      position: TooltipPosition;
      available: boolean;
   }[] = [
      {
         element: '#performanceMeta',
         intro: 'Choose platform here to view the performance insight.',
         position: 'top',
         available: true,
      },
      {
         element: '#dateId',
         intro: 'This dropdown allows you to easily select the time range that matters most to your analysis.',
         position: 'top',
         available: true,
      },
      {
         element: '#rangeId',
         intro: 'Choose aggregation period for the data analysis.',
         position: 'top',
         available: true,
      },
      {
         element: '#viewDetailsId',
         intro: 'Click on View Details to explore in-depth insights on campaign level, ad set level, and ad level.',
         position: 'top',
         available: data?.length > 0,
      },
      {
         element: '#untrackBtnId',
         intro: 'Untrack the KPIs that you no longer wish to closely track or monitor.',
         position: 'top',
         available: data?.length > 0,
      },
   ];

   const startTour = () => {
      const availableSteps = steps.filter((step) => step.available); // Filter steps that have available data

      if (availableSteps.length > 0) {
         intro.setOptions({ steps: availableSteps });
         void intro.start();
         dispatch(
            setFlag({ componentName: componentNames.TRACKED, flag: false }),
         );
      } else {
         console.log('No data available to start the tour.');
      }
   };

   const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchCampaign(e.target.value);
   };

   const filterMetaCampaignByName = (
      campaigns: TrackedCampaign[] | trackedkpisData[],
   ) => {
      return campaigns.filter((campaign) =>
         campaign.campaign_name
            .toLowerCase()
            .includes(searchcampaign.toLowerCase()),
      );
   };

   const handleChannelSelect = (value: string) => {
      dispatch(setChannel(value));
      setData([]);
      setPrevData([]);
   };

   useEffect(() => {
      const isDataAvailable =
         DateRangeSelect?.length > 0 || TrackedCard?.length > 0;

      if (tracked && isDataAvailable) {
         console.log('Data available. Starting tour...');
         startTour();
      } else {
         console.log('Data unavailable. Tour will not start.');
      }
   }, [tracked, DateRangeSelect, data]);

   useEffect(() => {
      void fetchTrackedCards(channel);
   }, [dateRange, groupBy, channel]);

   useEffect(() => {
      return () => {
         dispatch(resetDateRange());
      };
   }, []);

   return (
      <>
         <Box p='20px 40px'>
            <Box mb={6}>
               <Flex align='center'>
                  <Heading as='h4' size='lg' fontWeight='500'>
                     Performance Insights
                  </Heading>
                  <TooltipIcon
                     label={content.performance}
                     placement='top'
                     iconColor='blue.500'
                     ml={2}
                     mt={2}
                  />
               </Flex>
            </Box>
            <Flex justifyContent={'space-between'}>
               <OverviewAndTrackedTabs type='perf' />
            </Flex>
            <Flex width='100%' margin='20px 0' justifyContent='space-between'>
               <Flex gap={3}>
                  {dropdownChannelOptions && (
                     <Dropdown
                        id='performanceMeta'
                        options={dropdownChannelOptions}
                        onSelect={handleChannelSelect}
                        initialValue={channel}
                        width={114}
                     />
                  )}
                  <Input
                     name='search_campaign'
                     width='270px'
                     placeholder='Search Campaign Name'
                     fontSize='14px'
                     value={searchcampaign}
                     onChange={handleInputChange}
                  />
               </Flex>
               <DateRangeSelect dateId='dateId' rangeId='rangeId' pulse />
            </Flex>
            {loader && <SkeletonLoaderKpi spacing={10} length={9} />}
            {!loader && data.length == 0 && (
               <Text width={'100%'} ml={'20%'} mt={'20%'}>
                  No insights are being tracked. Please Track them from Overview
                  tab to see them here.{' '}
               </Text>
            )}
            <Grid templateColumns='repeat(auto-fill, 450px)' gap={6}>
               {!loader &&
                  data.length > 0 &&
                  filterMetaCampaignByName(data).map((card, index) => {
                     return (
                        <Box key={index} justifySelf='start'>
                           {channel === 'meta_ads' ? (
                              <TrackedCard
                                 viewdetailsId='viewDetailsId'
                                 untrackBtnId='untrackBtnId'
                                 key={card.campaign_id}
                                 chart_data={card as TrackedCampaign}
                                 handleUntrack={handleUntrack}
                                 range_data={prevData as TrackedCampaign[]}
                              />
                           ) : (
                              <GoogleAdsTrackedCard
                                 viewdetailsId='viewDetailsId'
                                 untrackBtnId='untrackBtnId'
                                 key={card.campaign_id}
                                 channel={channel}
                                 currentCard={card as trackedkpisData}
                                 prevCardsData={
                                    prevData as trackedPrevkpisData[]
                                 }
                                 handleUntrack={handleUntrack}
                              />
                           )}
                        </Box>
                     );
                  })}
            </Grid>
         </Box>
      </>
   );
};

export default Tracked;
