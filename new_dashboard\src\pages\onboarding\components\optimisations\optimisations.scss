@use '../../../../sass/variable.scss';
.widget-container {
   display: flex;
   flex-direction: column;
   gap: 20px;
   padding: 10px;
   // [data-theme='dark'] & {
   //    color: $background_surface;
   // }
   .social-media {
      display: flex;
      flex-direction: column;
      gap: 20px;
      .title {
         display: flex;
         align-items: center;
         gap: 10px;
         h5 {
            color: #242424;
            font-family: 'Poppins', sans-serif;
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: 28px;
            margin-left: 25px;
         }
      }
      .info {
         display: flex;
         align-items: center;
         gap: 3px;
         background-color: rgb(250, 240, 240);
         color: red;
         font-size: 10px;
         border-radius: 2px;
         padding: 2px 5px;
         font-weight: 700;
      }
      .social-media__components {
         display: flex;
         gap: 20px;
         margin-bottom: 20px;
         margin-left: 20px;
      }
   }
}
.optimisationText {
   [data-theme='dark'] & {
      color: #fff;
   }
}
.send-snippet {
   [data-theme='dark'] & {
      color: #fff;
   }
}
// .snippet {
//    [data-theme='dark'] & {
//       color: #fff;
//       background-color: $controls;
//    }
// }
.panel {
   [data-theme='dark'] & {
      color: #fff;
   }
}
// .SendSnippepBtn {
//    [data-theme='dark'] & {
//       color: #fff;
//       background-color: $controls;
//    }
// }
// .SendSnippepBtn:hover {
//    [data-theme='dark'] & {
//       color: #fff;
//       background-color: $controls_hover;
//    }
// }
