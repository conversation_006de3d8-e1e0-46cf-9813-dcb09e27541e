import React, { useState } from 'react';
import Chart from 'react-apexcharts';
import './chart-component.scss';
import { ApexOptions } from 'apexcharts';
import Dropdown from '../../../components/customDropdown/choose-dropdown';

type ChartType = 'line' | 'bar';
type ViewType = 'graph' | 'table';

interface ChartComponentProps {
   title: string;
   data: {
      categories: string[];
      series: Array<{ name: string; data: number[] }>;
      tableData: Array<{ date: string; columnA: number; columnB: number }>;
   };
}

const ChartComponent: React.FC<ChartComponentProps> = ({ title, data }) => {
   const [chartType, setChartType] = useState<ChartType>('bar');
   const [viewType, setViewType] = useState<ViewType>('graph');

   const handleChartTypeChange = (value: string) => {
      setChartType(value as ChartType);
   };

   const handleViewTypeChange = (view: ViewType) => {
      setViewType(view);
   };

   // Define chart options
   const chartOptions = {
      chart: {
         type: chartType,
         height: 350,
      },
      xaxis: {
         categories: data.categories,
      },
      stroke: {
         curve: chartType === 'line' ? 'smooth' : 'straight',
      },
      markers: {
         size: chartType === 'line' ? 5 : 0,
      },
   };

   const dropdownOptions = [
      { value: 'bar', label: 'Bar' },
      { value: 'line', label: 'line' },
   ];

   return (
      <div className='chart-container'>
         <div className='chart-header'>
            <h3>{title}</h3>
            <div className='tab-options'>
               <span
                  className={viewType === 'graph' ? 'active' : ''}
                  onClick={() => handleViewTypeChange('graph')}
               >
                  Graph
               </span>
               <span
                  className={viewType === 'table' ? 'active' : ''}
                  onClick={() => handleViewTypeChange('table')}
               >
                  Table
               </span>
            </div>
            {viewType === 'graph' && (
               //  <select
               //     className='chart-dropdown'
               //     onChange={handleChartTypeChange}
               //     value={chartType}
               //  >
               //     <option value='bar'>Bar Chart</option>
               //     <option value='line'>Line Chart</option>
               //  </select>
               <Dropdown
                  options={dropdownOptions}
                  initialValue={chartType}
                  onSelect={handleChartTypeChange}
               />
            )}
         </div>

         {viewType === 'graph' ? (
            <Chart
               options={chartOptions as ApexOptions}
               series={data.series}
               type={chartType}
               height={350}
            />
         ) : (
            <table className='data-table'>
               <thead>
                  <tr>
                     <th>Date</th>
                     <th>Column A</th>
                     <th>Column B</th>
                  </tr>
               </thead>
               <tbody>
                  {data.tableData.map((row, index) => (
                     <tr key={index}>
                        <td>{row.date}</td>
                        <td>{row.columnA}</td>
                        <td>{row.columnB}</td>
                     </tr>
                  ))}
               </tbody>
            </table>
         )}
      </div>
   );
};

export default ChartComponent;
