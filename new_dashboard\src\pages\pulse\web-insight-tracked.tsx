import TooltipIcon from '../../components/info-icon-content/tooltip-message';
import OverviewAndTrackedTabs from './performance-insights/overview-tracked-tabs';
import Dropdown from '../../components/customDropdown/choose-dropdown';

import { Flex, Heading, Select } from '@chakra-ui/react';
import { content } from '../../components/info-icon-content/info-content';
import { useState } from 'react';
import { dropdownOptions } from '.';
import { GROUP_BY_RANGE } from '../dashboard/utils/default-variables';

import './web-insight-tracked.scss';

function WebInsightTracked() {
   const [selectedDays, setSelectedDays] = useState<string>('7');
   const [groupBy, setGroupBy] = useState('day');

   const handleDaysSelect = (value: string) => {
      setSelectedDays(value);
   };

   const handleGroupBy = (e: React.ChangeEvent<HTMLSelectElement>) => {
      setGroupBy(e.target.value);
   };

   // const dummyData1 = {
   //    categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
   //    series: [
   //       { name: 'Bar A', data: [30, 40, 35, 50, 49, 60, 70] },
   //       { name: 'Bar B', data: [20, 30, 40, 30, 20, 40, 50] },
   //    ],
   // };

   // const dummyData2 = {
   //    categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
   //    series: [
   //       { name: 'Line A', data: [10, 20, 30, 40, 30, 20, 10] },
   //       { name: 'Line B', data: [50, 60, 70, 80, 70, 60, 50] },
   //    ],
   // };

   // const dummyData = {
   //    categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
   //    series: [
   //       { name: 'Bar A', data: [30, 40, 35, 50, 49, 60, 70] },
   //       { name: 'Bar B', data: [20, 30, 40, 30, 20, 40, 50] },
   //    ],
   //    tableData: [
   //       { date: '2024-09-24', columnA: 332, columnB: 2343 },
   //       { date: '2024-09-25', columnA: 423, columnB: 24321 },
   //       { date: '2024-09-26', columnA: 453, columnB: 843 },
   //    ],
   // };
   return (
      <div className='WebInsightTracked Pulse'>
         <div
            style={{
               display: 'flex',
               alignItems: 'center',
               justifyContent: 'space-between',
            }}
         >
            <Flex alignItems='center'>
               <Heading as='h4' size='bold' className='Main' fontWeight='600'>
                  Web Analytics Insights
               </Heading>
               <TooltipIcon
                  label={content.web_insight}
                  placement='top'
                  iconColor='blue.500'
                  ml={2}
                  mt={7}
               />
            </Flex>
            <Flex alignItems='center' justifyContent='space-between' gap='5'>
               <Dropdown
                  id='webInsightDropdown'
                  options={dropdownOptions}
                  onSelect={handleDaysSelect}
                  initialValue={selectedDays}
               />

               <Select
                  onChange={handleGroupBy}
                  placeholder='Group by'
                  height={'32px'}
                  fontSize={'13px'}
                  value={groupBy}
               >
                  {Object.entries(GROUP_BY_RANGE).map(([key, label]) => (
                     <option key={key} value={key}>
                        {label}
                     </option>
                  ))}
               </Select>
            </Flex>
         </div>
         <OverviewAndTrackedTabs type='web' />

         <h3>Coming Soon..</h3>

         {/* <div className='tracked-sections'>
            <ChartComponent title='Aggregated Traffic' data={dummyData} />
            <ChartComponent title='Page Engagement' data={dummyData} />
            <ChartComponent title='Page Engagement' data={dummyData} />
            <ChartComponent title='Page Engagement' data={dummyData} />
            <ChartComponent title='Page Engagement' data={dummyData} />
         </div> */}
      </div>
   );
}

export default WebInsightTracked;
