import React from 'react';
import { Fc<PERSON>ike } from 'react-icons/fc';
import { RiChat3Line } from 'react-icons/ri';
import { BsSend } from 'react-icons/bs';
import { FaRegBookmark } from 'react-icons/fa6';
import { MdMoreVert } from 'react-icons/md';
import { LocalStorageService, Keys } from '@/utils/local-storage';
import { AuthUser } from '@/types/auth';
import { FaUserCircle } from 'react-icons/fa';

interface CreativePreviewProps {
   imageUrl?: string;
   username?: string;
   avatarUrl?: string;
   description?: string;
}

//const defaultAvatar = 'https://randomuser.me/api/portraits/men/32.jpg';
const defaultImage =
   'https://images.unsplash.com/photo-1512436991641-6745cdb1723f?auto=format&fit=crop&w=600&q=80';
const userDetails = LocalStorageService.getItem<AuthUser>(
   Keys.FlableUserDetails,
);
const defaultUsername = userDetails?.company_name;
//const defaultDescription = `Uncover a large selection of T-Shirts for all! Find trendy and snug options for the whole //squad. 💯 Quality assured. Shop now and receive rapid shipping and top-notch customer service! ✨`;

export const CreativePreview: React.FC<CreativePreviewProps> = ({
   imageUrl = defaultImage,
   username = defaultUsername,
   //avatarUrl = defaultAvatar,
   description,
}) => {
   return (
      <div
         style={{
            maxWidth: 370,
            borderRadius: 18,
            background: '#fff',
            boxShadow: '0 2px 12px rgba(0,0,0,0.07)',
            overflow: 'hidden',
            border: '1px solid #eee',
            margin: '0 auto',
            fontFamily: 'Inter, Arial, sans-serif',
         }}
      >
         {/* Header */}
         <div
            style={{
               display: 'flex',
               alignItems: 'center',
               padding: '18px 16px 10px 16px',
               background: '#fff',
            }}
         >
            <FaUserCircle size={28} color='gray-400' />
            <div
               style={{
                  fontWeight: 600,
                  fontSize: 16,
                  color: '#232323',
                  flex: 1,
                  paddingLeft: 12,
               }}
            >
               {username}
            </div>
            <MdMoreVert size={20} />
         </div>
         {/* Image */}
         <div
            style={{
               background: '#f6f6f6',
               width: '100%',
               height: 320,
               display: 'flex',
               alignItems: 'center',
               justifyContent: 'center',
            }}
         >
            <img
               src={imageUrl}
               alt='creative'
               style={{ width: '100%', height: '100%', objectFit: 'cover' }}
            />
         </div>
         {/* Actions */}
         <div
            style={{
               display: 'flex',
               alignItems: 'center',
               padding: '12px 16px 0 16px',
               gap: 16,
            }}
         >
            <FcLike size={24} />
            <RiChat3Line size={24} />
            <BsSend size={20} />
            <div style={{ flex: 1 }} />
            <FaRegBookmark size={20} />
         </div>
         {/* Likes */}
         <div
            style={{
               padding: '8px 16px 0 16px',
               fontWeight: 600,
               fontSize: 15,
               color: '#232323',
            }}
         >
            12,542 Likes
         </div>
         {/* Description */}
         <div
            style={{
               padding: '6px 16px 0 16px',
               fontSize: 14,
               color: '#232323',
               lineHeight: 1.5,
            }}
         >
            {description || 'No description provided.'}
         </div>
         {/* CTA Button */}
         <div style={{ padding: '16px 16px 0 16px' }}>
            <button
               style={{
                  width: '100%',
                  background: '#EAF3FF',
                  color: '#2563EB',
                  border: 'none',
                  borderRadius: 10,
                  padding: '12px 0',
                  fontWeight: 600,
                  fontSize: 16,
                  cursor: 'pointer',
                  marginBottom: 8,
               }}
            >
               Shop Now
            </button>
         </div>
         {/* Comments link and time */}
         <div
            style={{ padding: '0 16px 16px 16px', fontSize: 13, color: '#888' }}
         >
            View all 1000 comments
            <br />
            <span style={{ fontSize: 12 }}>1 hour ago</span>
         </div>
      </div>
   );
};

export default CreativePreview;
