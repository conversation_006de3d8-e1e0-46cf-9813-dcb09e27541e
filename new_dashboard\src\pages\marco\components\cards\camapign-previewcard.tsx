import { <PERSON>, Card, Flex, Stack, Text, Grid } from '@chakra-ui/react';
import { MdCampaign } from 'react-icons/md';
import {
   FaChartLine,
   FaLightbulb,
   FaShoppingCart,
   FaTags,
   FaCogs,
   FaWallet,
} from 'react-icons/fa';
import { CampaignDetails } from '../../../../api/service/agentic-workflow/meta-ads-manager';
import { CAMPAIGN_STRINGS } from '../../utils/meta-ads-manager-agent/constants';
type InfoRowProps = {
   icon: React.ReactNode;
   label: string;
   value: string | boolean | number;
   iconBg: string;
   labelColor: string;
};
const InfoRow = ({ icon, label, value, iconBg, labelColor }: InfoRowProps) => (
   <Box>
      <Flex align='center' mb={2}>
         <Box bg={iconBg} p={2} borderRadius='md' mr={3}>
            {icon}
         </Box>
         <Text size='sm' color={labelColor}>
            {label}
         </Text>
      </Flex>
      <Text ml={12} color='gray.700'>
         {value}
      </Text>
   </Box>
);
const formatLabel = (label: string) => {
   return label.replace(/_/g, ' ').replace(/\b\w/g, (c) => c.toUpperCase());
};
export const CampaignPreviewCard = ({
   campaignDetails,
}: {
   campaignDetails: CampaignDetails;
}) => {
   return (
      <Card p={7} bg='gray.50'>
         <Grid templateColumns={['1fr', null, '1fr 1fr']} gap={6}>
            <Stack spacing={6}>
               <InfoRow
                  icon={<MdCampaign color='#3182CE' size={20} />}
                  label={CAMPAIGN_STRINGS.camp_name}
                  value={campaignDetails.name}
                  iconBg='blue.50'
                  labelColor='blue.600'
               />
               <InfoRow
                  icon={<FaChartLine color='#38A169' size={20} />}
                  label={CAMPAIGN_STRINGS.camp_objective}
                  value={
                     formatLabel(campaignDetails.objective) === 'LINK CLICKS'
                        ? 'Traffic'
                        : formatLabel(campaignDetails.objective) ===
                            'OUTCOME SALES'
                          ? 'Sales'
                          : formatLabel(campaignDetails.objective) ===
                              'OUTCOME LEADS'
                            ? 'Leads'
                            : formatLabel(campaignDetails.objective) ===
                                'OUTCOME AWARENESS'
                              ? 'Awareness'
                              : formatLabel(campaignDetails.objective)
                  }
                  iconBg='green.50'
                  labelColor='green.600'
               />

               <InfoRow
                  icon={<FaLightbulb color='#805AD5' size={20} />}
                  label={CAMPAIGN_STRINGS.camp_id}
                  value={campaignDetails.campaign_id}
                  iconBg='purple.50'
                  labelColor='purple.600'
               />
               <InfoRow
                  icon={<FaShoppingCart color='#DD6B20' size={20} />}
                  label={CAMPAIGN_STRINGS.prod_url}
                  value={campaignDetails.product_url}
                  iconBg='orange.50'
                  labelColor='orange.600'
               />
            </Stack>

            <Stack spacing={6}>
               <InfoRow
                  icon={<FaTags color='#319795' size={20} />}
                  label='Buying Type'
                  value={campaignDetails.buying_type}
                  iconBg='teal.50'
                  labelColor='teal.600'
               />
               <InfoRow
                  icon={<FaCogs color='#718096' size={20} />}
                  label=' Campaign Budget Optimization(CBO)'
                  value={
                     campaignDetails.campaign_budget_optimization
                        ? 'Enabled ✅'
                        : 'Disabled'
                  }
                  iconBg='gray.100'
                  labelColor='gray.600'
               />
               <InfoRow
                  icon={<FaWallet color='#D69E2E' size={20} />}
                  label='Daily Budget'
                  value={`₹ ${campaignDetails.daily_budget}`}
                  iconBg='yellow.50'
                  labelColor='yellow.600'
               />
            </Stack>
         </Grid>
      </Card>
   );
};
