import COGS from '../../../assets/icons/cost-setting/cogs.png';
import Shipping from '../../../assets/icons/cost-setting/shipping.png';
import PaymentGateway from '../../../assets/icons/cost-setting/paymentgate.png';
import Customexpenses from '../../../assets/icons/cost-setting/customexpenses.png';
import { CgWebsite } from 'react-icons/cg';
const CS_IMAGES: {
   [key: string]: string;
} = {
   cogs: COGS,
   shipping: Shipping,
   paymentgate: PaymentGateway,
   customexpenses: Customexpenses,
};

function CSImage(props: { csCat: string }) {
   const { csCat } = props;
   if (CS_IMAGES[csCat]) {
      return (
         <img src={CS_IMAGES[csCat]} width='30px' height='30px' alt={csCat} />
      );
   }
   return <CgWebsite />;
}

export default CSImage;
