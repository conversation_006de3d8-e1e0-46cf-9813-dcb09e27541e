import {
   Ta<PERSON>,
   <PERSON><PERSON><PERSON><PERSON>,
   Tab,
   TabPanel,
   TabPanels,
   Text,
   Box,
   Divider,
   TabIndicator,
   Input,
   IconButton,
   Flex,
   Button,
   Alert,
   Skeleton,
   Stack,
   useToast,
   AlertDialog,
   AlertDialogBody,
   AlertDialogFooter,
   AlertDialogHeader,
   AlertDialogContent,
   AlertDialogOverlay,
   useDisclosure,
   useColorModeValue,
} from '@chakra-ui/react';

import { DeleteIcon } from '@chakra-ui/icons';
import { useState, useRef } from 'react';
import { useApiQuery, useApiMutation } from '../../hooks/react-query-hooks';
import { Keys, LocalStorageService } from '../../utils/local-storage';
import { SettingsQueryKeys } from '../dashboard/utils/query-keys';
import settingsService from '../../api/service/settings/index';
import { settingsCompetitors } from '../../utils/strings/settings-strings';
import TooltipIcon from '../../components/info-icon-content/tooltip-message';

const COMPETITOR_CHANNEL = 'instagram';
const MAX_COMPETITORS = 5;
type VERIFY_STATES = 'idle' | 'success' | 'failed';

function Settings() {
   const [socialHandle, setSocialHandle] = useState('');
   const [status, setStatus] = useState<VERIFY_STATES>('idle');
   const [isLimitExceeded, setIsLimitExceeded] = useState(false);
   const toast = useToast();
   const { isOpen, onOpen, onClose } = useDisclosure();
   const cancelRef = useRef<HTMLButtonElement>(null);

   const { data, isFetching, refetch } = useApiQuery({
      queryKey: [SettingsQueryKeys.competitors],
      queryFn: () =>
         settingsService.fetchCompetitors({
            client_id: LocalStorageService.getItem(Keys.ClientId) as string,
            channel: COMPETITOR_CHANNEL,
         }),
      enabled: true,
   });

   const verifyService = useApiMutation({
      mutationFn: settingsService.verifySocial,
      onSuccessHandler(data) {
         setStatus(data.verified ? 'success' : 'failed');
      },
      onError() {
         setStatus('failed');
      },
   });

   const upsertService = useApiMutation({
      mutationFn: settingsService.updateCompetitors,
      onSuccessHandler() {
         void refetch();
      },
      onError(msg) {
         toast({
            title: 'Error',
            description: msg,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      },
   });

   const handleDelete = (item: string) => {
      const updatedItems = [...(data || [])];
      const index = updatedItems.findIndex((obj) => obj.handle === item);
      updatedItems.splice(index, 1);
      upsertService.mutate({
         client_id: LocalStorageService.getItem(Keys.ClientId) as string,
         channel: COMPETITOR_CHANNEL,
         competitor_handles: updatedItems,
      });
   };

   // const handleVerify = () => {
   //    setStatus('idle');
   //    verifyService.mutate({
   //       handle: encodeURI(`https://www.instagram.com/${socialHandle}`),
   //    });
   // };
   const handleAdd = () => {
      if (data && data.length >= MAX_COMPETITORS) {
         setIsLimitExceeded(true);
         return;
      }
      onOpen();
   };

   // const handleConfirmAdd = () => {
   //    onClose();
   //    setSocialHandle('');
   //    setStatus('idle');
   //    const updatedItems = [...(data || [])];
   //    updatedItems.push({
   //       handle:
   //          status === 'failed' ? `${socialHandle} (unverified)` : socialHandle,
   //    });
   //    upsertService.mutate({
   //       client_id: LocalStorageService.getItem(Keys.ClientId) as string,
   //       channel: COMPETITOR_CHANNEL,
   //       competitor_handles: updatedItems,
   //    });

   // };
   const handleConfirmAdd = () => {
      onClose();
      const updatedItems = [...(data || [])];
      updatedItems.push({
         handle: socialHandle,
      });
      upsertService.mutate({
         client_id: LocalStorageService.getItem(Keys.ClientId) as string,
         channel: COMPETITOR_CHANNEL,
         competitor_handles: updatedItems,
      });
      setSocialHandle('');
   };

   const isVerified = status === 'success';
   // const isIdle = status === 'idle';

   const getColor = () => {
      if (isVerified) {
         return 'green.600';
      } else if (status === 'failed') {
         return 'red.600';
      } else {
         return '';
      }
   };

   return (
      <Box p='2em' width='100%'>
         <Flex align='center' gap='0.5em'>
            <Text as='h5' fontSize='larger' fontWeight='500'>
               {settingsCompetitors.title}
            </Text>
            <TooltipIcon label='Adding or removing users into social creative takes minimum 24 hours' />
         </Flex>
         <Divider my='1em' borderWidth='1px' />
         <Tabs variant='unstyled'>
            <TabList>
               <Tab px={0}>Instagram</Tab>
            </TabList>
            <TabIndicator height='1px' width='auto' bg='blue.500' />
            <TabPanels>
               <TabPanel p={0} py='1.2em' width='60%'>
                  <Text fontSize='large'>{settingsCompetitors.addNew}</Text>
                  <Text mt='1em' fontSize='small'>
                     {settingsCompetitors.inputTitle}
                  </Text>

                  <Flex mt='0.5em' alignItems='center' gap='1em'>
                     <Box>
                        <Input
                           size='lg'
                           width='100%'
                           borderColor={getColor()}
                           value={socialHandle}
                           onChange={(e) => {
                              setStatus('idle');
                              setSocialHandle(e.target.value);
                           }}
                        />
                     </Box>
                     <Button
                        colorScheme='blue'
                        backgroundColor={useColorModeValue(
                           '',
                           'var(--controls)',
                        )}
                        color={useColorModeValue('black', 'white')}
                        isDisabled={socialHandle.length === 0}
                        isLoading={verifyService.isPending}
                        onClick={handleAdd}
                     >
                        Add
                     </Button>
                  </Flex>
                  {status !== 'idle' && (
                     <Text fontSize='smaller' color={getColor()}>
                        {isVerified ? 'Verified' : 'Could not verify'}
                     </Text>
                  )}
                  <Text mt='2em' mb='1em' fontSize='large'>
                     {settingsCompetitors.added}
                  </Text>
                  {isFetching || upsertService.isPending ? (
                     <Stack>
                        <Skeleton height='20px' />
                        <Skeleton height='20px' />
                        <Skeleton height='20px' />
                     </Stack>
                  ) : (
                     <Flex direction='column'>
                        {!isFetching && Boolean(data) && !data?.length && (
                           <Alert size='small' status='warning'>
                              {settingsCompetitors.na}
                           </Alert>
                        )}
                        {(data || []).map((item) => (
                           <Flex
                              key={item.handle}
                              alignItems='center'
                              justifyContent='space-between'
                           >
                              <Text>{item.handle}</Text>
                              <IconButton
                                 bg={useColorModeValue(
                                    'white',
                                    'var(--background)',
                                 )}
                                 aria-label='remove competitor'
                                 icon={<DeleteIcon />}
                                 onClick={() => handleDelete(item.handle)}
                              />
                           </Flex>
                        ))}
                     </Flex>
                  )}
               </TabPanel>
            </TabPanels>
         </Tabs>

         <AlertDialog
            isOpen={isOpen}
            leastDestructiveRef={cancelRef}
            onClose={onClose}
         >
            <AlertDialogOverlay>
               <AlertDialogContent>
                  <AlertDialogHeader fontSize='lg' fontWeight='bold'>
                     Do you want to add this account name?
                  </AlertDialogHeader>
                  <AlertDialogBody>
                     Please verify the account name or ID manually before
                     proceeding ahead.Changes will be reflected after 24 hours.
                  </AlertDialogBody>
                  <AlertDialogFooter>
                     <Button ref={cancelRef} onClick={onClose}>
                        No
                     </Button>
                     <Button
                        colorScheme='blue'
                        onClick={handleConfirmAdd}
                        ml={3}
                     >
                        Yes
                     </Button>
                  </AlertDialogFooter>
               </AlertDialogContent>
            </AlertDialogOverlay>
         </AlertDialog>
         <AlertDialog
            isOpen={isLimitExceeded}
            leastDestructiveRef={cancelRef}
            onClose={() => setIsLimitExceeded(false)}
         >
            <AlertDialogOverlay>
               <AlertDialogContent>
                  <AlertDialogHeader fontSize='lg' fontWeight='bold'>
                     Limit Exceeded
                  </AlertDialogHeader>
                  <AlertDialogBody>
                     You have exceeded the maximum limit of {MAX_COMPETITORS}{' '}
                     competitors. Please remove an existing competitor before
                     adding a new one.
                  </AlertDialogBody>
                  <AlertDialogFooter>
                     <Button
                        ref={cancelRef}
                        onClick={() => setIsLimitExceeded(false)}
                     >
                        Close
                     </Button>
                  </AlertDialogFooter>
               </AlertDialogContent>
            </AlertDialogOverlay>
         </AlertDialog>
      </Box>
   );
}

export default Settings;
