/* eslint-env node */
module.exports = {
   extends: [
      'eslint:recommended',
      'plugin:@typescript-eslint/recommended-type-checked',
      'prettier',
   ],
   parser: '@typescript-eslint/parser',
   parserOptions: {
      tsConfigRootDir: __dirname,
      project: true,
   },

   ignorePatterns: ['dist/', 'vite.config.ts'],

   plugins: ['@typescript-eslint', 'prettier'],
   root: true,
   rules: {
      // 'no-console': 'warn',
      '@typescript-eslint/no-unused-vars': 'error',
      '@typescript-eslint/no-empty-function': 'error',
   },
};
