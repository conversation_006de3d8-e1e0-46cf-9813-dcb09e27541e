import { GoChevronRight } from 'react-icons/go';

type Breadcrumb = {
   label: string;
   onClick?: () => void;
};

type PageHeaderProps = {
   title: string;
   breadcrumbs?: Breadcrumb[];
};

const PageHeader = ({ title, breadcrumbs = [] }: PageHeaderProps) => {
   return (
      <div className='headSection flex items-center justify-between'>
         <h1 className='title head3 font-medium'>{title}</h1>

         {breadcrumbs.length > 0 && (
            <div className='subtitle-head flex items-center justify-between gap-2'>
               {breadcrumbs.map((crumb, index) => (
                  <div key={index} className='flex items-center gap-2'>
                     <p
                        className='subtitle cursor-pointer'
                        onClick={crumb.onClick}
                     >
                        {crumb.label}
                     </p>
                     {index < breadcrumbs.length - 1 && <GoChevronRight />}
                  </div>
               ))}
            </div>
         )}
      </div>
   );
};

export default PageHeader;
