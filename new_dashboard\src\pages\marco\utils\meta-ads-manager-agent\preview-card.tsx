import React from 'react';
import { getUsername } from '../../utils/meta-ads-manager-agent/get-username';
import { useAppDispatch, useAppSelector } from '../../../../store/store';
import { BsThreeDots } from 'react-icons/bs';
import { truncateText } from '../../../pulse/utils/helper';
import { FaUserCircle } from 'react-icons/fa';

import {
   Flex,
   IconButton,
   Image,
   Input,
   Stack,
   Text,
   Card,
   CardBody,
   Button,
   Box,
   Textarea,
} from '@chakra-ui/react';

import { updateAdCreativeCardState } from '../../../../store/reducer/meta-ads-manager-reducer';

export const PreviewCard = React.memo(
   ({
      name,
      caption,
      picture,
      description,
      call_to_action,
      isEditing,
      editedMessage,

      handleImageChange,
      handleSave,
      isUploading,
      setIsEditing,
      isExpanded,
      setIsExpanded,
      previewImage,
      uploadedImageUri,
      fileName,
      link,
   }: {
      name: string;
      caption: string;
      picture: string;
      description?: string;
      call_to_action?: {
         type: string;
         value: {
            link: string;
         };
      };
      isEditing: boolean;
      editedMessage: string;
      setEditedMessage: (value: string) => void;
      handleImageChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
      handleSave: () => void;
      isUploading: boolean;
      setIsEditing: (value: boolean) => void;
      isExpanded: boolean;
      setIsExpanded: (value: boolean) => void;
      previewImage: string | null;
      uploadedImageUri: string | null;
      fileName: string;
      link: string;
   }) => {
      const imageSource = previewImage || uploadedImageUri || picture;

      const displayMessage = editedMessage || caption;

      const username = getUsername();

      const dispatch = useAppDispatch();
      const { adCreativeCardState } = useAppSelector(
         (state) => state.metaAdsManager,
      );

      const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
         dispatch(updateAdCreativeCardState({ editedMessage: e.target.value }));
      };

      return (
         <Card maxW='md' mt={4} boxShadow='lg' borderRadius='lg' width='500px'>
            <CardBody p={0}>
               <Stack spacing='4'>
                  {isEditing ? (
                     <Box p={4}>
                        <Textarea
                           value={adCreativeCardState.editedMessage ?? caption}
                           onChange={handleChange}
                           placeholder='Enter your caption'
                           size='sm'
                           minH='100px'
                           autoFocus
                        />
                        <Input
                           type='file'
                           accept='image/*'
                           onChange={handleImageChange}
                           size='md'
                           mt={4}
                        />
                        {fileName && (
                           <Box mt={1} color='gray.600'>
                              Selected File: {fileName}
                           </Box>
                        )}
                        <Button
                           colorScheme='blue'
                           onClick={handleSave}
                           isLoading={isUploading}
                           loadingText='Uploading...'
                           mt={2}
                        >
                           Save Changes
                        </Button>
                        <Button
                           onClick={() => setIsEditing(false)}
                           bg='#ffe5e5'
                           color='#cc0000'
                           border='1px solid #cc0000'
                           _hover={{ bg: '#ffcccc' }}
                           mt={2}
                           ml={6}
                        >
                           Cancel
                        </Button>
                     </Box>
                  ) : (
                     <Stack spacing={0} width='100%'>
                        <Flex
                           p={4}
                           justify='space-between'
                           align='center'
                           borderBottom='1px solid'
                           borderColor='gray.100'
                        >
                           <Flex align='center' gap={3}>
                              <Box
                                 position='relative'
                                 width='40px'
                                 height='40px'
                                 borderRadius='full'
                                 overflow='hidden'
                              >
                                 <FaUserCircle size={40} color='#718096' />
                              </Box>
                              <Stack spacing={0}>
                                 <Text fontWeight='600' fontSize='15px'>
                                    {username}
                                 </Text>
                                 <Flex align='center' gap={1}>
                                    <Text fontSize='13px' color='gray.500'>
                                       Sponsored
                                    </Text>
                                    <Text fontSize='13px' color='gray.500'>
                                       ·
                                    </Text>
                                 </Flex>
                              </Stack>
                           </Flex>
                           <Flex gap={4}>
                              <Button
                                 size='md'
                                 variant='outline'
                                 onClick={() => setIsEditing(true)}
                                 colorScheme='blue'
                                 borderWidth='2px'
                                 fontWeight='600'
                                 _hover={{
                                    bg: 'blue.50',
                                    transform: 'translateY(-1px)',
                                    boxShadow: 'md',
                                 }}
                                 transition='all 0.2s'
                              >
                                 Edit Creative
                              </Button>
                              <IconButton
                                 aria-label='More options'
                                 icon={<BsThreeDots />}
                                 variant='ghost'
                                 size='sm'
                                 sx={{
                                    cursor: 'default',
                                    _hover: {
                                       cursor: 'default',
                                       background: 'transparent',
                                    },
                                 }}
                              />
                           </Flex>
                        </Flex>

                        <Box px={4} py={2}>
                           {displayMessage ? (
                              <Flex direction='column'>
                                 <Text fontSize='15px' color='gray.700'>
                                    {isExpanded
                                       ? displayMessage
                                       : truncateText(
                                            displayMessage,
                                            false,
                                            150,
                                         )}
                                    {displayMessage.length > 150 && (
                                       <Text
                                          as='span'
                                          color='blue.500'
                                          cursor='pointer'
                                          ml={1}
                                          onClick={(e) => {
                                             e.stopPropagation();
                                             setIsExpanded(!isExpanded);
                                          }}
                                          _hover={{
                                             textDecoration: 'underline',
                                          }}
                                       >
                                          {isExpanded
                                             ? 'See less'
                                             : '...See more'}
                                       </Text>
                                    )}
                                 </Text>
                              </Flex>
                           ) : null}
                        </Box>

                        <Box width='100%'>
                           <Image
                              src={imageSource}
                              alt={name}
                              width='100%'
                              height='auto'
                              objectFit='cover'
                              fallback={
                                 <Box
                                    bg='gray.100'
                                    width='100%'
                                    height='300px'
                                 />
                              }
                           />
                        </Box>

                        <Box p={2} borderTop='1px solid' borderColor='gray.100'>
                           <Stack spacing='2' height='100%'>
                              <Text fontSize='15px' color='gray.700' mb={4}>
                                 {description || 'No description available'}
                              </Text>
                              <Box
                                 height='1px'
                                 bg='gray.200'
                                 mb={2}
                                 mt='auto'
                              />
                              <Flex
                                 width='100%'
                                 alignItems='center'
                                 justifyContent='space-between'
                                 cursor='pointer'
                                 py={0.5}
                                 px={1}
                                 borderRadius='md'
                                 mb={1}
                                 transition='all 0.2s ease-in-out'
                                 _hover={{
                                    bg: 'gray.100',
                                    transform: 'translateX(4px)',
                                    boxShadow: 'md',
                                    py: 0.25,
                                 }}
                              >
                                 <Text
                                    fontSize='15px'
                                    fontWeight='500'
                                    color='gray.700'
                                    cursor='pointer'
                                    onClick={() => {
                                       window.open(link);
                                    }}
                                 >
                                    {call_to_action?.type
                                       ?.toLowerCase()
                                       .split('_')
                                       .map(
                                          (word) =>
                                             word.charAt(0).toUpperCase() +
                                             word.slice(1),
                                       )
                                       .join(' ') || 'Sign up'}
                                 </Text>
                                 <Text
                                    fontSize='24px'
                                    transition='transform 0.2s ease-in-out'
                                    _hover={{ transform: 'translateX(4px)' }}
                                 >
                                    ›
                                 </Text>
                              </Flex>
                           </Stack>
                        </Box>
                     </Stack>
                  )}
               </Stack>
            </CardBody>
         </Card>
      );
   },
   (prevProps, nextProps) => {
      return (
         prevProps.caption === nextProps.caption &&
         prevProps.picture === nextProps.picture &&
         prevProps.isEditing === nextProps.isEditing &&
         prevProps.editedMessage === nextProps.editedMessage &&
         prevProps.previewImage === nextProps.previewImage &&
         prevProps.uploadedImageUri === nextProps.uploadedImageUri &&
         prevProps.isExpanded === nextProps.isExpanded
      );
   },
);
