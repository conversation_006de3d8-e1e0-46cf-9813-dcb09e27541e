import PulseTabs from '../components/pulse-tabs';
import { useLocation } from 'react-router-dom';

function OverviewAndTrackedTabs({ type }: { type: 'perf' | 'web' }) {
   const location = useLocation();
   const currentPath = location.pathname;

   function isActive(pathname: string) {
      return currentPath.endsWith(pathname);
   }

   function getTabs() {
      return [
         {
            id: 'overview',
            to: `/pulse/${type === 'perf' ? 'performance-insights' : 'web-insights'}/overview`,
            title: 'Overview',
            className: isActive('overview') ? 'active' : '',
         },
         {
            id: 'tracked',
            to: `/pulse/${type === 'perf' ? 'performance-insights' : 'web-insights'}/tracked`,
            title: 'Tracked',
            className: isActive('tracked') ? 'active' : '',
         },
      ];
   }

   return <PulseTabs tabs={getTabs()} />;
}

export default OverviewAndTrackedTabs;
