import {
   SidebarGroup,
   SidebarMenu,
   SidebarMenuAction,
   SidebarMenuButton,
   SidebarMenuItem,
   SidebarMenuSub,
   SidebarMenuSubItem,
   SidebarMenuSubButton,
} from '@/components/ui/sidebar';
import {
   Collapsible,
   CollapsibleContent,
   CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { AuthUser } from '@/types/auth';
import { useMemo } from 'react';
import { ChevronRight } from 'lucide-react';
import dashboardIcon from '@/assets/icons/dashboard-sidebar-icon.svg';
import marcoIcon from '@/assets/icons/marco-sidebar-icon.svg';
import pulseIcon from '@/assets/icons/pulse-sidebar-icon.svg';
import integrationsIcon from '@/assets/icons/integrations-sidebar-icon.svg';
import performanceInsightIcon from '@/assets/icons/performance-insights-icon.svg';
import webInsightIcon from '@/assets/icons/web-insights-icon.svg';
import chatIcon from '@/assets/icons/chat-sidebar-icon.svg';
import agenticWorkflowIcon from '@/assets/icons/agentic-workflow-sidebar-icon.svg';
import { useLocation, useNavigate } from 'react-router-dom';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import { Badge } from '../ui/badge';

const APP_SIDEBAR_CONTENT = [
   {
      title: 'Marco',
      url: '/marco',
      icon: marcoIcon,
      tag: 'Beta',
      isActive: true,
      matchPrefix: ['/marco'],
      items: [
         {
            title: 'Chats',
            url: '/marco',
            icon: chatIcon,
            matchPrefix: [
               '/marco/alerting-agent',
               '/marco/analytics-agent',
               '/marco/meta-ads-manager-agent',
               '/marco/meta-ads-manager-auto',
            ],
         },
         {
            title: 'Agents',
            url: '/marco/agentic-workflow',
            icon: agenticWorkflowIcon,
            matchPrefix: ['/marco/agentic-workflow'],
         },
      ],
   },
   {
      title: 'Dashboard',
      url: '/dashboard',
      icon: dashboardIcon,
      matchPrefix: ['/dashboard'],
      items: [],
   },
   {
      title: 'Pulse',
      url: '/pulse/performance-insights',
      icon: pulseIcon,
      matchPrefix: ['/pulse'],
      items: [
         {
            title: 'Performance Insights',
            url: '/pulse/performance-insights',
            icon: performanceInsightIcon,
            matchPrefix: ['/pulse/performance-insights'],
         },
         {
            title: 'Web Insights',
            url: '/pulse/web-insights',
            icon: webInsightIcon,
            matchPrefix: ['/pulse/web-insights'],
         },
      ],
   },
];

const AppSidebarContent = () => {
   const navigate = useNavigate();
   const location = useLocation();

   const currentPath = location.pathname;

   const userDetails = LocalStorageService.getItem(
      Keys.FlableUserDetails,
   ) as AuthUser;

   const handleClick = (url: string) => {
      navigate(url);
   };

   const isMatch = (prefixes: string[]) => {
      if (!prefixes) return false;
      return prefixes.some((prefix) => currentPath.startsWith(prefix));
   };

   const sidebarContent = useMemo(() => {
      const baseContent = [...APP_SIDEBAR_CONTENT];

      if (userDetails?.user_role === 'Admin') {
         baseContent.push({
            title: 'Integrations',
            url: '/integrations',
            icon: integrationsIcon,
            items: [],
            matchPrefix: ['/integrations'],
         });
      }

      return baseContent;
   }, [userDetails?.user_role]);

   return (
      <SidebarGroup className='mt-4'>
         <SidebarMenu className='flex flex-col gap-3'>
            {sidebarContent.map((item) => (
               <Collapsible
                  key={item.title}
                  asChild
                  defaultOpen={item.isActive}
               >
                  <SidebarMenuItem>
                     <SidebarMenuButton
                        className='mb-2 py-5 hover:cursor-pointer text-black data-[active=true]:text-[#3c76e1] data-[active=true]:bg-[#e4f2ff]'
                        asChild
                        tooltip={item.title}
                        isActive={isMatch(item.matchPrefix)}
                        onClick={() => handleClick(item.url)}
                     >
                        <div>
                           <img
                              src={item.icon}
                              alt={`${item.title} icon`}
                              className='w-[24px]'
                              style={{
                                 filter: isMatch(item.matchPrefix)
                                    ? 'brightness(0) saturate(100%) invert(39%) sepia(99%) saturate(746%) hue-rotate(180deg) brightness(0.91) contrast(1.01)'
                                    : 'none',
                              }}
                           />
                           <span className='text-[16px] font-bold'>
                              {item.title}
                           </span>
                           {item.tag && (
                              <Badge className='text-[12px] text-white bg-gradient-to-r from-[#417BE7] to-[#2A4F92] font-semibold rounded-full'>
                                 {item.tag}
                              </Badge>
                           )}
                        </div>
                     </SidebarMenuButton>
                     {item?.items?.length ? (
                        <>
                           <CollapsibleTrigger
                              className='hover:cursor-pointer'
                              asChild
                           >
                              <SidebarMenuAction className='data-[state=open]:rotate-90 text-blue mt-1'>
                                 <ChevronRight
                                    style={{
                                       filter: isMatch(item.matchPrefix)
                                          ? 'brightness(0) saturate(100%) invert(39%) sepia(99%) saturate(746%) hue-rotate(180deg) brightness(0.91) contrast(1.01)'
                                          : 'none',
                                    }}
                                 />
                                 <span className='sr-only'>Toggle</span>
                              </SidebarMenuAction>
                           </CollapsibleTrigger>
                           <CollapsibleContent>
                              <SidebarMenuSub>
                                 {item.items?.map((subItem) => (
                                    <SidebarMenuSubItem
                                       key={subItem.title}
                                       className='mt-3 mb-3'
                                    >
                                       <SidebarMenuSubButton
                                          className='hover:cursor-pointer text-black data-[active=true]:text-[#3c76e1] data-[active=true]:bg-white'
                                          isActive={isMatch(
                                             subItem.matchPrefix,
                                          )}
                                          asChild
                                          onClick={() =>
                                             handleClick(subItem.url)
                                          }
                                       >
                                          <div>
                                             <img
                                                src={subItem?.icon}
                                                alt={`${subItem.title} icon`}
                                                className='w-[20px]'
                                                style={{
                                                   filter: isMatch(
                                                      subItem.matchPrefix,
                                                   )
                                                      ? 'brightness(0) saturate(100%) invert(39%) sepia(99%) saturate(746%) hue-rotate(180deg) brightness(0.91) contrast(1.01)'
                                                      : 'none',
                                                }}
                                             />
                                             <span className='text-[16px] font-bold'>
                                                {subItem.title}
                                             </span>
                                          </div>
                                       </SidebarMenuSubButton>
                                    </SidebarMenuSubItem>
                                 ))}
                              </SidebarMenuSub>
                           </CollapsibleContent>
                        </>
                     ) : null}
                  </SidebarMenuItem>
               </Collapsible>
            ))}
         </SidebarMenu>
      </SidebarGroup>
   );
};

export default AppSidebarContent;
