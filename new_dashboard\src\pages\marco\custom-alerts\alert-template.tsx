import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Switch } from '@/components/ui/switch';
import { setCurrentAgent } from '@/store/reducer/marco-reducer';
import { useAppDispatch, useAppSelector } from '@/store/store';
import { cn } from '@/utils';
import { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import ConditionBuilder from './condition-builder';
import { PiCheckBold, PiWarningCircleBold, PiX } from 'react-icons/pi';
import {
   FormFields,
   handleFormChange,
   resetForm,
} from '@/store/reducer/custom-alerts-reducer';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { VALID_EMAILS_PATTERN } from '@/utils/strings/settings-strings';
import { verifyFields } from '../utils/custom-alerts/custom-alerts-helpers';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import { AuthUser } from '@/types/auth';
import {
   useCreateOrUpdateCustomAlert,
   useFetchAllAlerts,
   useFetchCustomAlertOptions,
} from '../apis/custom-alerts-apis';

interface FormState {
   alert_name: string;
   alert_description: string;
   new_recipient: string;
   email_recipients: { email: string; valid: boolean }[];
   alert_time: string;
   alert_status: 'active' | 'inactive';
}

const AlertTemplate = () => {
   const navigate = useNavigate();
   const dispatch = useAppDispatch();

   const { client_id, user_id, email } = LocalStorageService.getItem(
      Keys.FlableUserDetails,
   ) as AuthUser;

   const { form: customAlertForm, editMode } = useAppSelector(
      (state) => state.customAlerts,
   );
   const { generalSettings } = useAppSelector((state) => state.settings);

   const [form, setForm] = useState<FormState>({
      alert_name: '',
      alert_description: '',
      new_recipient: '',
      email_recipients: [{ email, valid: true }],
      alert_time: '09:00',
      alert_status: 'active',
   });
   const [inputWidth, setInputWidth] = useState(20);

   const spanRef = useRef<HTMLSpanElement>(null);

   const { isFetching: isCustomAlertOptionsFetching } =
      useFetchCustomAlertOptions();

   const { refetch: refetchAllAlerts } = useFetchAllAlerts();

   const createOrUpdateAlert = useCreateOrUpdateCustomAlert();

   const handleInputChange = (
      e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
   ) => {
      const { name, value } = e.target;
      setForm((prev) => ({ ...prev, [name]: value }));
      dispatch(handleFormChange({ name: name as FormFields, value }));
   };

   const handleCheckedChange = (name: FormFields, checked: boolean) => {
      const value = checked ? 'active' : 'inactive';
      setForm((prev) => ({ ...prev, [name]: value }));
      dispatch(handleFormChange({ name, value }));
   };

   const focusToInput = () => {
      const inputElement = document.getElementById('new_recipient');
      if (inputElement) {
         inputElement.focus();
      }
   };

   const addRecipient = () => {
      if (!form.new_recipient) {
         toast.error('Please enter a valid email address');
         return false;
      }

      const result = VALID_EMAILS_PATTERN.test(form.new_recipient);

      const emailExists = form.email_recipients.some(
         (recipient) => recipient.email === form.new_recipient,
      );

      if (emailExists) {
         toast.info('Email already added');
         setForm((prevForm) => ({
            ...prevForm,
            new_recipient: '',
         }));
         return false;
      }

      setForm((prevForm) => ({
         ...prevForm,
         email_recipients: [
            ...prevForm.email_recipients,
            { email: form.new_recipient, valid: result },
         ],
         new_recipient: '',
      }));
      dispatch(
         handleFormChange({
            name: 'email_recipients',
            value: [
               ...form.email_recipients,
               { email: form.new_recipient, valid: result },
            ],
         }),
      );

      if (!result) {
         toast.error('Please enter a valid email address');
         return false;
      }

      return true;
   };

   const deleteRecipient = (idx: number) => {
      const newData = [...form.email_recipients];
      newData.splice(idx, 1);

      setForm((prevForm) => {
         return { ...prevForm, email_recipients: newData };
      });

      dispatch(
         handleFormChange({
            name: 'email_recipients',
            value: [...newData],
         }),
      );
   };

   const removeLastRecipient = () => {
      if (form.email_recipients.length === 0) {
         return;
      }

      setForm((prevForm) => ({
         ...prevForm,
         email_recipients: prevForm.email_recipients.slice(0, -1),
      }));
   };

   const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (
         e.key === ',' ||
         e.key === 'Enter' ||
         e.key === 'Tab' ||
         e.key === ' '
      ) {
         e.preventDefault();
         addRecipient();
      }

      if (e.key === 'Backspace' && !form.new_recipient) {
         e.preventDefault();
         removeLastRecipient();
      }
   };

   const handleSubmit = async () => {
      let validRecipient = false;

      if (form.new_recipient) {
         validRecipient = addRecipient();
      } else {
         if (!(customAlertForm.email_recipients.length > 0)) {
            toast.error('Please add at least one email recipient');
         }
         validRecipient = customAlertForm.email_recipients.length > 0;
      }

      if (!validRecipient) {
         return;
      }

      const formattedAlert = verifyFields(customAlertForm);

      if (!formattedAlert) {
         return;
      }

      const createOrUpdateAlertPayload = {
         ...formattedAlert,
         client_id,
         user_id,
         user_timezone: generalSettings?.timezone || 'UTC',
         emails_sent: 0,
         alert_id: customAlertForm.alert_id || null,
      };

      const result = await createOrUpdateAlert.mutateAsync(
         createOrUpdateAlertPayload,
      );

      if (result.status === 'error') {
         toast.error(result.message || 'Failed to create or update alert');
      } else {
         toast.success('Alert created successfully');
         dispatch(resetForm());
         navigate('/marco/custom-alerts/alerts');
         await refetchAllAlerts();
      }
   };

   useEffect(() => {
      if (editMode) {
         setForm({
            alert_name: customAlertForm.alert_name,
            alert_description: customAlertForm.alert_description,
            new_recipient: '',
            email_recipients: customAlertForm.email_recipients,
            alert_time: customAlertForm.alert_time,
            alert_status: customAlertForm.alert_status,
         });
      }
   }, [editMode]);

   useEffect(() => {
      if (spanRef.current) {
         const newWidth = spanRef.current.offsetWidth + 10;
         setInputWidth(newWidth < 20 ? 20 : newWidth);
      }
   }, [form.new_recipient]);

   return (
      <ScrollArea className='w-auto h-full p-5'>
         <div className='w-full h-auto m-auto'>
            <div className='flex items-center justify-between mb-4'>
               <div className='text-2xl font-bold'>Custom Alert</div>
               <div className='flex items-center gap-2'>
                  <Button
                     size='lg'
                     variant='outline'
                     onClick={() => {
                        dispatch(setCurrentAgent('alerting-agent'));
                        navigate('/marco/agentic-workflow');
                     }}
                  >
                     Back
                  </Button>
                  <Button
                     className='bg-green-900'
                     size='lg'
                     onClick={() => void handleSubmit()}
                     disabled={isCustomAlertOptionsFetching}
                  >
                     Submit
                  </Button>
               </div>
            </div>
            <div className='w-full h-auto p-1'>
               {/* ROW 1 */}
               <div className='w-full grid grid-cols-1 md:grid-cols-3 gap-4 mb-4'>
                  <div className='flex flex-col gap-1'>
                     <Label
                        htmlFor='alert_name'
                        className='text-[14px] ml-1 font-semibold gap-1'
                     >
                        Alert Name<span className='text-red-500'>*</span>
                     </Label>
                     <Input
                        className='h-[40px] border shadow-none focus-visible:ring-0 focus-visible:outline-none'
                        id='alert_name'
                        type='text'
                        name='alert_name'
                        value={form.alert_name}
                        onChange={handleInputChange}
                     />
                  </div>
                  <div className='md:col-span-2 flex flex-col gap-1'>
                     <Label
                        htmlFor='alert_description'
                        className='text-[14px] ml-1 font-semibold'
                     >
                        Alert Description
                     </Label>
                     <Input
                        className='h-[40px] border shadow-none focus-visible:ring-0 focus-visible:outline-none'
                        id='alert_description'
                        type='text'
                        name='alert_description'
                        value={form.alert_description}
                        onChange={handleInputChange}
                     />
                  </div>
               </div>

               {/* ROW 2 */}
               <div className='w-full grid grid-cols-1 sm:grid-cols-4 md:grid-cols-5 gap-4 mb-4'>
                  <div className='row-span-2 sm:col-span-3 md:col-span-4 flex flex-col gap-1'>
                     <Label
                        htmlFor='email_recipients'
                        className='text-[14px] ml-1 font-semibold gap-1'
                     >
                        Email Recipients<span className='text-red-500'>*</span>
                     </Label>
                     <div
                        onClick={focusToInput}
                        className='w-full h-full overflow-x-scroll hover:cursor-text flex items-start flex-wrap gap-1 rounded-[5px] p-1 border'
                     >
                        {form.email_recipients.length > 0 &&
                           form.email_recipients.map((recipient, index) => (
                              <Badge
                                 key={index}
                                 className={`text-[12px] text-white px-2 py-0 mt-1 ${
                                    recipient.valid
                                       ? 'bg-green-700'
                                       : 'bg-red-600'
                                 } font-semibold rounded-full`}
                              >
                                 <div className='flex items-center gap-1'>
                                    {recipient.valid ? (
                                       <PiCheckBold />
                                    ) : (
                                       <PiWarningCircleBold size={18} />
                                    )}
                                    {recipient.email}
                                 </div>
                                 <Button
                                    variant='ghost'
                                    className={`${
                                       recipient.valid
                                          ? 'hover:bg-green-700'
                                          : 'hover:bg-red-600'
                                    } hover:text-gray-300 hover:cursor-pointer w-[22px] text-[12px] p-0 h-[22px]`}
                                    onClick={() => deleteRecipient(index)}
                                 >
                                    <PiX />
                                 </Button>
                              </Badge>
                           ))}
                        <div className='relative'>
                           <span
                              ref={spanRef}
                              className='invisible whitespace-pre absolute top-0 left-0 z-[-1] text-[14px] px-2'
                           >
                              {form.new_recipient || ' '}
                           </span>
                           <Input
                              className='ml-0 px-2 border-0 shadow-none text-[14px] focus-visible:ring-0 focus-visible:outline-none'
                              autoComplete='off'
                              type='text'
                              id='new_recipient'
                              name='new_recipient'
                              value={form.new_recipient}
                              onKeyDown={handleKeyDown}
                              onChange={handleInputChange}
                              style={{
                                 width: `${inputWidth}px`,
                                 minWidth: '30px',
                                 maxWidth: '100%',
                              }}
                           />
                        </div>
                     </div>
                  </div>
                  <div className='col-span-1 flex flex-col gap-1'>
                     <Label
                        htmlFor='alert_time'
                        className='text-[14px] ml-1 font-semibold gap-1'
                     >
                        Alert Time<span className='text-red-500'>*</span>
                     </Label>
                     <Input
                        id='alert_time'
                        type='time'
                        name='alert_time'
                        step='1'
                        defaultValue='10:30'
                        className={cn(
                           'h-[40px] border shadow-none focus-visible:ring-0 focus-visible:outline-none',
                           'bg-background appearance-none [&::-webkit-calendar-picker-indicator]:hidden [&::-webkit-calendar-picker-indicator]:appearance-none',
                        )}
                        value={form.alert_time}
                        onChange={handleInputChange}
                     />
                  </div>
                  <div className='flex flex-col gap-1'>
                     <Label
                        htmlFor='alert_status'
                        className='text-[14px] ml-1 font-semibold'
                     >
                        Alert Status
                     </Label>
                     <Switch
                        id='alert_status'
                        checked={form.alert_status === 'active'}
                        className='ml-1'
                        onCheckedChange={(val) =>
                           handleCheckedChange('alert_status', val)
                        }
                     />
                  </div>
               </div>
            </div>

            {/* ROW 3 - Conditions */}
            <div className='w-full h-auto p-1'>
               <div className='text-lg font-semibold mb-4 flex items-center justify-between'>
                  <div>Conditions:</div>
               </div>
               <div className='flex flex-col gap-4'>
                  <ConditionBuilder />
               </div>
            </div>
         </div>
      </ScrollArea>
   );
};

export default AlertTemplate;
