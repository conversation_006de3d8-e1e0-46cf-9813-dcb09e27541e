@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500&display=swap');

.SocialListening {
   .Main {
      // font-family: 'Poppins', sans-serif;
      font-size: 28px;
      font-weight: 500;
      color: #242424;
      margin-bottom: 25px;
      margin-left: 20px;
      margin-top: 10px;
   }
   .channels {
      margin-top: 50px;
      display: flex;
      gap: 40px;
      flex-wrap: wrap;
   }
   .widget-container {
      display: flex;
      flex-direction: column;
      gap: 20px;
      padding: 10px;

      .social-media {
         display: flex;
         flex-direction: column;
         gap: 20px;
         .title {
            display: flex;
            align-items: center;
            gap: 10px;
            h5 {
               color: #242424;
               font-family: 'Poppins', sans-serif;
               font-size: 16px;
               font-style: normal;
               font-weight: 500;
               line-height: 28px;
               margin-left: 25px;
            }
         }
         .info {
            display: flex;
            align-items: center;
            gap: 3px;
            background-color: rgb(250, 240, 240);
            color: red;
            font-size: 10px;
            border-radius: 2px;
            padding: 2px 5px;
            font-weight: 700;
         }
         .social-media__components {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            margin-left: 20px;
         }
      }
   }
}
[data-theme='dark'] {
   .Main {
      color: #ffffff; // Dark theme text color
   }
   .channels {
      background-color: #333333; // Dark theme background color
   }
   .widget-container {
      .social-media {
         .title {
            h5 {
               color: #ffffff; // Dark theme title color
            }
         }
      }
   }
}
