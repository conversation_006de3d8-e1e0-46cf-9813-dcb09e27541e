import {
   Box,
   Heading,
   Table,
   Tbody,
   Td,
   Th,
   Thead,
   Tr,
   Text,
} from '@chakra-ui/react';
import { AnalysisDataRow } from '../../../../api/service/agentic-workflow/meta-ads-manager';
interface AnalysisTableProps {
   analysisData: AnalysisDataRow[];
   recommendation: string;
}
export const AnalysisTable = ({
   analysisData,
   recommendation,
}: AnalysisTableProps) => {
   if (!analysisData || analysisData.length === 0) return null;

   const headers = Object.keys(analysisData[0]);

   return (
      <Box
         p={4}
         border='1px solid #e2e8f0'
         borderRadius='md'
         mt={4}
         width='100%'
         bg='gray.50'
      >
         <Heading size='md' mb={4}>
            Ad-creative Analysis
         </Heading>
         <Box overflowX='auto'>
            <Table variant='simple'>
               <Thead>
                  <Tr>
                     {headers.map((key) => (
                        <Th key={key}>
                           {key.replace(/_/g, ' ').toUpperCase()}
                        </Th>
                     ))}
                  </Tr>
               </Thead>
               <Tbody>
                  {analysisData.map((row, index) => (
                     <Tr key={index}>
                        {headers.map((key) => (
                           <Td key={key}>
                              {typeof row[key as keyof typeof row] === 'number'
                                 ? (
                                      row[key as keyof typeof row] as number
                                   ).toFixed(2)
                                 : (row[key as keyof typeof row] ?? '-')}
                           </Td>
                        ))}
                     </Tr>
                  ))}
               </Tbody>
            </Table>
         </Box>
         <Text fontSize='lg' fontWeight='bold' mb={2} mt={4}>
            Recommendation :
         </Text>
         <Text>• {recommendation}</Text>
      </Box>
   );
};
