import {
   Button,
   InputGroup,
   InputLeftElement,
   NumberDecrementStepper,
   NumberIncrementStepper,
   NumberInput,
   NumberInputField,
   NumberInputStepper,
   Spinner,
   Td,
   Tr,
   useColorModeValue,
} from '@chakra-ui/react';
import endPoints, { PaymentMethod } from '../../../api/service/cfo';
import { MdOutlinePercent } from 'react-icons/md';
import { FaIndianRupeeSign } from 'react-icons/fa6';
import { setPaymentMethods } from '../../../store/reducer/cfo-reducer';
import { useDispatch } from 'react-redux';
import { useApiMutation } from '../../../hooks/react-query-hooks';
import { CFOKeys } from '../../dashboard/utils/query-keys';
import { Keys, LocalStorageService } from '../../../utils/local-storage';

function PaymentM(props: {
   pm: PaymentMethod;
   handleValueChange: (
      newValue: string,
      pm: PaymentMethod,
      type: string,
   ) => void;
   pMethods: PaymentMethod[];
}) {
   const { pm, handleValueChange, pMethods } = props;

   const dispatch = useDispatch();
   const handleSuccess = (data: string) => {
      if (data) {
         dispatch(setPaymentMethods(pMethods));
      }
   };
   const { mutate: updateFixedRate, isPending: saveLoad } = useApiMutation({
      queryKey: [CFOKeys.upsertPaymentMethod],
      mutationFn: endPoints.upsertPaymentMethod,
      onSuccessHandler: handleSuccess,
   });
   const handlePMSave = (pm: PaymentMethod) => {
      updateFixedRate({
         ...pm,
         clientId: LocalStorageService.getItem(Keys.ClientId) as string,
      });
   };
   return (
      <Tr id={pm.method}>
         <Td>{pm.method}</Td>
         <Td>
            {' '}
            <InputGroup
               backgroundColor={useColorModeValue('white', 'var(--controls)')}
            >
               <InputLeftElement pointerEvents='none'>
                  <MdOutlinePercent />
               </InputLeftElement>
               <NumberInput
                  width={'100%'}
                  value={pm.cost || ''}
                  onChange={(value: string) =>
                     void handleValueChange(value, pm, 'cost')
                  }
               >
                  <NumberInputField
                     pl={8}
                     // value={cogsPerc}
                     // onChange={(event: React.ChangeEvent<HTMLInputElement>) =>
                     //    setcogsPerc(event.target.value)
                     // }
                  />
                  <NumberInputStepper>
                     <NumberIncrementStepper />
                     <NumberDecrementStepper />
                  </NumberInputStepper>
               </NumberInput>
            </InputGroup>
         </Td>
         <Td>
            <InputGroup
               backgroundColor={useColorModeValue('white', 'var(--controls)')}
            >
               <InputLeftElement pointerEvents='none'>
                  <FaIndianRupeeSign />
               </InputLeftElement>
               <NumberInput
                  width={'100%'}
                  value={pm.fee || ''}
                  onChange={(value: string) =>
                     handleValueChange(value, pm, 'fee')
                  }
               >
                  <NumberInputField
                     pl={8}
                     // value={cogsPerc}
                     // onChange={(event: React.ChangeEvent<HTMLInputElement>) =>
                     //    setcogsPerc(event.target.value)
                     // }
                  />
                  <NumberInputStepper>
                     <NumberIncrementStepper />
                     <NumberDecrementStepper />
                  </NumberInputStepper>
               </NumberInput>
            </InputGroup>
         </Td>
         <Td width={0}>
            {' '}
            <Button
               color={'white'}
               _hover={{
                  backgroundColor: '#437EEBBB',
               }}
               backgroundColor={'#437EEB'}
               py={4}
               px={6}
               border={'1px solid #437EEB'}
               borderRadius={'7px'}
               onClick={() => handlePMSave(pm)}
               disabled={saveLoad}
               _disabled={{
                  cursor: 'not-allowed',
                  color: 'grey',
               }}
            >
               {' '}
               Save
               {saveLoad && (
                  <Spinner
                     ml={2}
                     thickness='4px'
                     speed='0.65s'
                     emptyColor='gray.200'
                     color='blue.500'
                     size='sm'
                  />
               )}
            </Button>
         </Td>
      </Tr>
   );
}

export default PaymentM;
