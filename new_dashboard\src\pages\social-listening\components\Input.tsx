import React from 'react';
import './Input.scss';

interface InputProps {
   label: string;
   onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
   value: string;
   id: string;
   placeholder?: string;
   name: string;
   type?: string;
   disabled?: boolean;
   extra?: boolean;
   extraValue?: string;
}

const Input: React.FC<InputProps> = ({
   label,
   onChange,
   value,
   id,
   placeholder,
   extra,
   extraValue,
   type = 'text',
   disabled,
   ...props
}) => (
   <div className='custom-input-group'>
      <label htmlFor={id}>{label}</label>
      {extra ? (
         <div className='extra'>
            <input
               type={type}
               id={id}
               onChange={onChange}
               value={value}
               placeholder={placeholder}
               disabled={disabled}
               {...props}
            />

            <input type={type} id={id} value={extraValue} disabled />
         </div>
      ) : (
         <input
            type={type}
            id={id}
            onChange={onChange}
            value={value}
            placeholder={placeholder}
            disabled={disabled}
            {...props}
         />
      )}
   </div>
);

export default Input;
