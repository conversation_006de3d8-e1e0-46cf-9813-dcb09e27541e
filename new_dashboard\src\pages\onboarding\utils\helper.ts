import { UserMasterList } from '../../../api/service/onboarding';

export const removeDuplicatesAndSort = (arr: string[] | undefined) => {
   return [...new Set(arr)].sort();
};

export const splitIndustryCategories = (industries: string[] | undefined) => {
   if (!industries) return {};

   const industryMap: { [key: string]: string[] } = {};

   industries.forEach((item) => {
      const [industry, category] = item.split('_');

      if (!industryMap[industry]) {
         industryMap[industry] = [];
      }

      industryMap[industry].push(category);
   });

   return industryMap;
};

export const retrieveOptions = (list: UserMasterList[], type: string) => {
   return list
      .find((item: UserMasterList) => item.type === type)
      ?.value.split(/,(?![^(]*\))/);
};
