import {
   Box,
   Table,
   Tbody,
   Td,
   Th,
   Thead,
   Tr,
   Text,
   List,
   ListItem,
} from '@chakra-ui/react';

interface BudgetTableProps {
   data: Record<string, string | number | null | undefined>[];
   recommendation: string[];
}

const BudgetTable: React.FC<BudgetTableProps> = ({ data, recommendation }) => {
   if (!data || data.length === 0) return null;

   const headers = Object.keys(data[0]);

   return (
      <Box
         p={4}
         border='1px solid #e2e8f0'
         borderRadius='md'
         mt={4}
         width='100%'
         bg='gray.50'
      >
         <Text fontSize='lg' fontWeight='bold' mb={4}>
            Budget Analysis
         </Text>

         <Table variant='simple' size='sm'>
            <Thead>
               <Tr>
                  {headers.map((key) => (
                     <Th key={key}>{key.replace(/_/g, ' ')}</Th>
                  ))}
               </Tr>
            </Thead>
            <Tbody>
               {data.map((row, index) => (
                  <Tr key={index}>
                     {headers.map((key) => (
                        <Td key={key}>{row[key] !== null ? row[key] : '-'}</Td>
                     ))}
                  </Tr>
               ))}
            </Tbody>
         </Table>

         {recommendation && recommendation.length > 0 && (
            <Box mt={6}>
               <Text fontSize='lg' fontWeight='bold' mb={2}>
                  Recommendations
               </Text>
               <List spacing={2} pl={4} styleType='disc'>
                  {recommendation.map((rec, i) => (
                     <ListItem key={i}>{rec}</ListItem>
                  ))}
               </List>
            </Box>
         )}
      </Box>
   );
};

export default BudgetTable;
