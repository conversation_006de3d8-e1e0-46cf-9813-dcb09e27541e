import { Button, Flex, Heading } from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
interface ErrorPageProps {
   err: string;
   resetError: () => void;
}
function ErrorPage(props: ErrorPageProps) {
   const navigate = useNavigate();
   const location = useLocation();
   const [path] = useState(location.pathname);
   const handleGoBack = () => {
      props.resetError();
      navigate(-1);
   };
   const handleRefresh = () => {
      props.resetError();
      navigate(0);
   };
   useEffect(() => {
      if (path !== location.pathname) {
         props.resetError();
         navigate(location.pathname);
      }
   }, [location.pathname]);
   return (
      <Flex
         height={'50vh'}
         direction={'column'}
         gap={10}
         justifyContent={'center'}
         alignItems={'center'}
      >
         <Heading size={'lg'} width={'65%'} textAlign={'center'}>
            An error occurred while loading the page. Our team will get it
            fixed, contact Flable support for more details.
         </Heading>
         {/* <Text color={'red'}>Message:- {props.err}</Text> */}
         <Flex gap={5}>
            <Button onClick={handleGoBack}>Back</Button>
            <Button onClick={handleRefresh}>Refresh</Button>
         </Flex>
      </Flex>
   );
}

export default ErrorPage;
