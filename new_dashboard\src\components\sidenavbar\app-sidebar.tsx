import {
   Sidebar,
   SidebarContent,
   SidebarHeader,
   SidebarTrigger,
   // SidebarFooter,
} from '@/components/ui/sidebar';
import FlableIcon from '@/assets/image/flableicon.png';
import AppSidebarContent from './app-sidebar-content';
import AppSidebarFooter from './app-sidebar-footer';
import TourPopover from '../poppers/tour-popover';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import { AuthUser } from '@/types/auth';

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
   const userDetails = LocalStorageService.getItem(
      Keys.FlableUserDetails,
   ) as AuthUser;

   return (
      <Sidebar
         className='px-[15px] h-[100vh] max-h-[100vh] overflow-hidden'
         collapsible='offcanvas'
         {...props}
      >
         <SidebarHeader className='!bg-[#ffffff]'>
            <div className='flex items-center justify-between'>
               <img
                  src={FlableIcon}
                  className='w-[100px] md:w-[130px]'
                  alt='flable icon'
               />
               <SidebarTrigger className='ml-2 hover:cursor-pointer' />
            </div>
         </SidebarHeader>
         <SidebarContent className='!bg-[#ffffff] flex flex-col h-full'>
            <AppSidebarContent />
            <TourPopover />
            {userDetails?.user_role === 'Admin' && <AppSidebarFooter />}
         </SidebarContent>
         {/* <SidebarFooter></SidebarFooter> */}
      </Sidebar>
   );
}
