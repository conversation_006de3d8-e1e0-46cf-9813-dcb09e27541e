import { useEffect, useState } from 'react';
import {
   <PERSON><PERSON>,
   <PERSON>over<PERSON><PERSON>ger,
   <PERSON>over<PERSON>ontent,
   PopoverCloseButton,
   PopoverBody,
   Box,
   Text,
   IconButton,
   Flex,
} from '@chakra-ui/react';
import {
   <PERSON>a<PERSON>lock,
   FaTwitter,
   FaLinkedin,
   FaFacebook,
   FaYoutube,
   FaEdit as EditIcon,
   FaTrash as DeleteIcon,
} from 'react-icons/fa';
import { CalendarItem } from '../../types/social-watch';
import SimpleImageSlider from '../sliders/simple-image-slider';
import socialWatchEndpoints from '../../api/service/social-watch/apis';
import TruncatableText from '../custom-text/truncated-text';

interface PopupProps {
   openPopup: boolean;
   setOpenPopup: (open: boolean) => void;
   setDialogData: (data: CalendarItem | null) => void;
   dialogData: CalendarItem | null;
   handleDelete: () => void;
   handleEdit: (uuid: string) => void;
   position: { x: number; y: number } | null;
}

const Popup: React.FC<PopupProps> = ({
   openPopup,
   setOpenPopup,
   setDialogData,
   dialogData,
   handleDelete,
   handleEdit,
   position,
}) => {
   const [imageDecode, setImageDecode] = useState<string[]>([]);
   const [loading, setLoading] = useState(true);
   useEffect(() => {
      async function decodeImageAsync() {
         if (!dialogData || !dialogData.media) return;
         setLoading(true);
         try {
            const imageLinks = dialogData.media.map((item) =>
               socialWatchEndpoints
                  .decodeAzureMediaUrl({
                     image_url: item.image_link!,
                  })
                  .then((res) => res.data),
            );
            const res = await Promise.all(imageLinks);
            setImageDecode(res.map((res) => res.uri));
         } catch (err) {
            console.log('[ERROR] Image decode error ', JSON.stringify(err));
         } finally {
            setLoading(false);
         }
      }

      void decodeImageAsync();
   }, [dialogData?.media]);

   const handleClose = () => {
      setOpenPopup(false);
      setImageDecode([]);
      setDialogData(null);
   };

   if (!openPopup || !dialogData) {
      return null;
   }

   return (
      <Popover
         isOpen={openPopup}
         onClose={handleClose}
         placement='auto'
         closeOnBlur={true}
         closeOnEsc
      >
         <PopoverTrigger>
            <Box
               position='absolute'
               left={position ? `${position.x}px` : '0'}
               top={position ? `${position.y + 100}px` : '0'}
            />
         </PopoverTrigger>
         <PopoverContent borderRadius='20px'>
            <PopoverCloseButton />
            <Box
               display='flex'
               justifyContent='center'
               alignItems='center'
               padding='20px'
            >
               {loading ? (
                  <Text>Loading...</Text>
               ) : imageDecode.length > 0 ? (
                  <SimpleImageSlider images={imageDecode} />
               ) : (
                  <Text fontWeight={600}>No Image uploaded</Text>
               )}
            </Box>
            <PopoverBody>
               <div style={{ height: 200, overflowY: 'auto' }}>
                  <TruncatableText text={dialogData.title} maxLength={300} />
               </div>

               <Flex color='gray.500' mt={2} justifyContent={'space-between'}>
                  <FaClock />
                  &nbsp;
                  {new Date(dialogData.start ?? '').toLocaleString()}
                  <Box float='right'>
                     {dialogData.social_media_type?.toLowerCase() ===
                        'twitter' && (
                        <IconButton
                           aria-label='Twitter'
                           icon={<FaTwitter />}
                           colorScheme='twitter'
                           variant='ghost'
                        />
                     )}
                     {dialogData.social_media_type.toLowerCase() ===
                        'linkedin' && (
                        <IconButton
                           aria-label='LinkedIn'
                           icon={<FaLinkedin />}
                           colorScheme='linkedin'
                           variant='ghost'
                        />
                     )}
                     {dialogData.social_media_type.toLowerCase() ===
                        'facebook' && (
                        <IconButton
                           aria-label='Facebook'
                           icon={<FaFacebook />}
                           colorScheme='facebook'
                           variant='ghost'
                        />
                     )}
                     {dialogData.social_media_type.toLowerCase() ===
                        'youtube' && (
                        <IconButton
                           aria-label='YouTube'
                           icon={<FaYoutube />}
                           colorScheme='red'
                           variant='ghost'
                        />
                     )}
                  </Box>
               </Flex>
               <Box
                  mt={4}
                  borderRadius='5px'
                  backgroundColor={
                     dialogData?.is_schedule ? '#FAF4DA' : '#DAF4FA'
                  }
                  padding='5px 10px'
                  display='inline-block'
               >
                  <Text color={dialogData.is_schedule ? '#D8B840' : 'blue'}>
                     {dialogData.is_schedule ? 'Scheduled' : 'In Draft'}
                  </Text>
               </Box>
               <Box
                  display='flex'
                  justifyContent='flex-end'
                  alignItems='center'
                  mt={4}
               >
                  <IconButton
                     aria-label='Edit'
                     icon={<EditIcon />}
                     onClick={() => handleEdit(dialogData.uuid ?? '')}
                     colorScheme='blue'
                     size='sm'
                  />
                  <IconButton
                     aria-label='Delete'
                     icon={<DeleteIcon />}
                     onClick={() => handleDelete()}
                     colorScheme='red'
                     size='sm'
                     ml={2}
                  />
               </Box>
            </PopoverBody>
         </PopoverContent>
      </Popover>
   );
};

export default Popup;
