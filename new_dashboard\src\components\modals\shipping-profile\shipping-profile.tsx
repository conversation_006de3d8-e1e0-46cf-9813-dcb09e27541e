import { Flex } from '@chakra-ui/react';
import ModalWrapper from '../modal-wrapper';
import ProfileName from './profile-name';
import './shipping-profile.scss';
import { useEffect, useState } from 'react';
import RateSetting from './rate-setting';
import { useDispatch } from 'react-redux';
import { setShippingProfile } from '../../../store/reducer/cfo-reducer';
import { useAppSelector } from '../../../store/store';

function ShippingProfileModal() {
   const [step, setstep] = useState<string>('1');
   const currentModalProps = useAppSelector(
      (state) => state.modal.payload?.modalProps,
   ) as {
      edit: boolean;
   };
   const dispatch = useDispatch();
   useEffect(() => {
      return () => {
         dispatch(setShippingProfile(null));
      };
   }, []);
   return (
      <ModalWrapper
         heading={`${currentModalProps?.edit ? 'Edit' : 'Create'} Shipping Profile`}
         parentClassName='shipping-profile'
      >
         <Flex py={4} borderTop={'1px solid #C2CBD4'} direction={'column'}>
            {step == '1' ? (
               <ProfileName setstep={setstep} />
            ) : (
               <RateSetting setstep={setstep} />
            )}
         </Flex>
      </ModalWrapper>
   );
}

export default ShippingProfileModal;
