import { useState, useEffect } from 'react';
import { Box, Text } from '@chakra-ui/react';

export interface LoaderWithTextFlowProps {
   messages: string[];
}

const LoaderWithTextFlow = (props: LoaderWithTextFlowProps) => {
   const { messages } = props;

   const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
   const [fade, setFade] = useState(true);

   useEffect(() => {
      const interval = setInterval(() => {
         setFade(false);

         setTimeout(() => {
            setCurrentMessageIndex(
               (prevIndex) => (prevIndex + 1) % messages.length,
            );
            setFade(true);
         }, 500);
      }, 1500);

      return () => clearInterval(interval);
   }, []);

   return (
      <Box textAlign='center' p={6}>
         <Text
            fontSize='lg'
            fontWeight='bold'
            color='gray.700'
            opacity={fade ? 1 : 0}
            transition='opacity 0.5s ease-in-out'
         >
            {messages[currentMessageIndex]}
         </Text>
      </Box>
   );
};

export default LoaderWithTextFlow;
