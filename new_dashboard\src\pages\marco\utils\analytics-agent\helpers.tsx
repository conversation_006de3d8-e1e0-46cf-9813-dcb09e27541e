import {
   AnalyticsAgentChat,
   ChunkData,
   StreamChunk,
} from '@/api/service/agentic-workflow/analytics-agent';
import ChunkRenderer from './chunk-renderer';
import { UserSocialDetails } from '@/api/service/onboarding';
import {
   parseISO,
   isToday,
   isYesterday,
   isAfter,
   subDays,
   subMonths,
   subYears,
} from 'date-fns';
import { AppDispatch, RootState } from '@/store/store';
import { addChunk, setChunks } from '@/store/reducer/analytics-agent-reducer';

export const convertToHTMLTable = (input: string): string => {
   const lines = input.trim().split('\n');
   if (lines.length < 2) return '';

   const headers = lines[0]
      .split('|')
      .map((h) => h.trim().toUpperCase())
      .filter(Boolean);

   const rows = lines.slice(2).map((line) =>
      line
         .split('|')
         .map((cell) => cell.trim())
         .filter(Boolean),
   );

   const html = `
    <table style="border-collapse: collapse; margin-top: 1rem; width: 100%;">
      <thead>
        <tr>
          ${headers.map((h) => `<th style="border: 1px solid #ccc; padding: 8px;">${h}</th>`).join('')}
        </tr>
      </thead>
      <tbody>
        ${rows
           .map(
              (row) =>
                 `<tr>${row
                    .map(
                       (cell) =>
                          `<td style="border: 1px solid #eee; padding: 8px;">${cell}</td>`,
                    )
                    .join('')}</tr>`,
           )
           .join('')}
      </tbody>
    </table>
  `;

   return html;
};

export const handleStreamedResponse = (
   info: ChunkData,
   dispatch: AppDispatch,
   getState: () => RootState,
) => {
   const { chunks } = getState().analyticsAgent;

   const normalizedType = info.type?.trim().toLowerCase();

   const renderableTypes = ['tool_call', 'tool_input', 'transition'];

   if (renderableTypes.includes(normalizedType)) {
      dispatch(
         addChunk({
            type: normalizedType as StreamChunk['type'],
            node: <ChunkRenderer chunk={info} />,
         }),
      );
      return;
   }

   if (normalizedType === 'thought') {
      const index = chunks.findIndex((chunk) => chunk.type === 'thought');

      if (index !== -1) {
         const existingChunk = chunks[index];

         dispatch(
            setChunks([
               {
                  ...existingChunk,
                  content: (existingChunk.content || '') + info.content,
               },
            ]),
         );
      } else {
         dispatch(
            addChunk({
               type: 'thought',
               content: info.content,
            }),
         );
      }

      return;
   }

   if (normalizedType === 'final_result') {
      return;
   }

   console.warn(`Unhandled stream type: ${normalizedType}`);
};

export const getChunkNodes = (
   chunks: ChunkData[],
): {
   type: 'tool_call' | 'tool_input' | 'transition' | 'thought' | 'final_result';
   node: JSX.Element;
}[] => {
   const renderableTypes = ['tool_call', 'tool_input', 'transition'];

   return chunks
      .filter((chunk) =>
         renderableTypes.includes(chunk.type?.trim().toLowerCase()),
      )
      .map((chunk) => ({
         type: chunk.type.trim().toLowerCase() as
            | 'tool_call'
            | 'tool_input'
            | 'transition'
            | 'thought'
            | 'final_result',
         node: <ChunkRenderer chunk={chunk} />,
      }));
};

export const getSuggestionSubset = (suggestions: string[], count: number) => {
   const shuffled = [...suggestions].sort(() => 0.5 - Math.random());
   return shuffled.slice(0, count);
};

export const getSmartSuggestions = (
   connections: UserSocialDetails[],
   suggestionsMap: {
      web: string[];
      facebookads: string[];
      store: string[];
      googleads: string[];
   },
) => {
   const aliasMap: Record<string, string> = {
      shopify: 'store',
      flable_pixel: 'web',
   };

   const available = Array.from(
      new Set(
         connections.map(
            ({ channel_name }) => aliasMap[channel_name] || channel_name,
         ),
      ),
   ).filter(
      (ch) =>
         suggestionsMap[ch as keyof typeof suggestionsMap] &&
         suggestionsMap[ch as keyof typeof suggestionsMap].length > 0,
   );

   const result: string[] = [];

   if (available.length === 1) {
      result.push(
         ...getSuggestionSubset(
            suggestionsMap[available[0] as keyof typeof suggestionsMap],
            4,
         ),
      );
   } else if (available.length === 2) {
      result.push(
         ...getSuggestionSubset(
            suggestionsMap[available[0] as keyof typeof suggestionsMap],
            2,
         ),
      );
      result.push(
         ...getSuggestionSubset(
            suggestionsMap[available[1] as keyof typeof suggestionsMap],
            2,
         ),
      );
   } else if (available.length === 3) {
      result.push(
         ...getSuggestionSubset(
            suggestionsMap[available[0] as keyof typeof suggestionsMap],
            1,
         ),
      );
      result.push(
         ...getSuggestionSubset(
            suggestionsMap[available[1] as keyof typeof suggestionsMap],
            1,
         ),
      );
      result.push(
         ...getSuggestionSubset(
            suggestionsMap[available[2] as keyof typeof suggestionsMap],
            1,
         ),
      );

      const extraSource = getSuggestionSubset(available, 1)[0];
      result.push(
         ...getSuggestionSubset(
            suggestionsMap[extraSource as keyof typeof suggestionsMap],
            1,
         ),
      );
   } else {
      for (let i = 0; i < Math.min(4, available.length); i++) {
         result.push(
            ...getSuggestionSubset(
               suggestionsMap[available[i] as keyof typeof suggestionsMap],
               1,
            ),
         );
      }
   }

   return result;
};

type GroupedHistory = {
   today: AnalyticsAgentChat[];
   yesterday: AnalyticsAgentChat[];
   last7Days: AnalyticsAgentChat[];
   lastMonth: AnalyticsAgentChat[];
   lastYear: AnalyticsAgentChat[];
   older: AnalyticsAgentChat[];
};

export const groupAgentChatsByDate = (
   chatList: AnalyticsAgentChat[],
): GroupedHistory => {
   const grouped: GroupedHistory = {
      today: [],
      yesterday: [],
      last7Days: [],
      lastMonth: [],
      lastYear: [],
      older: [],
   };

   const now = new Date();

   for (const chat of chatList) {
      const date = parseISO(chat.updated_at);

      if (isToday(date)) {
         grouped.today.push(chat);
      } else if (isYesterday(date)) {
         grouped.yesterday.push(chat);
      } else if (isAfter(date, subDays(now, 7))) {
         grouped.last7Days.push(chat);
      } else if (isAfter(date, subMonths(now, 1))) {
         grouped.lastMonth.push(chat);
      } else if (isAfter(date, subYears(now, 1))) {
         grouped.lastYear.push(chat);
      } else {
         grouped.older.push(chat);
      }
   }

   for (const key in grouped) {
      grouped[key as keyof GroupedHistory].sort((a, b) => {
         const dateA = parseISO(a.updated_at).getTime();
         const dateB = parseISO(b.updated_at).getTime();
         return dateB - dateA;
      });
   }

   return grouped;
};

export const formatSqlForHtml = (rawSql: string): string => {
   if (!rawSql) return '';

   const sql = rawSql
      .replace(/\s+/g, ' ')
      .replace(
         /\b(SELECT|FROM|WHERE|GROUP BY|ORDER BY|AND|OR|INNER JOIN|LEFT JOIN|RIGHT JOIN|JOIN|ON|AS|IN|INSERT INTO|VALUES|UPDATE|SET|DELETE|CREATE TABLE|ALTER TABLE|DROP TABLE|LIMIT|OFFSET)\b/gi,
         '\n$1',
      )
      .trim();

   const lines = sql.split('\n').map((line) => line.trim());

   let indentLevel = 0;
   const indentedLines = lines.map((line) => {
      const lower = line.toLowerCase();

      if (lower.startsWith('from') || lower.startsWith('where'))
         indentLevel = 1;
      else if (lower.startsWith('group by') || lower.startsWith('order by'))
         indentLevel = 2;
      else if (
         lower.startsWith('and') ||
         lower.startsWith('or') ||
         lower.startsWith('join') ||
         lower.startsWith('on')
      )
         indentLevel = 3;

      return '&nbsp;'.repeat(indentLevel * 4) + escapeHtml(line);
   });

   const highlighted = indentedLines
      .join('<br/>')
      .replace(
         /\b(SELECT|FROM|WHERE|GROUP BY|ORDER BY|AND|OR|JOIN|LEFT JOIN|RIGHT JOIN|ON|AS|INSERT INTO|VALUES|UPDATE|SET|DELETE|CREATE TABLE|ALTER TABLE|DROP TABLE|LIMIT|OFFSET)\b/gi,
         (match) =>
            `<span style="color: #7c3aed; font-weight: bold;">${match.toUpperCase()}</span>`,
      );

   return `<pre style="white-space: pre-wrap; font-family: monospace;">${highlighted}</pre>`;
};

const escapeHtml = (unsafe: string): string =>
   unsafe
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
