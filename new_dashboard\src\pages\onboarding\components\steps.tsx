import { Box } from '@chakra-ui/react';
import { useAppSelector } from '../../../store/store';

import WelcomeStep from './welcome-step/welcome-step';
import AccountSetupStep from './account-setup-step/account-setup-step';
import IntegrationsStep from './integrations-step/integrations-step';
import FlablePixelStep from './flable-pixel-step/flable-pixel-step';
import CompetitorStep from './competitors-step/competitor-step';

const Steps = () => {
   const { registerProgress } = useAppSelector((state) => state.onboarding);

   const renderStep = (registerProgress: string): JSX.Element => {
      switch (registerProgress) {
         case 'Step 1':
            return <WelcomeStep />;
         case 'Step 2':
            return <AccountSetupStep />;
         case 'Step 3':
            return <IntegrationsStep />;
         case 'Step 4':
            return <FlablePixelStep />;
         case 'Step 5':
            return <CompetitorStep />;
         default:
            return <WelcomeStep />;
      }
   };

   return (
      <Box width='70%' height='100%'>
         {renderStep(registerProgress)}
      </Box>
   );
};

export default Steps;
