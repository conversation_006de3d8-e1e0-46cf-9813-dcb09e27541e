parameters:
   - name: env
     displayName: Deployment Environment
     type: string
     default: develop
     values:
        - develop
        - master
        - master_de

variables:
   - ${{ if and(eq(parameters.env, 'develop'), eq(variables['Build.SourceBranchName'], 'develop')) }}:
        - group: flable-main-kvt
        - name: containerRegistry
          value: 'flabledacr4ce3.azurecr.io' #EU DEV
        - name: containerRegistryName
          value: 'flabledacr4ce3' #EU DEV
        - name: imageRepository
          value: 'new_dashboard'
        - name: sshServiceConnection
          value: eu-dev-ssh-conn
        - name: environmentDeploymentGate
          value: env-dev-ui-deployment
        - name: dockerRegistryServiceConnection
          value: eu-dev-docker-deployment-sc
        - name: serviceConnection
          value: infra-we-dev-deployment-sc
        - name: keyvault
          value: flable-d-kv-4bf4
        - name: containerAppName
          value: flable-d-eu-ca-newdash-4257
        - name: resourceGroup
          value: flable-dev-infra-rg-datatransform

   - ${{ if and(eq(parameters.env, 'master_de'), eq(variables['Build.SourceBranchName'], 'master')) }}:
        - group: flable-eu-prod-main-kvt
        - name: containerRegistry
          value: 'flablepacr01a1.azurecr.io'
        - name: containerRegistryName
          value: 'flablepacr01a1' #EU Prod
        - name: imageRepository
          value: 'new_dashboard'
        - name: sshServiceConnection
          value: eu-prod-ssh-conn
        - name: environmentDeploymentGate
          value: env-prod-ui-deployment
        - name: dockerRegistryServiceConnection
          value: eu-prd-docker-deployment-sc
        - name: serviceConnection
          value: infra-we-prd-deployment-sc
        - name: keyvault
          value: flable-p-kv-dbd2
        - name: containerAppName
          value: 'flable-p-eu-ca-newdash-1b60' # Update when container app is deployed
        - name: resourceGroup
          value: flable-prd-infra-rg-datatransform

   - ${{ if and(eq(parameters.env, 'master'), eq(variables['Build.SourceBranchName'], 'master')) }}:
        - group: flable-ind-prod-main-kvt
        - name: containerRegistry
          value: 'flablepacr4e52.azurecr.io'
        - name: containerRegistryName
          value: 'flablepacr4e52' #IND Prod
        - name: imageRepository
          value: 'new_dashboard'
        - name: sshServiceConnection
          value: ind-prod-ssh-conn
        - name: environmentDeploymentGate
          value: env-prod-ui-deployment
        - name: dockerRegistryServiceConnection
          value: ind-prd-docker-deployment-sc
        - name: serviceConnection
          value: infra-in-prd-deployment-sc
        - name: keyvault
          value: flable-p-kv-5a7c
        - name: containerAppName
          value: flable-p-in-ca-newdash-0838
        - name: resourceGroup
          value: flable-prd-in-infra-rg-datatransform
