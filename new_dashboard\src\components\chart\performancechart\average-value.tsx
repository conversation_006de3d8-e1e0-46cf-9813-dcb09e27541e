import { Flex, Text } from '@chakra-ui/react';
import { PerformanceChartData } from '../../chatbox/interface';
import {
   getFormattedVal,
   getMetricDescription,
} from '../../../pages/dashboard/utils/helpers';

interface AverageKPIValueProps {
   performanceData: PerformanceChartData | undefined;
}
function AverageKPIValue(props: AverageKPIValueProps) {
   if (!props.performanceData?.average_KPI_values) return null;
   const data = props.performanceData.average_KPI_values;
   const { fields } = props.performanceData.schema;
   const validFields = fields.filter((f) => !f.name.includes('cumulative'));
   if (data.length < 2) {
      return;
      // const val = Number(Object.values(data[0])[0]);
      // return (
      //    <Text fontWeight={700}>
      //       Average: {getFormattedVal(Math.round(val * 100) / 100)}
      //    </Text>
      // );
   }

   return (
      <Flex direction={'column'} gap={2} maxWidth={'100%'}>
         <Text fontWeight={700}>Aggregated: </Text>
         <Flex overflow={'auto'}>
            <table className='kpi-table'>
               <thead>
                  <tr>
                     {data.map((field, j) => (
                        <th key={j}>
                           {getMetricDescription(Object.keys(field)[0])}
                        </th>
                     ))}
                  </tr>
               </thead>
               <tbody>
                  <tr>
                     {data.map((field, i) => {
                        const showVal = getFormattedVal(
                           Math.round(Number(Object.values(field)[0]) * 100) /
                              100,
                        );

                        return <td key={i}>{showVal}</td>;
                     })}
                  </tr>
               </tbody>
            </table>
         </Flex>
         <Text fontWeight={700} pb={3}>
            {getMetricDescription(validFields.slice(1)[0].name)}-wise Data
         </Text>
      </Flex>
   );
}

export default AverageKPIValue;
