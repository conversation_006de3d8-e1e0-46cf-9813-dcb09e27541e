import { Tooltip, Icon, PlacementWithLogical } from '@chakra-ui/react';
import { InfoIcon } from '@chakra-ui/icons';

interface TooltipMessageProps {
   label: string;
   placement?: PlacementWithLogical;
   fontSize?: string;
   iconColor?: string;
   ml?: number;
   mt?: number;
   useOutlineIcon?: boolean;
   boxSize?: number;
}

const TooltipIcon: React.FC<TooltipMessageProps> = ({
   label,
   placement = 'top',
   fontSize = 'small',
   iconColor = 'blue.500',
   ml,
   mt,
   boxSize,
}) => {
   return (
      <Tooltip label={label} placement={placement} fontSize={fontSize}>
         <Icon
            as={InfoIcon}
            cursor='pointer'
            color={iconColor}
            ml={ml}
            mt={mt}
            boxSize={boxSize}
         />
      </Tooltip>
   );
};

export default TooltipIcon;
