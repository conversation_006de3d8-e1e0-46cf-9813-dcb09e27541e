import React, { useState } from 'react';
import {
   <PERSON><PERSON>,
   <PERSON><PERSON><PERSON>ontent,
   <PERSON><PERSON><PERSON>eader,
   <PERSON><PERSON><PERSON><PERSON>le,
   DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useAppSelector } from '../../store/store';
import { useDispatch } from 'react-redux';
import { closeModal } from '../../store/reducer/modal-reducer';

interface MultiSelectModalProps {
   title?: string;
   options: Record<string, string>;
   onSelect: (selectedValues: string[] | null) => void;
   confirmButtonText?: string;
   showCancelButton?: boolean;
   minSelections?: number;
}

const MultiSelectModal: React.FC = () => {
   const dispatch = useDispatch();
   const { payload } = useAppSelector((state) => state.modal);

   const {
      title = 'Select Items',
      options = {},
      onSelect,
      confirmButtonText = 'Save',
      showCancelButton = true,
      minSelections = 1,
   } = (payload?.modalProps || {}) as MultiSelectModalProps;

   const [selectedValues, setSelectedValues] = useState<string[]>([]);

   const handleCheckboxChange = (value: string, checked: boolean) => {
      let newSelectedValues: string[];

      if (checked) {
         newSelectedValues = [...selectedValues, value];
      } else {
         newSelectedValues = selectedValues.filter((v) => v !== value);
      }

      setSelectedValues(newSelectedValues);
   };

   const handleConfirm = () => {
      onSelect(selectedValues);
      dispatch(closeModal());
   };

   const handleCancel = () => {
      onSelect(null);
      dispatch(closeModal());
   };

   return (
      <Dialog open onOpenChange={() => handleCancel()}>
         <DialogContent
            className='max-w-lg bg-white dark:bg-zinc-900 shadow-2xl rounded-xl border-0 z-50 p-0 overflow-hidden'
            onInteractOutside={(e) => e.preventDefault()}
         >
            <div className='bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-zinc-800 dark:to-zinc-700 px-6 py-4 border-b border-gray-100 dark:border-zinc-600'>
               <DialogHeader className='space-y-0'>
                  <DialogTitle className='text-xl font-semibold text-gray-900 dark:text-white'>
                     {title}
                  </DialogTitle>
                  <p className='text-sm text-gray-600 dark:text-gray-400 mt-1'>
                     Select{' '}
                     {minSelections > 1
                        ? `at least ${minSelections} items`
                        : 'one or more items'}
                  </p>
               </DialogHeader>
            </div>

            <div className='px-5 py-5'>
               <div className='space-y-3 max-h-80 overflow-y-auto p-1'>
                  {Object.entries(options).map(([value, label]) => (
                     <div
                        key={value}
                        className={`flex items-center space-x-3 px-3 py-3 rounded-md cursor-pointer transition-all duration-200 ${
                           selectedValues.includes(value)
                              ? 'bg-blue-50 dark:bg-blue-900/20 border !border-blue-500'
                              : 'bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-600 hover:border-blue-300 hover:bg-blue-50/50 dark:hover:bg-zinc-700'
                        }`}
                        onClick={(e) => {
                           e.preventDefault();
                           handleCheckboxChange(
                              value,
                              !selectedValues.includes(value),
                           );
                        }}
                     >
                        <div
                           className={`w-[16px] h-[16px] border-[2px] cursor-pointer flex items-center justify-center transition-all duration-200 ${
                              selectedValues.includes(value)
                                 ? 'bg-blue-600 !border-blue-600'
                                 : 'bg-white dark:bg-zinc-800 border-gray-300 dark:border-zinc-600'
                           }`}
                           style={{ borderRadius: '4px' }}
                           onClick={(e) => {
                              e.stopPropagation();
                              handleCheckboxChange(
                                 value,
                                 !selectedValues.includes(value),
                              );
                           }}
                        >
                           {selectedValues.includes(value) && (
                              <svg
                                 className='w-3 h-3 text-white'
                                 fill='currentColor'
                                 viewBox='0 0 20 20'
                              >
                                 <path
                                    fillRule='evenodd'
                                    d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z'
                                    clipRule='evenodd'
                                 />
                              </svg>
                           )}
                        </div>
                        <label
                           htmlFor={value}
                           className='text-sm font-medium leading-relaxed cursor-pointer flex-1 select-none text-gray-900 dark:text-white'
                           onClick={(e) => {
                              e.preventDefault();
                              handleCheckboxChange(
                                 value,
                                 !selectedValues.includes(value),
                              );
                           }}
                        >
                           {label}
                        </label>
                     </div>
                  ))}
               </div>
            </div>

            <div className='px-6 py-4   dark:border-zinc-600'>
               <DialogFooter className='flex justify-end space-x-3'>
                  {showCancelButton && (
                     <Button
                        variant='outline'
                        onClick={handleCancel}
                        className='px-6 py-2 border-gray-300 dark:border-zinc-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-zinc-700 transition-colors duration-200'
                     >
                        Cancel
                     </Button>
                  )}
                  <Button
                     onClick={handleConfirm}
                     disabled={selectedValues.length < minSelections}
                     className={`px-6 py-2 font-medium transition-all duration-200 ${
                        selectedValues.length < minSelections
                           ? 'bg-gray-300 dark:bg-zinc-600 text-gray-500 dark:text-zinc-400 cursor-not-allowed'
                           : 'bg-blue-600 hover:bg-blue-500 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5'
                     }`}
                  >
                     {confirmButtonText}{' '}
                     {selectedValues.length > 0 && `(${selectedValues.length})`}
                  </Button>
               </DialogFooter>
            </div>
         </DialogContent>
      </Dialog>
   );
};

export default MultiSelectModal;
