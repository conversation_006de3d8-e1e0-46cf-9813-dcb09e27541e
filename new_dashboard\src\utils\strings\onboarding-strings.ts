import shopifyLogo from '../../assets/image/onboarding/integration-logos/shopify-logo.png';
import twitterLogo from '../../assets/image/onboarding/integration-logos/twitter-logo.png';

export const welcomeStep = {
   title: 'Welcome',
   info: 'You are minutes away from unifying your data, reducing customer acquisition cost and increasing retention.',
   question: 'What best describes your organisation?',
};

export const accountSetupStep = {
   title: 'Account Setup',
   info: 'Flable will use the information you provide to create a customised experience for your organisation.',
   agency: 'Agency Details',
   brand: 'Brand Information',
   regionLanguage: 'Region & Language',
   updateUserFailed: 'Failed to update user',
   missingFieldsHeader: 'Missing fields',
   missingFields: 'Please fill all the required fields',
   incorrectURL: 'Please enter a valid URL',
   urlRegex: /^(https?:\/\/)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(:\d+)?(\/[^\s]*)?$/, // /^(www\.)?[a-zA-Z0-9-]+\.[a-zA-Z]{2,}(\/[^\s]*)?$/
};

export const integrationsStep = {
   title: 'Integrations',
   info: 'Flable brings all of your data together to give you a clear view of all the important kpis and customer actions. Connect ad platforms and your website to Flable to see every step of your customer journey.',
   channel: 'Channel',
   channelInfo: 'Some text here to explain the feature',
   socialMedia: 'Social Media',
   socialMediaInfo: 'Some text here to explain the feature',
   channelIcons: [
      {
         name: 'Shopify',
         logo: shopifyLogo,
      },
   ],
   socialMediaIcons: [
      {
         logo: twitterLogo,
         name: 'Twitter',
      },
   ],
};

export const flablePixelStep = {
   title: 'Flable Pixel',
   info: 'The Flable Pixel is essential for collecting data about your website visitors and customer journeys.',
   sendSnippet: 'Send your snippet to a teammate.',
   tidyInstructions: 'Nice, tidy instructions for your favorite engineer.',
   sendingSnippet: 'Who are you sending this snippet to?',
   buttonText: 'Send your snippet',
   installSnippet: 'Install your snippet.',
   pasteSnippet: 'Paste your Flable snippet in the head tag of your website.',
   sendSnippetSuccessMessage: 'Snippet sent successfully',
   sendSnippetFailedMessage: 'Something went wrong. Please try again later.',
   testConnectionSuccessMessage: 'Connection successful',
   testConnectionFailedMessage: 'Connection failed',
};

export const competitorStep = {
   title: 'Competitor',
   info: 'Flable provides you with a competitor post on social media platforms to take inspiration and create fresh content ideas.',
};
