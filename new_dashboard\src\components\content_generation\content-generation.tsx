import React, { useEffect, useState } from 'react';
import {
   Box,
   Button,
   Checkbox,
   Stack,
   Textarea,
   Text,
   useToast,
   useColorMode,
} from '@chakra-ui/react';
import { ChevronLeftIcon } from '@chakra-ui/icons';
import { useDispatch } from 'react-redux';
import { setAiText, setLoading } from '../../store/reducer/configReducer';
import { useAppSelector } from '../../store/store';
import GenerateContent from '../../assets/image/contentgenration.svg';

import introJs from 'intro.js';
import { TooltipPosition } from 'intro.js/src/packages/tooltip';
import { componentNames, setFlag } from '../../store/reducer/tour-reducer';

import {
   toastMessage,
   buttonMessage,
} from '../../utils/strings/content-manager';
import { Keys, LocalStorageService } from '../../utils/local-storage';
import socialWatchEndpoints from '../../api/service/social-watch/apis';
import { AuthUser } from '../../types/auth';
import { socialLabel } from '../../utils/strings/socialwatch-strings';
import { setSelectedTab } from '../../store/reducer/user-details-reducer';
import useIntegrationConnectionDetails from '../../hooks/use-integration-connection-details';

interface Props {
   handleBack: () => void;
   handleBackToWrapper: () => void;
}

const ContentGeneration: React.FC<Props> = ({
   handleBack,
   handleBackToWrapper,
}) => {
   const [text, setText] = useState<string>('');
   const dispatch = useDispatch();
   const toast = useToast();
   const userDetails = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   );

   const { mediaTypes } = useIntegrationConnectionDetails();

   const [selectedOptions, setSelectedOptions] = useState<string[]>([]);

   const { contentGeneration } = useAppSelector((state) => state.tour);

   const intro = introJs();
   const stateLoading = useAppSelector((state) => state.config.loading);
   const handleCheckboxChange = (option: string) => {
      setSelectedOptions((prevSelected) =>
         prevSelected.includes(option)
            ? prevSelected.filter((o) => o !== option)
            : [...prevSelected, option],
      );
   };

   const handleAiSuggestion = async () => {
      dispatch(setLoading(true));

      try {
         const responses = await Promise.all(
            selectedOptions.map(async (option) => {
               dispatch(setSelectedTab(option));
               const payload = {
                  client_id: userDetails!.client_id,
                  social_channel:
                     option.charAt(0).toUpperCase() + option.slice(1),
                  reference_text: text,
                  tone: '',
                  no_hashtags: '5',
                  word_size: '',
                  top_posts: '',
                  top_competitor_posts: '',
                  top_hashtags: '',
               };
               return await socialWatchEndpoints.getAiSuggestion(payload);
            }),
         );
         responses.forEach((response) => {
            if (response && response.data) {
               dispatch(
                  setAiText({
                     media: response.data.media,
                     text: response.data.captions[0],
                  }),
               );

               handleBackToWrapper();
            }
         });
         toast({
            title: toastMessage.copilotMessageSuccess.title,
            description: toastMessage.copilotMessageSuccess.description,
            status: 'success',
            duration: 5000,
            isClosable: true,
         });
         handleBackToWrapper();

         sessionStorage.setItem('aiSuggestion', text);
         sessionStorage.setItem('aibutton', 'Regenerate');
      } catch (error) {
         console;
         toast({
            title: toastMessage.copilotMessageError.title,
            description: toastMessage.copilotMessageError.description,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
         console.error(error);
      } finally {
         dispatch(setLoading(false));
      }
   };
   const steps: {
      element: string;
      intro: string;
      position: TooltipPosition;
   }[] = [
      {
         element: '#mediaBox-linkedin',
         intro: 'Generate content based on the social platform’s genre. ',
         position: 'top',
      },
      // {
      //    element: '#mediaBox-twitter',
      //    intro: 'Dropdown',
      //    position: 'top',
      // },
      {
         element: '#generateBtn',
         intro: 'Generate content to create the customized content for selected social media platforms with AI.',
         position: 'top',
      },
      // {
      //    element: '#contentCreationSection3',
      //    intro: 'Dropdown',
      //    position: 'top',
      // },
   ];

   const startTour = () => {
      intro.setOptions({ steps });
      void intro.start();

      dispatch(
         setFlag({
            componentName: componentNames.CONTENT_CREATOR,
            flag: false,
         }),
      );
   };

   useEffect(() => {
      if (contentGeneration) startTour();
   }, [contentGeneration]);

   const { colorMode } = useColorMode();

   return (
      <>
         <Button
            onClick={handleBack}
            variant='ghost'
            style={{
               color:
                  colorMode === 'dark'
                     ? 'var(--chakra-colors-whiteAlpha-900)'
                     : 'var(--chakra-colors-gray-800)',
               textAlign: 'center',
            }}
            justifyContent='flex-start'
         >
            <ChevronLeftIcon />
            Back
         </Button>
         <Box
            p={4}
            ml='20%'
            mr='20%'
            style={{
               background: colorMode === 'dark' ? 'var(--background)' : 'white',
            }}
         >
            <Box mb={4}>
               <Box
                  display='flex'
                  justifyContent='center'
                  alignItems='center'
                  mb={4}
               >
                  <img
                     src={GenerateContent}
                     alt='Generate Content'
                     style={{
                        filter: colorMode === 'dark' ? 'invert(1)' : 'none',
                        opacity: colorMode === 'dark' ? '0.87' : '1',
                     }}
                  />
               </Box>
               <Text fontSize='xl' fontWeight='bold' textAlign='center'>
                  Content Generation
               </Text>
            </Box>
            <Text
               fontSize='md'
               textAlign='center'
               mb={2}
               color={colorMode === 'dark' ? 'whiteAlpha.900' : 'gray.800'}
            >
               Write down for which idea you want to generate content !!
            </Text>
            <Box border='1px solid #A0A4A8' borderRadius='md' p={4} mb={4}>
               <Text
                  fontSize='lg'
                  color={colorMode === 'dark' ? 'whiteAlpha.900' : '#000000'}
                  mb={4}
               >
                  Give your idea here
               </Text>
               <Box borderBottom='1px solid #A0A4A8' mb={4} />
               <Textarea
                  placeholder='content goes here....'
                  size='md'
                  width='100%'
                  height='200px'
                  value={text}
                  onChange={(e) => setText(e.target.value)}
                  bg={colorMode === 'dark' ? 'var(--background)' : 'white'}
                  color={colorMode === 'dark' ? 'whiteAlpha.900' : 'gray.800'}
                  borderColor={
                     colorMode === 'dark' ? 'whiteAlpha.300' : 'gray.200'
                  }
                  _hover={{
                     borderColor:
                        colorMode === 'dark' ? 'whiteAlpha.400' : 'gray.300',
                  }}
                  _focus={{
                     borderColor: 'blue.500',
                     boxShadow: 'outline',
                  }}
                  _placeholder={{
                     color:
                        colorMode === 'dark' ? 'whiteAlpha.400' : 'gray.500',
                     opacity: colorMode === 'dark' ? 0.6 : 1,
                  }}
               />
               <Box mt={4}>
                  <Text
                     fontSize='xs'
                     color={colorMode === 'dark' ? 'whiteAlpha.900' : '#000000'}
                     mb={4}
                  >
                     For which social media account you want to generate content
                  </Text>
                  <Stack direction='row' spacing={4}>
                     {mediaTypes.map((media) => (
                        <div
                           id={`mediaBox-${media.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}`}
                           key={media}
                        >
                           <Checkbox
                              id={`checkbox-${media.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}`}
                              colorScheme='green'
                              checked={selectedOptions.includes(media)}
                              onChange={() => handleCheckboxChange(media)}
                           >
                              {socialLabel[media] || media}
                           </Checkbox>
                        </div>
                     ))}
                  </Stack>
               </Box>
            </Box>

            <Stack direction='row' spacing={4}>
               <Button
                  id='generateBtn'
                  ml='180px'
                  style={{
                     backgroundColor: '#437EEB',
                     color: 'white',
                     textAlign: 'center',
                  }}
                  onClick={() => void handleAiSuggestion()}
                  loadingText='Generating'
                  isLoading={stateLoading}
                  spinnerPlacement='start'
                  isDisabled={!text || !selectedOptions.length}
               >
                  {buttonMessage.generate}
               </Button>
            </Stack>
         </Box>
      </>
   );
};

export default ContentGeneration;
