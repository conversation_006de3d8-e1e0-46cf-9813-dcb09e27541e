/* .handle-preloader {
   background: linear-gradient(
      to bottom,
      rgb(118, 31, 227) 0%,
      rgba(0, 0, 0, 1) 100%
   );
} */

.handle-preloader {
   align-items: center;
   -webkit-align-items: center;
   display: flex;
   display: -ms-flexbox;
   height: 100%;
   justify-content: center;
   -webkit-justify-content: center;
   position: fixed;
   left: 0;
   top: 0;
   width: 100%;
   z-index: 9999999;
}

.preloader-close {
   position: fixed;
   z-index: 9999999999999;
   font-size: 18px;
   background: #ffffff;
   width: 30px;
   height: 30px;
   line-height: 26px;
   text-align: center;
   cursor: pointer;
   right: 15px;
   top: 15px;
   border-radius: 0%;
   display: none;
}

.handle-preloader .animation-preloader {
   position: absolute;
   z-index: 100;
}

.handle-preloader .animation-preloader .spinner {
   animation: spinner 1s infinite linear;
   border-radius: 50%;
   height: 150px;
   margin: 0 auto 45px auto;
   width: 150px;
}

.handle-preloader .animation-preloader .txt-loading {
   text-align: center;
   user-select: none;
}

.handle-preloader .animation-preloader .txt-loading .letters-loading:before {
   animation: letters-loading 4s infinite;
   content: attr(data-text-preloader);
   /* text-transform: capitalize; */
   left: 0;
   opacity: 0;
   top: 0;
   position: absolute;
   font-family: 'Inter', sans-serif;
}
.handle-preloader .animation-preloader .txt-loading .words-loading:before {
   animation: letters-loading 4s infinite;
   content: attr(data-text-preloader);
   /* text-transform: capitalize; */
   left: 0;
   opacity: 0;
   top: 0;
   position: absolute;
   font-family: 'Inter', sans-serif;
}

.handle-preloader .animation-preloader .txt-loading .letters-loading {
   font-weight: 500;
   letter-spacing: 15px;
   display: inline-block;
   position: relative;
   font-size: 40px;
   line-height: 40px;
   font-family: 'Inter', sans-serif;
   color: linear-gradient(
      to bottom,
      rgb(118, 31, 227) 0%,
      rgba(0, 0, 0, 1) 100%
   );
   -webkit-text-stroke-width: 1px;
   /* -webkit-text-stroke-color: rgba(255, 255, 255, 0.3); */
}
.handle-preloader .animation-preloader .txt-loading .words-loading {
   font-weight: 500;
   letter-spacing: 10px;
   position: relative;
   font-size: 20px;
   line-height: 20px;
   font-family: 'Inter', sans-serif;
   color: transparent;
   -webkit-text-stroke-width: 1px;

   -webkit-text-stroke-color: rgba(255, 255, 255, 0.3);
}

.handle-preloader .animation-preloader > .letters-loading:nth-child(2):before {
   animation-delay: 0.2s;
}
.favIcon {
   display: flex;
   align-items: center;
   justify-content: center;
}
.handle-preloader .animation-preloader > .letters-loading:nth-child(3):before {
   animation-delay: 0.4s;
}
.handle-preloader
   .animation-preloader
   .txt-loading
   .letters-loading:nth-child(4):before {
   animation-delay: 0.6s;
}
.handle-preloader
   .animation-preloader
   .txt-loading
   .letters-loading:nth-child(5):before {
   animation-delay: 0.8s;
}
.handle-preloader
   .animation-preloader
   .txt-loading
   .letters-loading:nth-child(6):before {
   animation-delay: 1s;
}
.handle-preloader
   .animation-preloader
   .txt-loading
   .letters-loading:nth-child(7):before {
   animation-delay: 1.2s;
}
.handle-preloader
   .animation-preloader
   .txt-loading
   .letters-loading:nth-child(8):before {
   animation-delay: 1.4s;
}
.handle-preloader .loader-section {
   background-color: #fff;
   height: 100%;
   position: fixed;
   top: 0;
   width: calc(50% + 1px);
}

.preloader .loaded .animation-preloader {
   opacity: 0;
   transition: 0.3s ease-out;
}

.handle-preloader .animation-preloader .txt-loading .letters-loading:before {
   color: #fff;
}

.handle-preloader .animation-preloader .spinner {
   border: 3px solid #fff;
   border-top-color: rgba(255, 255, 255, 0.5);
}

/* AnimaciÃƒÆ’Ã†â€™Ãƒâ€ Ã¢â‚¬â„¢ÃƒÆ’Ã¢â‚¬ ÃƒÂ¢Ã¢â€šÂ¬Ã¢â€žÂ¢ÃƒÆ’Ã†â€™ÃƒÂ¢Ã¢â€šÂ¬Ã…Â¡ÃƒÆ’Ã¢â‚¬Å¡Ãƒâ€šÃ‚Â³n del preloader */
@keyframes spinner {
   to {
      transform: rotateZ(360deg);
   }
}
@keyframes letters-loading {
   0%,
   75%,
   100% {
      opacity: 0;
      transform: rotateY(-90deg);
   }

   25%,
   50% {
      opacity: 1;
      transform: rotateY(0deg);
   }
}

@media screen and (max-width: 767px) {
   .handle-preloader .animation-preloader .spinner {
      height: 8em;
      width: 8em;
   }
}
@media screen and (max-width: 500px) {
   .handle-preloader .animation-preloader .spinner {
      height: 7em;
      width: 7em;
   }
   .handle-preloader .animation-preloader .txt-loading .letters-loading {
      font-size: 30px;
      letter-spacing: 10px;
   }
}
