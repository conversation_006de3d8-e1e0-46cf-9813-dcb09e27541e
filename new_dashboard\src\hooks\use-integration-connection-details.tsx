import { useAppSelector } from '../store/store';

function useIntegrationConnectionDetails() {
   const { connections, isFetching } = useAppSelector(
      (state) => state.integration,
   );

   const mediaTypes = !isFetching
      ? Object.entries(connections)
           .map((item) => {
              return item[1] !== null ? item[0] : '';
           })
           .filter(Boolean)
      : [];

   return { mediaTypes };
}

export default useIntegrationConnectionDetails;
