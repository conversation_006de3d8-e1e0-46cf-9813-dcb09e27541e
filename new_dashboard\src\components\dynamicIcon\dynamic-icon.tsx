import {
   <PERSON><PERSON><PERSON><PERSON>,
   Fa<PERSON><PERSON>tter,
   FaLinkedin,
   FaFacebook,
   FaYoutube,
} from 'react-icons/fa';
import moment from 'moment';
import { IconType } from 'react-icons';

interface IconData {
   type: keyof typeof iconMap;
   fontSize: number;
   paddingTop?: string;
   condition?: string;
   color?: string;
}

interface Data {
   icon: IconData;
   timeFormat: string;
   socialMediaIcons: IconData[];
}

const iconMap = {
   FaClock,
   FaTwitter,
   FaLinkedin,
   FaFacebook,
   FaYoutube,
} as const;

const data: Data = {
   icon: {
      type: 'FaClock',
      fontSize: 20,
      paddingTop: '5px',
   },
   timeFormat: 'hh:mm',
   socialMediaIcons: [
      {
         type: 'FaTwitter',
         condition: 'twitter',
         color: 'blue.500',
         fontSize: 20,
      },
      {
         type: 'FaLinkedin',
         condition: 'linkedin',
         color: 'blue.700',
         fontSize: 20,
      },
      {
         type: 'FaFacebook',
         condition: 'facebook',
         color: 'blue.600',
         fontSize: 20,
      },
      {
         type: 'FaYoutube',
         condition: 'youtube',
         color: 'red.500',
         fontSize: 20,
      },
   ],
};

interface Item {
   start: string | Date | moment.Moment;
   social_media_type?: string;
   is_schedule: boolean;
}

interface DynamicIconsProps {
   item: Item;
}

const DynamicIcons: React.FC<DynamicIconsProps> = ({ item }) => {
   const ClockIcon: IconType = iconMap[data.icon.type];

   return (
      <div style={{ display: 'contents' }}>
         {item.is_schedule && (
            <ClockIcon
               fontSize={data.icon.fontSize}
               style={{ paddingTop: data.icon.paddingTop }}
            />
         )}
         &nbsp;
         {moment(item.start).format(data.timeFormat)}
         &nbsp;
         {data.socialMediaIcons.map((iconData, index) => {
            const IconComponent = iconMap[iconData.type]; // Add type assertion
            return (
               item?.social_media_type?.toLowerCase() ===
                  iconData.condition && (
                  <IconComponent
                     key={index}
                     color={iconData.color}
                     fontSize={iconData.fontSize}
                  />
               )
            );
         })}
      </div>
   );
};

export default DynamicIcons;
