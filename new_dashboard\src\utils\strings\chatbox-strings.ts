import sageOption from '../../assets/icons/sage-option-icon.svg';
import perfomanceOption from '../../assets/icons/perfomance-option-icon.svg';
import seoOption from '../../assets/icons/seo-option-icon.svg';
import webanalyticsOption from '../../assets/icons/webanalytics-option-icon.svg';
import socialanlyticsOption from '../../assets/icons/socialanalytics-option-icon.svg';

import { Option } from '../../components/chatbox/interface';
export const TEXTS = {
   selectedTimeFrame: 'Please select a time frame in (days):',
   marco: 'MARCO- Your AI Powered Analyst',
   sampleQuestionsHeading: 'Sample Questions for',
   askButtonText: 'Ask',
   selectedMetricMenu: 'Please select a metric',
   selectedObjectiveMenu: 'Please select an objective',
   channel: 'Please select a channel',
   topMatch: 'Please select one',
   errorAnswer: 'Server is busy, please try after some time',
};
export const modeOptions: Option[] = [
   {
      value: 'sage',
      label: 'Sage',
      description: 'Coming Soon',
      mode: 'sagemode',
      icon: sageOption,
      comingSoon: true,
      isActive: true,
      inActiveMsg: 'Please integrate with Flable Pixel',
   },
   {
      value: 'Performance Copilot',
      label: 'Performance',
      description: 'Analyze metrics and improve ads ',
      mode: 'Performance Copilot',
      icon: perfomanceOption,
      comingSoon: false,
      isActive: true,
      inActiveMsg: 'Please integrate with Google Ads or Meta Ads',
   },
   {
      value: 'seomode',
      label: 'SEO Mode',
      description: 'Coming Soon',
      mode: 'seomode',
      icon: seoOption,
      comingSoon: true,
      isActive: true,
      inActiveMsg: 'Please integrate with Flable Pixel',
   },
   {
      value: 'storemode',
      label: 'Store Mode',
      description: 'Coming Soon',
      mode: 'storemode',
      icon: webanalyticsOption,
      comingSoon: true,
      isActive: true,
      inActiveMsg: 'Please integrate with Flable Pixel',
   },
   {
      value: 'Console',
      label: 'Web Analytics',
      description: 'Insights into user behavior data.',
      mode: 'Console',
      icon: socialanlyticsOption,
      comingSoon: false,
      isActive: true,
      inActiveMsg: 'Please integrate with Flable Pixel',
   },
];
