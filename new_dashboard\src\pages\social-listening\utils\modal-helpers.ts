import store from '../../../store/store';
import { openModal } from '../../../store/reducer/modal-reducer';
import { modalTypes } from '../../../components/modals/modal-types';

//const dispatch = useAppDispatch();
export function showAccountSelectModal(
   options: Record<string, string>,
): Promise<string | null> {
   return new Promise((resolve) => {
      store.dispatch(
         openModal({
            modalType: modalTypes.ACCOUNT_SELECT_MODAL,
            modalProps: {
               title: 'Select GA Account',
               options,
               placeholder: 'Choose an account',
               confirmButtonText: 'Next',
               showCancelButton: true,
               onSelect: (selectedValue: string | null) => {
                  resolve(selectedValue);
               },
            },
         }),
      );
   });
}

export function showMultiSelectModal(
   title: string,
   options: Record<string, string>,
): Promise<string[] | null> {
   return new Promise((resolve) => {
      store.dispatch(
         openModal({
            modalType: modalTypes.MULTI_SELECT_MODAL,
            modalProps: {
               title,
               options,
               confirmButtonText: 'Save',
               showCancelButton: true,
               minSelections: 1,
               onSelect: (selectedValues: string[] | null) => {
                  resolve(selectedValues);
               },
            },
         }),
      );
   });
}

export function showConfirmationModal(
   title: string,
   message: string,
   options: {
      confirmButtonText?: string;
      cancelButtonText?: string;
      confirmButtonColor?: string;
      cancelButtonColor?: string;
      icon?: 'warning' | 'info' | 'success';
      showCancelButton?: boolean;
   } = {},
): Promise<boolean> {
   return new Promise((resolve) => {
      store.dispatch(
         openModal({
            modalType: modalTypes.CONFIRMATION_MODAL,
            modalProps: {
               title,
               message,
               confirmButtonText: options.confirmButtonText || 'OK',
               cancelButtonText: options.cancelButtonText || 'Cancel',
               confirmButtonColor: options.confirmButtonColor || 'blue',
               cancelButtonColor: options.cancelButtonColor || 'gray',
               icon: options.icon || 'info',
               showCancelButton: options.showCancelButton || false,
               onConfirm: () => resolve(true),
               onCancel: () => resolve(false),
            },
         }),
      );
   });
}
