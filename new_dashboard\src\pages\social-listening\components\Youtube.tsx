/* eslint-disable @typescript-eslint/no-misused-promises */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
// import { ElementRef, useEffect, useRef, useState } from 'react';
// import { channelNames } from '../utils/constant';
// import { connectToYoutube } from '../utils';
// import { Keys, LocalStorageService } from '../../../utils/local-storage';
// import { useApiMutation, useApiQuery } from '../../../hooks/react-query-hooks';
// import { SpinnerIcon } from '@chakra-ui/icons';

// import endPoints from '../apis/agent';
import Card from './Card';
import image from '../images/integrations/youtube.png';

// import SocialListeningQueryKeys from '../utils/query-keys';
// import { AuthUser } from '../../../types/auth';

import './Youtube.scss';

// function Youtube(): JSX.Element {
// const [switchInput, setSwitchInput] = useState<boolean>(false);
// const [channelId, setChannelId] = useState<string>('');
// const [loading, setLoading] = useState<boolean>(false);

// const [validationError, setValidationError] = useState('');

// const inputRef = useRef<ElementRef<'input'>>(null);
// const client_id = LocalStorageService.getItem<AuthUser>(
//    Keys.FlableUserDetails,
// )?.client_id;

// const {
//    mutate: mutateYoutubeMongo,
//    isPending: isConnectingToYoutubeMongo,
//    errorMessage: youtubeMongoConnectionError,
// } = useApiMutation({
//    queryKey: [SocialListeningQueryKeys.connectYoutubeMongo],
//    mutationFn: endPoints.connectWithYoutube,
//    onSuccessHandler: (data) => {
//       console.log('DATA CONNECTION ', data);
//    },
// });

// const {
//    mutate: disconnectYoutubeMongo,
// isPending: isDisconnectingToYoutubeMongo,
// errorMessage: youtubeMongoDisconnectionError,
// } = useApiMutation({
//    queryKey: [SocialListeningQueryKeys.disconnectYoutubeMongo],
//    mutationFn: endPoints.disconnectYoutube,
//    invalidateCacheQuery: [SocialListeningQueryKeys.connectionDetailsYoutube],
//    onSuccessHandler: (data) => {
//       console.log('DATA DISCONNECTION ', data);
//    },
// });

// Fetch the connection details
// const {
//    data: connectionDetails,
// isLoading: isConnectionLoading,
// errorMessage: connectionError,
// } = useApiQuery({
//    queryKey: [SocialListeningQueryKeys.connectionDetailsYoutube],
//    queryFn: () =>
//       endPoints.checkConnectionDetails({
//          client_id: client_id!,
//          channel_name: channelNames.YOUTUBE,
//       }),
// });

// const {
//    mutate: mutateYoutubeSentiment,
// isPending: isConnectingToYoutubeSentiment,
// errorMessage: youtubeSentimentConnectionError,
// } = useApiMutation({
//    queryKey: [SocialListeningQueryKeys.connectYoutubeSentiment],
//    mutationFn: connectToYoutube,
//    onSuccessHandler: (data) => {
//       console.log('DATA CONNECTION SENTIMENT ', data);
//    },
// });

// async function handleConnect() {
//    if (!channelId || !client_id) {
//       setValidationError('Please provide channel ID');
//       return;
//    }
//    try {
//       setLoading(true);
//       const {
//          data: { youtubeData, success },
//       } = await endPoints.getYoutubeChannelInfo(channelId);

//       if (!success) {
//          setValidationError('Channel is not valid');
//          setChannelId('');
//          return;
//       }

//       const { totalResults, url, title } = youtubeData;

//       if (totalResults === 0)
//          return setValidationError('Channel is not valid');
//       mutateYoutubeMongo({
//          channelId,
//          client_id,
//       });

//       if (youtubeMongoConnectionError) {
//          setValidationError(youtubeMongoConnectionError);
//          return;
//       }
//       // Sentiment connection
//       mutateYoutubeSentiment({
//          channel_name: channelNames.YOUTUBE,
//          client_id,
//          actual_account_name: title,
//          image_url: url,
//          channel_id: channelId,
//          isConnect: true,
//       });
//    } catch (err) {
//       const error = err as any;
//       alert(error.message);
//    } finally {
//       setLoading(false);
//    }
// }

// useEffect(() => {
//    inputRef.current?.focus();
// }, [switchInput]);

// function disconnect() {
//    try {
//       // setLoading(true);

//       if (confirm('Are you sure to disconnect youtube?')) {
//          if (client_id && connectionDetails?.details?.is_active) {
//             disconnectYoutubeMongo({
//                client_id,
//                channel_id: connectionDetails.details.channel_id,
//             });
//             mutateYoutubeSentiment({
//                channel_name: channelNames.YOUTUBE,
//                client_id,
//                isConnect: false,
//             });
//             // setSwitchInput(false);
//          }
//       }
//    } catch (err: any) {
//       alert(err.message);
//    } finally {
//       // setLoading(false);
//    }
// }
// if (connectionDetails?.details?.is_active) {
//    const { actual_account_name } = connectionDetails.details;
//    return (
//       <Card
//          heading={actual_account_name}
//          // isFetching={isDisconnectingToYoutubeMongo}
//          src={image}
//          // isConnected
//          commingSoon
//          onButtonClick={disconnect}
//       />
//    );
// }

// function handleCancel() {
//    setSwitchInput(false);
//    setValidationError('');
// }

// if (switchInput) {
//    return (
//       <Card
//          heading='Youtube'
//          src={image}
//          error={
//             validationError ||
//             youtubeMongoConnectionError ||
//             youtubeSentimentConnectionError
//          }
//       >
//          <div className='input-container'>
//             <input
//                ref={inputRef}
//                type='text'
//                className='input-field'
//                placeholder='Channel ID'
//                value={channelId}
//                onChange={(e) => setChannelId(e.target.value)}
//             />
//             <div className='input-buttons-container'>
//                <button
//                   disabled={loading || isConnectingToYoutubeMongo}
//                   className='btn-card'
//                   onClick={handleConnect}
//                >
//                   {(loading ||
//                      isConnectingToYoutubeMongo ||
//                      isConnectingToYoutubeSentiment) && <SpinnerIcon />}
//                   {loading || isConnectingToYoutubeMongo
//                      ? 'Connecting..'
//                      : 'Connect'}
//                </button>
//                <button
//                   disabled={loading || isConnectingToYoutubeMongo}
//                   className='btn-card'
//                   onClick={handleCancel}
//                >
//                   Cancel
//                </button>
//             </div>
//          </div>
//       </Card>
//    );
// }

// return (
//    <Card
//       error={connectionError || youtubeMongoDisconnectionError}
//       isFetching={isConnectionLoading}
//       heading='Youtube'
//       src={image}
//       onButtonClick={() => setSwitchInput(true)}
//    />
// );
// }

const Youtube: React.FC = () => (
   <Card heading='Youtube' src={image} commingSoon />
);

export default Youtube;
