import { useState, useRef, useEffect } from 'react';
import {
   Button,
   AlertDialog,
   AlertDialogBody,
   AlertDialogFooter,
   AlertDialogHeader,
   AlertDialogContent,
   AlertDialogOverlay,
   Select,
   Input,
   Box,
   useToast,
} from '@chakra-ui/react';
import { setAiText } from '../../store/reducer/configReducer';
import { useAppDispatch, useAppSelector } from '../../store/store';
import { userPrompt } from '../../utils/strings/socialwatch-strings';
import {
   toastMessage,
   buttonMessage,
   sessionKey,
   toneOptions,
   wordSizeOptions,
} from '../../utils/strings/content-manager';
import { Keys, LocalStorageService } from '../../utils/local-storage';
import socialWatchEndpoints from '../../api/service/social-watch/apis';
import { AuthUser } from '../../types/auth';

interface Props {
   isOpen: boolean;
   onClose: () => void;
}

const DialogBox = ({ isOpen, onClose }: Props) => {
   const [text, setText] = useState<string>('');
   const [tone, setTone] = useState<string>('');
   const [wordSize, setWordSize] = useState<string>('');
   const [loading, setLoading] = useState(false);

   const cancelRef = useRef<HTMLButtonElement>(null);
   const dispatch = useAppDispatch();
   const toast = useToast();

   const userDetails = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   );
   // const mediaType = useAppSelector((state) => state.media.mediaType || []);
   const { selectedTab } = useAppSelector((state) => state.media);
   const aiText = useAppSelector((state) => state.config.aiText[selectedTab]);
   const referenceText = aiText
      ? userPrompt(aiText, text)
      : `Using this idea "${text}", create an engaging and unique post that highlights my company's expertise and relevance in the industry. The content should seamlessly integrate my company name and align with industry trends. If the topic seems off-track, creatively adapt it to ensure it remains relevant to my company's focus and industry standards.`;

   const handleAiSuggestion = async () => {
      setLoading(true);
      const socialChannel =
         // mediaType.length > 0
         //    ? mediaType[0].social_media.charAt(0).toUpperCase() +
         //      mediaType[0].social_media.slice(1)
         //    : '';
         selectedTab.charAt(0).toUpperCase() + selectedTab.slice(1);

      const payload = {
         client_id: userDetails!.client_id,
         social_channel: socialChannel,
         reference_text: referenceText,
         tone,
         no_hashtags: '5',
         word_size: wordSize,
         top_posts: '',
         top_competitor_posts: '',
         top_hashtags: '',
      };

      try {
         const {
            data: { captions },
         } = await socialWatchEndpoints.getAiSuggestion(payload);

         if (captions.length > 0) {
            const generatedCaption = captions[0];
            dispatch(setAiText({ media: selectedTab, text: generatedCaption }));
            sessionStorage.setItem(sessionKey.aiSuggestion, generatedCaption);
            toast({
               title: toastMessage.copilotMessageSuccess.title,
               description: toastMessage.copilotMessageSuccess.description,
               status: 'success',
               duration: 5000,
               isClosable: true,
            });
         } else {
            toast({
               title: toastMessage.copilotMessageError.title,
               description: toastMessage.copilotMessageError.description,
               status: 'error',
               duration: 5000,
               isClosable: true,
            });
         }

         onClose();
      } catch (error) {
         const err = error as { response: { data: { message: string } } };
         console.error(err.response);

         toast({
            title: toastMessage.copilotMessageError.title,
            description: toastMessage.copilotMessageError.description,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      } finally {
         setLoading(false);
      }
   };

   useEffect(() => {
      if (!isOpen) {
         setText('');
         setTone('');
         setWordSize('');
      }
   }, [isOpen]);

   return (
      <AlertDialog
         isOpen={isOpen}
         onClose={onClose}
         leastDestructiveRef={cancelRef}
         isCentered
         motionPreset='slideInBottom'
      >
         <AlertDialogOverlay />
         <AlertDialogContent maxW='xl'>
            <AlertDialogHeader fontSize='lg' fontWeight='bold'>
               Regenerate
            </AlertDialogHeader>
            <AlertDialogBody
               display='flex'
               flexDirection='column'
               alignItems='center'
            >
               <Box display='flex' justifyContent='space-between' width='100%'>
                  <Input
                     placeholder='Additional instructions for regenerating'
                     size='sm'
                     width='90%'
                     value={text}
                     onChange={(e) => setText(e.target.value)}
                     mb={2}
                     mr={2}
                  />
                  <Select
                     placeholder='Tone'
                     width='30%'
                     mr={2}
                     onChange={(e) => setTone(e.target.value)}
                     size={'sm'}
                  >
                     {toneOptions.map((tone) => (
                        <option key={tone.value} value={tone.value}>
                           {tone.label}
                        </option>
                     ))}
                  </Select>
                  <Select
                     placeholder='Word limit'
                     width='30%'
                     size={'sm'}
                     onChange={(e) => setWordSize(e.target.value)}
                  >
                     {wordSizeOptions.map((wordSize) => (
                        <option key={wordSize.value} value={wordSize.value}>
                           {wordSize.label}
                        </option>
                     ))}
                  </Select>
               </Box>
            </AlertDialogBody>
            <AlertDialogFooter>
               <Button
                  variant={'outline'}
                  colorScheme='red'
                  px={6}
                  size='sm'
                  onClick={onClose}
               >
                  Cancel
               </Button>
               <Button
                  colorScheme='blue'
                  px={6}
                  ml={3}
                  size='sm'
                  variant='outline'
                  isDisabled={!wordSize || !tone || !text}
                  onClick={() => void handleAiSuggestion()}
                  isLoading={loading}
                  spinnerPlacement='start'
               >
                  {buttonMessage.plainGenerate}
               </Button>
            </AlertDialogFooter>
         </AlertDialogContent>
      </AlertDialog>
   );
};

export default DialogBox;
