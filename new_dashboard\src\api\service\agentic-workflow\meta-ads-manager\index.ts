import axios, { AxiosResponse } from 'axios';
import Config from '../../../../config';
import { PromiseAxios } from '../../common';
import dashboardApiAgent from '../../../agent';

export const marcoClient = axios.create({
   baseURL: Config.VITE_AGENTIC_API,
   headers: {
      'Content-Type': 'application/json',
   },
   timeout: 1000 * 120,
});

/** MARCO ENPOINTS **/

export type ChatLevels =
   | 'campaign'
   | 'adset-initial'
   | 'adset-final'
   | 'ad-creative'
   | 'confirm-creative'
   | 'ad'
   | 'completed';

export interface SendPromptPayload {
   session_id: string;
   client_id: string;
   user_query: string;
   chat_level: ChatLevels;
   product_url?: string;
   targeting_analysis?: {
      geo_locations: {
         countries: string[];
      };
      age_range: { [key: string]: string }[];
      audience_interests: { [key: string]: string }[];
      placement_targeting: { [key: string]: string }[];
      audience_behaviors: { [key: string]: string }[];
      regions: { [key: string]: string }[];
      cities: { [key: string]: string }[];
   };
}

export interface SendPromptResponse {
   name?: string;
   objective?: string;
   chat_name?: string;
   product_url?: string;
   chat_response?: string;
   adset_name?: string;
   targeting_analysis?: {
      age: { [key: string]: string }[];
      audience_interests: { [key: string]: string }[];
      placement: { [key: string]: string }[];
      region: { [key: string]: string }[];
   };
   daily_budget?: number;
   options?: string;
   bid_strategy?: string;
   bid_amount?: number;
   billing_event?: string;
   optimization_goal?: string;
   targeting?: string;
}

export interface DataItem {
   [key: string]: string;
}

export const sendPrompt = async (
   payload: SendPromptPayload,
): Promise<AxiosResponse<SendPromptResponse> | null> => {
   try {
      /*if (!payload.client_id || !payload.user_query || !payload.chat_level) {
         throw new Error('Missing required fields in payload');
      }*/

      const response: AxiosResponse<SendPromptResponse> =
         await marcoClient.post<SendPromptResponse>(
            '/meta-ads-manager',
            payload,
         );

      /* if (!response.data) {
         throw new Error('No data received from server');
      }*/

      return response;
   } catch (error) {
      console.error('Error in sendPrompt:', error);

      if (axios.isAxiosError(error) && error.response) {
         console.error('Axios error details:', {
            status: error.response.status,
            data: error.response.data as object,
            message: error.message,
         });

         if (error.response.status === 424) {
            const errorData = error.response.data as { detail: BudgetDetails };

            return {
               status: 424,
               data: {
                  detail: errorData.detail, // full BudgetDetails object
               } as SendPromptResponse,
               statusText: error.response.statusText ?? '',
               headers: error.response.headers ?? {},
               config: error.response.config ?? {},
            };
         }
      }
   }
   return null;
};

/** NODE ENPOINTS **/
export interface AdsetData {
   adset_name: string;
   daily_budget?: number;
   bid_amount: number;
   targeting_analysis?: {
      geo_locations: {
         countries: string[];
      };
      age: { [key: string]: string }[];
      audience_interests: { [key: string]: string }[];
      Placement: { [key: string]: string }[];
      audience_behaviors: { [key: string]: string }[];
      region: { [key: string]: string }[];
   };
   bid_strategy: string;
   billing_event: string;
   optimization_goal: string;
   promoted_object?: {
      pixel_id: string;
      custom_event_type: string;
   };
}

export interface ChatHistory {
   question: string;
   response: string;
   hasTables?: boolean;
   tableData?: TableData;
}

export interface CampaignDetails {
   name: string;
   campaign_id: string;
   objective: string;
   product_url: string;
   buying_type: string;
   campaign_budget_optimization: boolean;
   daily_budget: string | number;
}
export interface ChatDetails {
   chat_id: string;
   chat_name: string;
   chat_history: ChatHistory[];
   chat_level: string;
   campaign_details: CampaignDetails | null;
   adset_details: string;
   ad_creative_details: string;
   ad_details: string;
   created_time: string;
}
export interface CreateCampaignPayload {
   client_id: string;
   name: string;
   objective: string;
   status: 'ACTIVE' | 'PAUSED';
   special_ad_categories: string[];
   buying_type: string;
   campaign_budget_optimization: boolean;
   daily_budget: number;
}

export interface CreateAdsetPayload {
   client_id: string;
   campaign_id: string;
   name: string;
   daily_budget?: number;
   bid_amount: number;
   billing_event: string;
   bid_strategy: string;
   optimization_goal: string;
   targeting: {
      age_min: number;
      age_max: number;
      geo_locations: {
         countries: string[];
      };
   };
   status: 'ACTIVE' | 'PAUSED';
}

export interface AdCreativePayload {
   client_id?: string;
   name: string;
   object_story_spec: {
      page_id: string;
      link_data: {
         link: string;
         message: string;
         name: string;
         description: string;
         picture: string;
         call_to_action: {
            type: string;
            value: {
               link: string;
            };
         };
      };
   };
}

export interface SaveHistoryPayload {
   chat_name: string;
   chat_history: string;
   chat_level: string;
   campaign_details: string;
   adset_details: string | null;
   ad_creative_details: string | null;
   ad_details: string | null;
}
export interface AgeData {
   age?: string;
   age_range?: string;
   spend?: number;
   roas?: number;
   cpp?: number;
   ctr?: number;
   cpc?: number;
   purchase?: number;
}
/**********************************resolving lint error */
export interface CampaignInsights {
   best_performing_audience: string;
   engagement_trends: string;
   optimization_tip: string;
}
export interface Age_range {
   age?: string; // If age can be undefined
   spend: number;
   roas: number;
   cpp: number;
   ctr: number;
   cpc: number;
   purchase: number;
}
export interface AudienceInterest {
   audience_interests: string;
   spend: number;
   roas: number;
   cpp: number;
   ctr: number;
   cpc: number;
   purchase: number;
}
export interface PlacementTargeting {
   Placement: string;
   spend: number;
   roas: number;
   cpp: number;
   ctr: number;
   cpc: number;
   purchase: number;
}
export interface AudienceBehaviors {
   audience_behaviors: { [key: string]: string }[];
}
export interface Regions {
   region: string;
   spend: number;
   roas: number;
   cpp: number;
   ctr: number;
   cpc: number;
   purchase: number;
}
export interface Cities {
   city: { [key: string]: string }[];
}
export type TableData = {
   type: 'table';
   data: {
      age_range: Age_range[];
      audience_interests: AudienceInterest[];
      placement_targeting: PlacementTargeting[];
      audience_behaviors: AudienceBehaviors[];
      regions: Regions[];
      cities: Cities[];
   };
};
export interface AdCreativeData {
   name: string;
   object_story_spec: {
      page_id: string;
      link_data: {
         link: string;
         message: string;
         picture: string;
         call_to_action?: {
            type: string;
            value: {
               link: string;
            };
         };
      };
   };
}
export type ExtendedChatHistory = {
   question: string;
   response: string;
   hasTables?: boolean;
   tableData?: TableData;
   adCreativeData?: AdCreativeData;
};

export interface AdSettings {
   adCreativeId: string;
   adId: string;
   status: 'ACTIVE' | 'PAUSED';
   campaignName: string;
   adsetName: string;
   dailyBudget: string;
   locations: string;
   ageRange: string;
   gender: string;
   optimizationGoal: string;
   adImage: string;
}
export interface AdsetDetails
   extends Omit<ResponseAdSetData, 'targeting_analysis'> {
   targeting_analysis: targeting;
}
export type ExtendedChatHistoryWithPreview = ExtendedChatHistory & {
   id?: string;
   campaignPreview?: CampaignDetails;
   showloader?: boolean;
   AdsetPreview?: AdsetDetails;
   adSettings?: AdSettings;
   campaignInsights?: CampaignInsights | null;
   hasTables?: boolean;
   tableData?: TableData;

   hasBudgetAnalysis?: boolean;
   budgetDetails?: BudgetDetails;
   budgetOptions?: string[];
   showBudopt?: boolean;
};

export interface ChatHistoryResponse {
   chat_id: string;
   chat_name: string;
   chat_history: ExtendedChatHistory[];
   chat_level: string;
   campaign_details: CampaignDetails | null;
   adset_details: string;
   ad_creative_details: string;
   ad_details: string;
   created_time: string;
}
export interface CampaignInsights {
   best_performing_audience: string;
   engagement_trends: string;
   optimization_tip: string;
}
export interface AnalysisDataRow {
   objective_type: string;
   spend: number | string;
   roas: number | string;
   cpp: number | string;
   ctr: number | string;
   cpc: number | string;
   purchase: number | string;
}

export interface FetchAllChatHistoryResponse {
   status: string;
   message: string;
   data: ChatDetails[];
}

export interface CreateCampaignResponse {
   id: string;
}

export interface CreateAdsetResponse {
   id: string;
}

export interface CreateAdResponse {
   id: string;
}

export interface CreateAdCreativeResponse {
   id: string;
}

export interface CreateAdPayload {
   client_id: string;
   name: string;
   adset_id: string;
   creative: { creative_id: string };
   status: 'ACTIVE' | 'PAUSED';
}

export interface CreateAdResponse {
   id: string;
   status: string;
}
export interface FetchCredentialsPayload {
   client_id: string;
}
export interface FetchCredentialsResponse {
   meta_access_token: string;
   page_id: string;
   ad_account_id: string;
}
export interface FetchCredentialsResponseWrapper {
   data: FetchCredentialsResponse;
}
export interface UpdateCredentialsPayload {
   client_id: string;
   meta_access_token: string;
   page_id: string;
   ad_account_id: string;
}
export interface UpdateCredentialsResponse {
   status: string;
   message: string;
}
export interface SummaryPayload {
   campaignDetails: CampaignDetails;
   adsetDetails: AdsetDetails;
   adCreativeDetails: AdCreativeData;
   adDetails: {
      adId: string;
      adName: string;
   };
}
export interface SummaryResponse {
   status: string;
   summary: string;
}

export interface scrapeResponse {
   name: string;
   image: string;
}
export interface creativeImagePayload {
   caption: string;
   description: string;
}
export interface creativeImageResponse {
   imageUrl: string;
   promptUsed: string;
}
/******************************************************************** */
export interface ResponseCampaignData {
   name: string;
   objective: string;
   product_url: string;
   chat_name?: string;
   chat_response?: string;
   buying_type: string;
   campaign_budget_optimization: boolean;
   daily_budget: number;
}

export interface ResponseTargetingAnalysis {
   geo_locations: {
      countries: string[];
   };
   age: { [key: string]: string }[];
   audience_interests: { [key: string]: string }[];
   Placement: { [key: string]: string }[];
   region: { [key: string]: string }[];
   audience_behaviors: { [key: string]: string }[];
}
interface promptObj {
   pixel_id: string;
   custom_event_type: string;
}
export interface ResponseAdSetData {
   adset_id: string;
   adset_name: string;
   daily_budget?: number;
   bid_amount: number;
   targeting_analysis: ResponseTargetingAnalysis;
   options: object;
   chat_response: object;
   bid_strategy: string;
   billing_event: string;
   optimization_goal: string;
   promoted_object?: promptObj;
}
export interface Interest {
   id: string;
   name: string;
}

export interface GeoLocation {
   countries: string[];
}
export interface behavior {
   id: string;
   name: string;
}
export interface targeting {
   age_min: number;
   age_max: number;
   interests: Interest[];
   geo_locations: GeoLocation;
   behaviors: behavior[];
   publisher_platforms: string[];
   facebook_positions: string[];
   genders: number[];
   instagram_positions: string[];
}

export interface ResponseAdsetFinal {
   chat_response: string;
   targeting: targeting;
}
interface ResponseAdCreativeAnalysisItem {
   [key: string]: {
      spend: number;
      roas: number;
      cpp: number;
      ctr: number;
      cpc: number;
      purchase: number;
   };
}
export interface ResponseAdCreativeData {
   ad_creative_name: string;
   caption: string;
   image_url: string;
   description: string;
   ad_creative_analysis: ResponseAdCreativeAnalysisItem;
   recommendation: string;
}

export type MarcoResponse =
   | { channel: 'level1'; data: ResponseCampaignData }
   | { channel: 'level2'; data: ResponseAdSetData }
   | { channel: 'level3'; data: ResponseAdsetFinal }
   | { channel: 'level4'; data: ResponseAdCreativeData };

export interface BudgetDetails {
   budget_analysis: {
      roas?: number;
      spend: number;
      purchase?: number;
      budget_cluster: string;
   }[];
   recommendation: string[];
   message: string;
}

interface Endpoints {
   fetchHistory: () => PromiseAxios<FetchAllChatHistoryResponse>;

   saveHistory: (payload: SaveHistoryPayload) => PromiseAxios<ChatDetails>;

   //getAzureImageUri: (payload: string) => PromiseAxios<string>;

   // Auto Agent History Endpoints
   fetchAllAutoAgentHistory: (
      payload: FetchAllAutoAgentHistoryPayload,
   ) => PromiseAxios<MetaAdsAutoAgentChat[]>;

   fetchAutoAgentHistoryBySession: (
      payload: FetchAutoAgentHistoryBySessionPayload,
   ) => PromiseAxios<MetaAdsAutoAgentChat[]>;

   saveAutoAgentHistory: (
      payload: SaveAutoAgentHistoryPayload,
   ) => PromiseAxios<void>;

   createCampaign: (
      payload: CreateCampaignPayload,
   ) => PromiseAxios<CreateCampaignResponse>;

   createAdset: (
      payload: CreateAdsetPayload,
   ) => PromiseAxios<CreateAdsetResponse>;

   createAdCreative: (
      payload: AdCreativePayload,
   ) => PromiseAxios<CreateAdCreativeResponse>;

   createAd: (payload: CreateAdPayload) => PromiseAxios<CreateAdResponse>;

   fetchCredentials: (
      payload: FetchCredentialsPayload,
   ) => PromiseAxios<FetchCredentialsResponseWrapper>;

   updateCredentials: (
      payload: UpdateCredentialsPayload,
   ) => PromiseAxios<UpdateCredentialsResponse>;

   getSummary: (payload: SummaryPayload) => PromiseAxios<SummaryResponse>;

   generateCreativeImage: (
      payload: creativeImagePayload,
   ) => PromiseAxios<creativeImageResponse>;
   // required for future usage
   //webScrapeForImage: (payload: string) => PromiseAxios<scrapeResponse[]>;
}

const metaAdsManagerEndPoints: Endpoints = {
   fetchHistory: () => dashboardApiAgent.get('/meta-ads-manager/chat-history'),

   saveHistory: (payload) =>
      dashboardApiAgent.post('/meta-ads-manager/chat-history', payload),

   fetchAllAutoAgentHistory: async (
      payload: FetchAllAutoAgentHistoryPayload,
   ) => {
      return await dashboardApiAgent.get(
         '/meta-ads-manager/auto-agent-history',
         {
            params: payload,
         },
      );
   },

   fetchAutoAgentHistoryBySession: async (
      payload: FetchAutoAgentHistoryBySessionPayload,
   ) => {
      return await dashboardApiAgent.get(
         '/meta-ads-manager/auto-agent-history/session',
         {
            params: payload,
         },
      );
   },

   saveAutoAgentHistory: async (payload: SaveAutoAgentHistoryPayload) => {
      return await dashboardApiAgent.post(
         '/meta-ads-manager/auto-agent-history',
         payload,
      );
   },

   // getAzureImageUri: async (image_url: string) => {
   //    return await dashboardApiAgent.post(
   //       '/meta-ads-manager/uploadChatGptImage',
   //       { image_url },
   //    );
   // },
   createCampaign: async (payload: CreateCampaignPayload) => {
      return dashboardApiAgent.post('/meta-ads-manager/campaigns', payload);
   },

   createAdset: async (payload: CreateAdsetPayload) => {
      return dashboardApiAgent.post('/meta-ads-manager/adsets', payload);
   },

   createAdCreative: async (payload: AdCreativePayload) => {
      return await dashboardApiAgent.post(
         '/meta-ads-manager/adcreative',
         payload,
      );
   },

   createAd: async (payload: CreateAdPayload) => {
      return await dashboardApiAgent.post('/meta-ads-manager/ad', payload);
   },

   fetchCredentials: async (payload: FetchCredentialsPayload) => {
      return await dashboardApiAgent.get(
         `/settings/meta-ads-credentials/${payload.client_id}`,
      );
   },
   updateCredentials: async (payload: UpdateCredentialsPayload) => {
      return dashboardApiAgent.post('/settings/meta-ads-credentials', payload);
   },
   getSummary: async (payload: SummaryPayload) => {
      return dashboardApiAgent.post('/meta-ads-manager/summarization', payload);
   },

   generateCreativeImage: async (payload: creativeImagePayload) => {
      return dashboardApiAgent.get('/meta-ads-manager/get-creative', {
         params: payload,
      });
   },
   //required for future usage
   /*webScrapeForImage: async (payload: string) => {
      return dashboardApiAgent.get('/meta-ads-manager/scraping', {
         params: { url: payload },
      });
   },*/
};

export default metaAdsManagerEndPoints;

// Auto Agent History Interfaces
export interface MetaAdsAutoAgentChat {
   chat_id: string;
   session_id: string;
   user_query: string;
   final_response: string;
   created_at: string;
   updated_at: string;
   campaign_details?: CampaignDetails;
   adset_details?: AdsetDetails;
   adset_data?: AdsetData;
   ad_creative_id?: string;
   ad_id?: string;
   summary_content?: string;
   streaming_messages?: StreamingMessage[];
   step_statuses?: AutomationStep[];
}

export interface FetchAllAutoAgentHistoryPayload {
   client_id: string;
   user_id: string;
}

export interface FetchAutoAgentHistoryBySessionPayload {
   client_id: string;
   user_id: string;
   session_id: string;
}

export interface SaveAutoAgentHistoryPayload {
   client_id: string;
   user_id: string;
   session_id: string;
   chat_id: string;
   user_query: string;
   final_response: string;
   campaign_details?: CampaignDetails;
   adset_details?: AdsetDetails;
   adset_data?: AdsetData;
   ad_creative_id?: string;
   ad_id?: string;
   summary_content?: string;
   streaming_messages?: StreamingMessage[];
   step_statuses?: AutomationStep[];
   response_like_dislike?: string;
}

export interface AutomationStep {
   step: number;
   name: string;
   status: 'pending' | 'in-progress' | 'completed' | 'error';
   chatLevel: ChatLevels;
}

export interface StreamingMessage {
   id: string;
   type:
      | 'campaign'
      | 'adset-analysis'
      | 'adset-creation'
      | 'ad-creative'
      | 'ad';
   title: string;
   content: string;
   isStreaming: boolean;
   timestamp: string;
   data?: unknown;
   tableData?: TableSection[];
   imageUrl?: string;
   isComplete: boolean;
}

export interface TableSection {
   sectionTitle: string;
   rows: { [key: string]: string }[];
}
