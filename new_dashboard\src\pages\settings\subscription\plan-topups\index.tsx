import { plansTab } from '../constants';
import TabSection from '@/components/tabSection';
import PageHeader from '@/components/pageHeader';

const PlanTopups = () => {
   const breadcrumbs = [
      { label: 'Settings', onClick: () => console.log('Go to Settings') },
      { label: 'Plans & Topups', onClick: () => console.log('Go to Plans') },
   ];

   return (
      <section className='plans p-8 text-jet bg-porcelain w-full h-auto'>
         <PageHeader title='Manage Subscription' breadcrumbs={breadcrumbs} />

         <div className='tabsestion mt-10'>
            <TabSection tabObject={plansTab} />
         </div>
      </section>
   );
};

export default PlanTopups;
