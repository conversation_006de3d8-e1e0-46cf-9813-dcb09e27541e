import React, { useState, useRef, ChangeEvent, ElementRef } from 'react'; // Import React
import {
   Box,
   Button,
   Stack,
   Icon,
   InputGroup,
   Input,
   useToast,
   IconButton,
   Image,
   Spinner,
   useColorMode,
} from '@chakra-ui/react';
import Picker, { EmojiClickData } from 'emoji-picker-react';

import { CiImageOn } from 'react-icons/ci';
import { CloseIcon } from '@chakra-ui/icons';
import { BsEmojiSmile } from 'react-icons/bs';
import { useDispatch } from 'react-redux';
import AIAsset from '../../assets/image/aiicon.svg';
import DialogBox from '../dialogbox/dialog';
import { setAiText } from '../../store/reducer/configReducer';

import { useAppSelector } from '../../store/store';
import useClickOutside from '../../hooks/click-outside';
import {
   removeMultiMedia,
   removeRawFile,
   setMultiMedia,
   setRawFiles,
} from '../../store/reducer/user-details-reducer';
import socialWatchEndpoints from '../../api/service/social-watch/apis';

import './TabContent.scss';
import { mediaPlaceHolder } from '../../utils/strings/socialwatch-strings';

interface ImageWithDeleteIconProps {
   src: string;
   alt: string;
   onDelete?: () => void;
   preview?: boolean;
}

export const ImageWithDeleteIcon = ({
   src,
   alt,
   onDelete,
   preview = false,
}: ImageWithDeleteIconProps) => {
   const { selectedTab, multiMedia } = useAppSelector((state) => state.media);
   const isLoading = !multiMedia[selectedTab]?.some((x) => x.rawFile == src);
   return (
      <Box
         position='relative'
         display='inline-block'
         marginRight={!preview ? 2 : ''}
         width={!preview ? 20 : '100%'}
         height={!preview ? 20 : '50vh'}
      >
         {isLoading && !preview && (
            <div className='img-load'>
               <Spinner
                  thickness='3px'
                  speed='0.65s'
                  emptyColor='gray.200'
                  color='blue.500'
                  size={'lg'}
               />
            </div>
         )}
         <Image
            src={src}
            alt={alt}
            borderRadius='md'
            height={'100%'}
            width={'100%'}
         />
         {!preview && (
            <IconButton
               position='absolute'
               top='0'
               right={0}
               minWidth={5}
               height={5}
               // m="1"
               colorScheme='red'
               aria-label='Delete image'
               icon={<CloseIcon fontSize={10} />}
               size='sm'
               onClick={onDelete}
            />
         )}
      </Box>
   );
};

const TabContent: React.FC = () => {
   const [isOpen, setIsOpen] = useState<boolean>(false);
   const [openEmoji, setOpenEmoji] = useState(false);

   const dynamicSocialMedia = useAppSelector((state) => state.media);
   const { connections } = useAppSelector((state) => state.integration);
   const { selectedTab } = useAppSelector((state) => state.media);
   const pickerRef = useRef<ElementRef<'div'>>(null);

   const { twitter, linkedin } = connections;

   const dispatch = useDispatch();
   const onClose = () => setIsOpen(false);
   const onOpen = () => setIsOpen(true);
   const toast = useToast();
   const state = useAppSelector((state) => state.config);
   const { colorMode } = useColorMode();

   useClickOutside(pickerRef, () => setOpenEmoji(false));

   const handleFileUpload = async (
      e: ChangeEvent<HTMLInputElement>,
      socialMedia: string,
   ): Promise<void> => {
      const files = e.target.files;

      if (!files || files.length === 0) return;

      const readFile = (file: File): Promise<string> => {
         return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result as string);
            reader.onerror = () => reject(new Error('File reading error'));
            reader.readAsDataURL(file);
         });
      };

      const uploadFile = async (
         result: string,
         formData: FormData,
         platform: string,
      ): Promise<void> => {
         try {
            let uploadResponse: {
               data: Record<string, string>;
            } | null = null;
            if (platform === 'twitter') {
               uploadResponse =
                  await socialWatchEndpoints.uploadImageToTwitter(formData);
            } else if (platform === 'linkedin') {
               uploadResponse =
                  await socialWatchEndpoints.uploadImageToLinkedin(formData);
            }

            const {
               data: { uri },
            } = await socialWatchEndpoints.uploadMediaToAzure(formData);
            const mediaIdKey = platform === 'twitter' ? 'mediaId' : 'assetId';

            if (!uploadResponse) {
               toast({
                  title: 'Upload error',
                  description: `Media upload failed`,
                  status: 'error',
                  duration: 3000,
                  isClosable: true,
               });
               return;
            }

            dispatch(
               setMultiMedia({
                  media_id: uploadResponse.data[mediaIdKey],
                  image_link: uri,
                  rawFile: result,
                  socialMedia,
               }),
            );
         } catch (error) {
            console.error('Upload error:', error);
            throw error;
         }
      };

      const handleFile = async (file: File): Promise<void> => {
         let fileType: string | undefined;
         const result = await readFile(file);

         if (file.type.includes('image')) {
            dispatch(setRawFiles({ result, socialMedia }));
            fileType = 'image';
         } else if (file.type.includes('video')) {
            fileType = 'video';
         }

         const formData = new FormData();
         const platform = selectedTab;
         try {
            if (platform === 'twitter' && twitter) {
               const { oauthToken, oauthTokenSecret } = twitter;
               formData.append('media', file);
               formData.append('oauthToken', oauthToken);
               formData.append('oauthTokenSecret', oauthTokenSecret);
            } else if (platform === 'linkedin' && linkedin) {
               const { user } = linkedin;
               formData.append('media', file);
               formData.append('token', user.access_token);
               formData.append('userId', user.userId);
            }

            await uploadFile(result, formData, platform);
         } catch (error) {
            toast({
               title: 'Upload error',
               description: `${fileType} upload failed`,
               status: 'error',
               duration: 3000,
               isClosable: true,
            });
            dispatch(removeRawFile({ file: result, socialMedia: platform }));
         }
      };

      await Promise.all(Array.from(files).map(handleFile));
   };

   const renderPreview = (socialMedia: string) => {
      return (
         <div style={{ display: 'flex', gap: 7, alignItems: 'center' }}>
            {dynamicSocialMedia.rawFiles[socialMedia] &&
               dynamicSocialMedia.rawFiles[socialMedia].map((file) =>
                  file.startsWith('data:video/') ? (
                     <video width={106} controls>
                        <source src={file} type='video/mp4' />
                     </video>
                  ) : (
                     <ImageWithDeleteIcon
                        key={file}
                        src={file}
                        alt='Preview'
                        onDelete={() => onDelete(file, socialMedia)}
                     />
                  ),
               )}
         </div>
      );

      return;
   };

   const onDelete = (file: string, socialMedia: string) => {
      dispatch(removeRawFile({ file, socialMedia }));
      dispatch(removeMultiMedia({ file, socialMedia }));
   };
   const handleChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
      dispatch(setAiText({ media: selectedTab, text: e.target.value }));
   };

   function handleEmojiClick(emojiData: EmojiClickData) {
      const { emoji } = emojiData;
      setOpenEmoji(false);
      dispatch(
         setAiText({
            media: selectedTab,
            text: (state.aiText[selectedTab] || '') + emoji,
         }),
      );
   }
   const onInputClick = (
      event: React.MouseEvent<HTMLInputElement, MouseEvent>,
   ) => {
      const element = event.target as HTMLInputElement;
      element.value = '';
   };
   return (
      <>
         <DialogBox isOpen={isOpen} onClose={onClose} />
         <Box
            bg={colorMode === 'dark' ? 'var(--background)' : 'white'}
            color={colorMode === 'dark' ? 'white' : 'black'}
         >
            <Box borderRadius='4px'>
               <textarea
                  className='text-area'
                  placeholder={
                     mediaPlaceHolder[selectedTab] || 'content goes here....'
                  }
                  value={state.aiText[selectedTab] || ''}
                  onChange={handleChange}
               />
               {renderPreview(selectedTab)}
            </Box>
            <Box mt={4}>
               <Stack direction='row' spacing={2}>
                  <Icon
                     as={BsEmojiSmile}
                     w={5}
                     h={5}
                     onClick={() => setOpenEmoji(true)}
                  />
                  <InputGroup w={5}>
                     <Input
                        type='file'
                        id='file'
                        multiple
                        style={{ display: 'none' }}
                        onChange={(e) => void handleFileUpload(e, selectedTab)}
                        onClick={onInputClick}
                     />
                     <label htmlFor='file'>
                        <Icon as={CiImageOn} w={5} h={5} />
                     </label>
                  </InputGroup>
               </Stack>
               <div style={{ width: '48%' }} ref={pickerRef}>
                  <Picker open={openEmoji} onEmojiClick={handleEmojiClick} />
               </div>

               <Stack direction='row' spacing={0} justifyContent='flex-end'>
                  <Button
                     id='aiAssist'
                     size='xs'
                     style={{
                        color: '#337CDF',
                        textAlign: 'center',
                        textDecoration: 'none',
                        cursor: 'pointer',
                        borderRadius: '4px',
                        border: '1px solid #337CDF',
                        padding: '16px 22px 16px 12px',
                     }}
                     variant='outline'
                     leftIcon={
                        <img src={AIAsset} alt='AI' width={20} height={20} />
                     }
                     onClick={onOpen}
                  >
                     {sessionStorage.getItem('aibutton') || 'Regenerate'}
                  </Button>
               </Stack>
            </Box>
         </Box>
      </>
   );
};

export default TabContent;
