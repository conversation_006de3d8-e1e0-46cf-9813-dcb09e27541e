import { AdsetDetails } from '../../../../api/service/agentic-workflow/meta-ads-manager';
import {
   Box,
   Flex,
   Text,
   Heading,
   VStack,
   HStack,
   Icon,
} from '@chakra-ui/react';
import {
   FaTag,
   FaBookmark,
   FaRupeeSign,
   FaChartLine,
   FaCreditCard,
   FaBullseye,
   FaUsers,
   FaGlobe,
   FaFacebook,
   FaInstagram,
   FaShareAlt,
   FaUserTag,
} from 'react-icons/fa';
const formatLabel = (label: string) => {
   return label.replace(/_/g, ' ').replace(/\b\w/g, (c) => c.toUpperCase());
};
export const AdsetPreviewCard = ({
   AdsetDetails,
}: {
   AdsetDetails: AdsetDetails;
}) => {
   const ageMin = AdsetDetails?.targeting_analysis?.age_min ?? '-';
   const ageMax = AdsetDetails?.targeting_analysis?.age_max ?? '-';
   const interests = AdsetDetails?.targeting_analysis?.interests ?? [];
   const behaviors = AdsetDetails.targeting_analysis.behaviors ?? [];
   const countries =
      AdsetDetails?.targeting_analysis?.geo_locations?.countries ?? [];
   const instagramPositions =
      AdsetDetails?.targeting_analysis?.instagram_positions ?? [];
   const facebookPositions =
      AdsetDetails?.targeting_analysis?.facebook_positions ?? [];
   console.log('debugginnnnnnn!!!!!!!!!!!!!!!!', behaviors);
   return (
      <Box
         p={4}
         border='1px solid #e2e8f0'
         borderRadius='md'
         mt={4}
         width='100%'
         bg='gray.50'
      >
         <Box borderBottomWidth={1} pb={4} mb={6}>
            <HStack spacing={3}>
               <Icon as={FaTag} w={7} h={7} color='purple.600' />
               <Heading size='md' color='gray.800'>
                  AdSet Details
               </Heading>
            </HStack>
         </Box>

         <Flex direction={{ base: 'column', md: 'row' }} gap={6}>
            <VStack flex={1} spacing={6}>
               <VStack spacing={4} w='full'>
                  <HStack p={4} bg='purple.50' borderRadius='lg' w='full'>
                     <Icon as={FaBookmark} w={6} h={6} color='purple.600' />
                     <Box>
                        <Text fontSize='sm' color='gray.600'>
                           AdSet Name
                        </Text>
                        <Text fontWeight='semibold' color='gray.800'>
                           {AdsetDetails?.adset_name ?? 'N/A'}
                        </Text>
                     </Box>
                  </HStack>
                  <HStack p={4} bg='blue.50' borderRadius='lg' w='full'>
                     <Icon as={FaRupeeSign} w={6} h={6} color='blue.600' />
                     <Box>
                        <Text fontSize='sm' color='gray.600'>
                           Bid Amount
                        </Text>
                        <Text fontWeight='semibold' color='gray.800'>
                           ₹
                           {AdsetDetails.bid_amount
                              ? `  ${Number(AdsetDetails.bid_amount) / 100}`
                              : 'N/A'}
                        </Text>
                     </Box>
                  </HStack>
               </VStack>

               <VStack spacing={4} w='full'>
                  <HStack p={4} bg='green.50' borderRadius='lg' w='full'>
                     <Icon as={FaChartLine} w={6} h={6} color='green.600' />
                     <Box>
                        <Text fontSize='sm' color='gray.600'>
                           Bid Strategy
                        </Text>
                        <Text fontWeight='semibold' color='gray.800'>
                           {formatLabel(AdsetDetails?.bid_strategy) ?? 'N/A'}
                        </Text>
                     </Box>
                  </HStack>
                  <HStack p={4} bg='yellow.50' borderRadius='lg' w='full'>
                     <Icon as={FaCreditCard} w={6} h={6} color='yellow.600' />
                     <Box>
                        <Text fontSize='sm' color='gray.600'>
                           Billing Event
                        </Text>
                        <Text fontWeight='semibold' color='gray.800'>
                           {formatLabel(AdsetDetails?.billing_event) ?? 'N/A'}
                        </Text>
                     </Box>
                  </HStack>
                  <HStack p={4} bg='indigo.50' borderRadius='lg' w='full'>
                     <Icon as={FaBullseye} w={6} h={6} color='indigo.600' />
                     <Box>
                        <Text fontSize='sm' color='gray.600'>
                           Optimization Goal
                        </Text>
                        <Text fontWeight='semibold' color='gray.800'>
                           {formatLabel(AdsetDetails?.optimization_goal) ??
                              'N/A'}
                        </Text>
                     </Box>
                  </HStack>
               </VStack>
            </VStack>

            <VStack flex={1} spacing={6}>
               <Box p={4} bg='gray.50' borderRadius='lg' w='full'>
                  <HStack>
                     <Icon as={FaUsers} w={6} h={6} color='gray.600' />
                     <Text fontWeight='semibold' color='gray.800'>
                        Demographics
                     </Text>
                  </HStack>
                  <Box mt={3} ml={9}>
                     <Text fontSize='sm' color='gray.700'>
                        Age Range: {ageMin} - {ageMax}
                     </Text>
                     <Text
                        fontSize='sm'
                        fontWeight='medium'
                        color='gray.700'
                        mt={2}
                     >
                        Interests:
                     </Text>
                     <VStack align='start' spacing={1} pl={2}>
                        {interests.length > 0 ? (
                           interests.map((interest) => (
                              <Text
                                 key={interest.id}
                                 fontSize='sm'
                                 color='gray.700'
                              >
                                 • {interest.name}
                              </Text>
                           ))
                        ) : (
                           <Text fontSize='sm' color='gray.500'>
                              No interests available
                           </Text>
                        )}
                     </VStack>
                     <HStack mt={3}>
                        <Icon as={FaGlobe} w={5} h={5} color='emerald.600' />
                        <Text fontSize='sm' color='gray.700'>
                           Locations:{' '}
                           {countries.length > 0
                              ? countries.join(', ')
                              : 'No location data'}
                        </Text>
                     </HStack>
                  </Box>
               </Box>

               <Box p={4} bg='gray.50' borderRadius='lg' w='full'>
                  <HStack>
                     <Icon as={FaShareAlt} w={6} h={6} color='cyan.600' />
                     <Text fontWeight='semibold' color='gray.800'>
                        Placements
                     </Text>
                  </HStack>
                  <Box mt={3} ml={9}>
                     <HStack>
                        <Icon as={FaFacebook} w={5} h={5} color='blue.600' />
                        <Text fontSize='sm' color='gray.700'>
                           Facebook:{' '}
                           {facebookPositions.length > 0
                              ? facebookPositions.join(', ')
                              : 'No placements'}
                        </Text>
                     </HStack>
                     <HStack>
                        <Icon as={FaInstagram} w={5} h={5} color='pink.600' />
                        <Text fontSize='sm' color='gray.700' mt={2}>
                           Instagram:{' '}
                           {instagramPositions.length > 0
                              ? instagramPositions.join(', ')
                              : 'No placements'}
                        </Text>
                     </HStack>
                  </Box>

                  <HStack>
                     <Icon
                        as={FaUserTag}
                        w={8}
                        h={6}
                        color='green.600'
                        mt={4}
                     />
                     <Text fontWeight='semibold' color='gray.800' mt={4}>
                        Audience Behaviours
                     </Text>
                  </HStack>
                  <HStack align='start' spacing={4}>
                     <VStack align='start' spacing={2} mt={4} ml={10}>
                        {behaviors.length > 0 ? (
                           behaviors.map((behave) => (
                              <Text
                                 key={behave.id}
                                 fontSize='sm'
                                 color='gray.700'
                              >
                                 • {behave.name}
                              </Text>
                           ))
                        ) : (
                           <Text fontSize='sm' color='gray.500'>
                              No behaviors available
                           </Text>
                        )}
                     </VStack>
                  </HStack>
               </Box>
            </VStack>
         </Flex>
      </Box>
   );
};
