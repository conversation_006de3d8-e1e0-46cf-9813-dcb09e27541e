import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { AllUserDetails } from '../../api/service/users';

interface InitialState {
   allUsers: AllUserDetails[];
   selectedUser: AllUserDetails | null;
}

const initialState: InitialState = {
   allUsers: [],
   selectedUser: null,
};

const userManagementSlice = createSlice({
   name: 'userManagement',
   initialState,
   reducers: {
      setAllUsers: (state, action: PayloadAction<AllUserDetails[]>) => {
         state.allUsers = action.payload;
      },
      setSelectedUser: (
         state,
         action: PayloadAction<AllUserDetails | null>,
      ) => {
         state.selectedUser = action.payload;
      },
   },
});

export const { setAllUsers, setSelectedUser } = userManagementSlice.actions;

export default userManagementSlice.reducer;
