import { Link } from 'react-router-dom';
import './pulse-tabs.scss';

interface Tab {
   id: string;
   to: string;
   className: string;
   onClick?: () => void;
   title: string;
}

interface Props {
   tabs: Tab[];
}

function PulseTabs({ tabs }: Props) {
   return (
      <div className='overview'>
         {tabs.map(({ id, title, className, to, onClick }) => (
            <Link
               key={id}
               id={id}
               to={to}
               className={className}
               onClick={onClick}
            >
               {title}
            </Link>
         ))}
      </div>
   );
}

export default PulseTabs;
