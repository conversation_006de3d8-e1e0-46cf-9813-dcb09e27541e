@use '../../../sass/variable.scss';
.rdrStaticRangeLabel {
   padding: 4px;
   font-weight: 600;
}
.rdrDefinedRangesWrapper {
   width: 150px;
   padding: 5px;
}
.rdrMonthAndYearWrapper {
   height: 30px;
}
.rdrCalendarWrapper {
   font-size: 11px;
   .rdrDayNumber,
   .rdrWeekDay,
   .rdrMonthName {
      font-weight: 700;
   }
   .rdrMonthName {
      padding: 5px;
   }
}
.rdrMonthAndYearPickers {
   padding-top: 0;
   select {
      padding: 5px 20px 5px 5px;
   }
}
.rdrDateDisplay {
   margin: 5px;
}
.date-select {
   & .apply {
      background-color: #437eeb;
      color: white;
      border: 1px solid #437eeb;
      border-radius: 5px;
      position: revert-layer;
      width: fit-content;
      &:hover {
         background-color: white;
         color: #437eeb;
      }
   }
   & .btn {
      height: 32px;
      font-size: 13px;
      &:active {
         scale: 0.95;
      }
   }
   // Light Theme (Default)
   .rdrCalendarWrapper {
      background-color: #ffffff !important;
      color: #1a202c !important;
      font-size: 11px;

      .rdrDayNumber,
      .rdrWeekDay,
      .rdrMonthName {
         font-weight: 700;
         color: #1a202c;
      }
   }

   // Dark Theme
   [data-theme='dark'] .rdrCalendarWrapper {
      background-color: #1a202c !important;
      color: #ffffff !important;

      .rdrDayNumber,
      .rdrWeekDay,
      .rdrMonthName {
         color: #ffffff !important;
      }
   }

   .rdrMonthAndYearWrapper {
      background-color: #ffffff !important;
      height: 30px;
   }

   // Dark Theme
   [data-theme='dark'] .rdrMonthAndYearWrapper {
      background-color: #1a202c !important;
   }

   .rdrMonthAndYearPickers select {
      background-color: #ffffff !important;
      color: #1a202c !important;
   }

   // Dark Theme
   [data-theme='dark'] .rdrMonthAndYearPickers select {
      background-color: #2d3748 !important;
      color: #cbd5e0 !important;
   }

   .rdrStaticRange {
      background-color: #ffffff !important;
      color: #1a202c !important;

      [data-theme='dark'] & {
         background-color: #2d3748 !important;
         color: #ffffff !important;
      }

      &:hover {
         background-color: #edf2f7 !important;
         color: #437eeb !important;

         // [data-theme='dark'] & {
         //    background-color: $background_surface !important;
         // }
      }

      &.rdrStaticRangeSelected {
         background-color: #edf2f7 !important;
         color: #437eeb !important;
         // [data-theme='dark'] & {
         //    background-color: $background_surface !important;
         // }
      }
   }
   // .rdrStaticRange:hover .rdrStaticRangeLabel,
   // .rdrStaticRange:focus .rdrStaticRangeLabel {
   //    [data-theme='dark'] & {
   //       background-color: $background_surface !important;
   //       color: #ffffff !important;
   //    }
   // }
   .rdrDefinedRangesWrapper {
      background-color: #ffffff !important;
      border-color: #e2e8f0;

      [data-theme='dark'] & {
         color: #cbd5e0 !important;
         background-color: #2d3748 !important;
      }
   }

   .rdrDateDisplay {
      margin: 5px;

      input {
         background-color: #ffffff !important;
         color: #1a202c;

         [data-theme='dark'] & {
            background-color: #2d3748 !important;
            color: #cbd5e0 !important;
         }
      }
   }

   .rdrDayNumber span {
      color: #1a202c;

      [data-theme='dark'] & {
         color: #ffffff !important;
      }
   }

   .rdrDayPassive .rdrDayNumber span {
      color: #718096 !important;

      [data-theme='dark'] & {
         color: #a0aec0 !important;
      }
   }

   // Dark Theme
}
.date-label {
   span {
      padding-top: 3px;
   }
}
.kpi-date-filter {
   & .btn {
      height: 32px;
      font-size: 13px;
      &:active {
         scale: 0.95;
      }
   }

   select {
      background-color: #2d3748;
      color: #cbd5e0;
      border-color: #4a5568;

      &:hover {
         border-color: #63b3ed;
      }

      &:focus {
         border-color: #4299e1;
         box-shadow: 0 0 0 1px #4299e1;
      }

      option {
         background-color: #2d3748;
         color: #cbd5e0;

         &:hover {
            background-color: #4a5568;
         }

         &:disabled {
            color: #718096;
         }
      }
   }
}

// Range Select specific styles
.range-select-light {
   background-color: white !important;
   color: #1a202c !important;
   border-color: #e2e8f0 !important;

   option {
      background-color: white !important;
      color: #1a202c !important;
   }

   &:hover {
      border-color: #4299e1 !important;
   }
}

// Override default select styles
select {
   &.range-select-light {
      option {
         background-color: white !important;
         color: #1a202c !important;
      }
   }
}

.rdrSelected,
.rdrInRange,
.rdrStartEdge,
.rdrEndEdge {
   [data-theme='dark'] & {
      background: rgb(61, 145, 255);
      // color: #ffffff !important;
   }
}
// Additional specificity for select options
.chakra-select__wrapper {
   select {
      &.range-select-light {
         option {
            background-color: white !important;
            color: #1a202c !important;
         }
      }
   }
}

// Force theme colors on dropdown
select:-internal-list-box {
   option {
      &.range-select-light {
         background-color: white !important;
         color: #1a202c !important;
      }
   }
}
// .date-select .rdrCalendarWrapper,
// .rdrDateDisplayWrapper,
// .date-select .rdrMonthAndYearWrapper {
//    [data-theme='dark'] & {
//       background-color: #2d3748 !important;
//       color: $text_color !important;
//    }
// }
// .rdrNextPrevButton {
//    [data-theme='dark'] & {
//       background-color: $background_light !important;
//       color: $text_color !important;
//    }
//    &:hover {
//       [data-theme='dark'] & {
//          background-color: $background_surface !important;
//       }
//    }
// }
// .rdrPprevButton i {
//    [data-theme='dark'] & {
//       border-color: transparent $text_color transparent transparent;
//    }
// }

// .rdrNextButton i {
//    [data-theme='dark'] & {
//       border-color: transparent transparent transparent $text_color;
//    }
// }
// .date-select .rdrMonthAndYearPickers select {
//    [data-theme='dark'] & {
//       background-color: #2d3748 !important;
//       color: $text_color !important;
//    }
// }

// .date-select .rdrCalendarWrapper .rdrDayNumber,
// .date-select .rdrCalendarWrapper .rdrWeekDay,
// .date-select .rdrCalendarWrapper .rdrMonthName {
//    [data-theme='dark'] & {
//       color: $text_color;
//    }
// }
// .rdrDayDisabled {
//    [data-theme='dark'] & {
//       background-color: #2d3748 !important;
//       color: $text_color !important;
//    }
// }
