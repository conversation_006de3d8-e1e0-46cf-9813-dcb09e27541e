import axios, { AxiosResponse } from 'axios';
import Config from '../../../../config';
import dashboardApiAgent from '../../../agent';

export const marcoClient = axios.create({
   baseURL: Config.VITE_AGENTIC_API,
   headers: {
      'Content-Type': 'application/json',
   },
   timeout: 1000 * 60,
});

/** MARCO ENPOINTS **/

export interface SendAlertPromptPayload {
   client_id: string;
   chat: string;
   mode: 'prompt' | 'json';
}

export interface SendAlertOptions {
   campaigns: {
      facebookads: {
         id: string;
         name: string;
      }[];
      googleads: {
         id: string;
         name: string;
      }[];
   };
   channels: string[];
   dashboard_kpis: {
      amazon_selling_partner: string[];
      amazon_ads: string[];
      facebookads: string[];
      googleads: string[];
      store: string[];
      web: string[];
   };
   pulse_kpis: {
      amazon_selling_partner: string[];
      amazon_ads: string[];
      facebookads: string[];
      googleads: string[];
      store: string[];
      web: string[];
   };
   time: string;
}

export interface SendAlertResponse {
   status: 'success' | 'failure' | 'missing';
   message: string;
   response: string;
   options: SendAlertOptions;
   missing_fields?: string[];
   data: {
      title: string;
      description: string;
      instruction: string;
      channel: string;
      kpi: string;
      trend: string;
      value: string;
      value_type: string;
      campaign: {
         id: string;
         name: string;
      }[];
      comparison: string;
      comparison_type: string;
   };
}

export const sendAlertPrompt = async (
   payload: SendAlertPromptPayload,
): Promise<SendAlertResponse | null> => {
   try {
      const response: AxiosResponse<SendAlertResponse> = await marcoClient.post(
         '/alerting-agent',
         payload,
      );
      return response.data;
   } catch (err) {
      const error = err as Error;
      console.error('Error creating alert:', error.message);
      return null;
   }
};

/** NEWDASHBOARD_API ENDPOINTS **/

/** MISCELLANEOUS **/
export interface AlertingAgentChatMetaData {
   alert_id: string;
   alert_name: string;
   alert_description: string;
   options: SendAlertOptions;
   alert_setup_status: 'success' | 'missing';
   meta_data: SendAlertResponse;
}

export interface AlertingAgentChat {
   client_id: string;
   user_id: string;
   session_id: string;
   chat_id: string;
   user_query: string;
   response_status: 'success' | 'error' | 'pending';
   final_response: string;
   session_summary: string;
   response_time: number;
   created_at: string;
   updated_at: string;
}

export interface AlertingAgentAlert {
   alert_id: number;
   client_id: string;
   user_id: string;
   chat_id: string;
   session_id: string;
   alert_name: string;
   alert_description: string;
   recipients: string;
   channel: string;
   campaigns: AlertCampaign[];
   kpi: string;
   trend: string;
   value: string;
   value_type: string;
   comparison: string;
   comparison_type: string;
   alert_status: string;
   alert_time: string;
   user_timezone: string;
   emails_triggered: number;
   timestamps_when_triggered: string;
   alert_instruction: string;
   alert_timeframe: string;
   created_at: string;
   updated_at: string;
}

export interface FeatureUsage {
   client_id: string;
   user_id: string;
   feature_name: string;
   feature_type: string;
   is_enabled: boolean;
   no_of_calls: number;
   free_limit_expired: boolean;
   last_call_made_at: string;
}

export interface AlertCampaign {
   id: string;
   name: string;
}

/** PAYLOADS **/

export interface FetchAllSessionsByUserIDPayload {
   client_id: string;
   user_id: string;
   page: number;
}

export interface FetchHistoryBySessionIDPayload {
   client_id: string;
   user_id: string;
   session_id: string;
}

export interface AddChatToSessionHistoryPayload {
   client_id: string;
   user_id: string;
   session_id: string;
   chat_id: string;
   user_query: string;
   response_status: 'success' | 'error' | 'pending';
   final_response: string;
   session_summary: string;
   response_time: number;
}

export interface FetchChatByChatIdPayload {
   client_id: string;
   user_id: string;
   session_id: string;
   chat_id: string;
}

export interface FetchFeatureUsagePayload {
   client_id: string;
   user_id: string;
   feature_name: string;
   feature_type: string;
}

export interface TrackFeatureUsagePayload {
   client_id: string;
   user_id: string;
   feature_name: string;
   feature_type: string;
}

export interface FetchAlertsBySessionIDPayload {
   client_id: string;
   user_id: string;
   session_id: string;
}

export interface FetchAllAlertsPayload {
   client_id: string;
   user_id: string;
}

export interface CreateAlertPayload {
   client_id: string;
   user_id: string;
   chat_id: string;
   session_id: string;
   alert_name: string;
   alert_description: string;
   recipients: string;
   channel: string;
   campaigns: AlertCampaign[];
   kpi: string;
   trend: string;
   value: string;
   value_type: string;
   comparison: string;
   comparison_type: string;
   alert_status: string;
   alert_time: string;
   user_timezone: string;
   emails_triggered: number;
   timestamps_when_triggered: string;
   alert_instruction: string;
   alert_timeframe: string;
}

export interface DeleteMultipleAlertsPayload {
   client_id: string;
   user_id: string;
   alert_ids: number[];
}

export interface FetchAlertByIdPayload {
   client_id: string;
   user_id: string;
   alert_id: string;
}

export interface UpdateAlertPayload {
   client_id: string;
   user_id: string;
   chat_id: string;
   session_id: string;
   alert_id: string;
   alert_name: string;
   alert_description: string;
   recipients: string;
   channel: string;
   campaigns: AlertCampaign[];
   kpi: string;
   trend: string;
   value: string;
   value_type: string;
   comparison: string;
   comparison_type: string;
   alert_status: string;
   alert_time: string;
   user_timezone: string;
   emails_triggered: number;
   timestamps_when_triggered: string;
   alert_instruction: string;
   alert_timeframe: string;
}

export interface DeleteAlertPayload {
   client_id: string;
   user_id: string;
   alert_id: string;
}

export interface PauseUnpauseAlertPayload {
   client_id: string;
   user_id: string;
   alert_id: string;
}

export interface UpdateEmailRecipientsPayload {
   client_id: string;
   user_id: string;
   alert_id: string;
   recipients: string[];
}

interface Endpoints {
   fetchAllSessionsByUserID: (
      payload: FetchAllSessionsByUserIDPayload,
   ) => Promise<AxiosResponse<AlertingAgentChat[]>>;

   fetchHistoryBySessionID: (
      payload: FetchHistoryBySessionIDPayload,
   ) => Promise<AxiosResponse<AlertingAgentChat[]>>;

   addChatToSessionHistory: (
      payload: AddChatToSessionHistoryPayload,
   ) => Promise<AxiosResponse<void>>;

   fetchChatByChatId: (
      payload: FetchChatByChatIdPayload,
   ) => Promise<AxiosResponse<AlertingAgentChat>>;

   fetchUserFeatureUsage: (
      payload: FetchFeatureUsagePayload,
   ) => Promise<AxiosResponse<FeatureUsage>>;

   trackFeatureUsage: (
      payload: TrackFeatureUsagePayload,
   ) => Promise<AxiosResponse<void>>;

   fetchAlertsBySessionID: (
      payload: FetchAlertsBySessionIDPayload,
   ) => Promise<AxiosResponse<AlertingAgentAlert[]>>;

   fetchAllAlerts: (
      payload: FetchAllAlertsPayload,
   ) => Promise<AxiosResponse<AlertingAgentAlert[]>>;

   fetchAlertById: (
      payload: FetchAlertByIdPayload,
   ) => Promise<AxiosResponse<AlertingAgentAlert>>;

   createAlert: (
      payload: CreateAlertPayload,
   ) => Promise<AxiosResponse<AlertingAgentAlert>>;

   updateAlert: (
      payload: UpdateAlertPayload,
   ) => Promise<AxiosResponse<AlertingAgentAlert>>;

   deleteAlert: (payload: DeleteAlertPayload) => Promise<AxiosResponse<void>>;

   deleteMultipleAlerts: (
      payload: DeleteMultipleAlertsPayload,
   ) => Promise<AxiosResponse<void>>;

   pauseUnpauseAlert: (
      payload: PauseUnpauseAlertPayload,
   ) => Promise<AxiosResponse<void>>;

   updateRecipients: (
      payload: UpdateEmailRecipientsPayload,
   ) => Promise<AxiosResponse<void>>;
}

const alertingAgentEndpoints: Endpoints = {
   fetchAllSessionsByUserID: (payload) =>
      dashboardApiAgent.get(
         `/alerting-agent/${payload.client_id}/${payload.user_id}/session`,
         {
            params: {
               page: payload.page, // Added for pagination
            },
         },
      ),

   fetchHistoryBySessionID: (payload) =>
      dashboardApiAgent.get(
         `/alerting-agent/${payload.client_id}/${payload.user_id}/session/${payload.session_id}`,
      ),

   addChatToSessionHistory: (payload) =>
      dashboardApiAgent.post(
         `/alerting-agent/${payload.client_id}/${payload.user_id}/session/${payload.session_id}`,
         payload,
      ),

   fetchChatByChatId: (payload) =>
      dashboardApiAgent.get(
         `/alerting-agent/${payload.client_id}/${payload.user_id}/session/${payload.session_id}/chat/${payload.chat_id}`,
      ),

   fetchUserFeatureUsage: (payload) => {
      return dashboardApiAgent.get(
         `/settings/${payload.client_id}/${payload.user_id}/feature-usage`,
         {
            params: payload,
         },
      );
   },

   trackFeatureUsage: (payload) => {
      return dashboardApiAgent.post(
         `/settings/${payload.client_id}/${payload.user_id}/feature-usage`,
         payload,
      );
   },

   fetchAlertsBySessionID: (payload) =>
      dashboardApiAgent.get(
         `/alerting-agent/${payload.client_id}/${payload.user_id}/session/${payload.session_id}/alerts`,
      ),

   fetchAllAlerts: (payload) =>
      dashboardApiAgent.get(
         `/alerting-agent/${payload.client_id}/${payload.user_id}/alerts`,
      ),

   fetchAlertById: (payload) =>
      dashboardApiAgent.get(
         `/alerting-agent/${payload.client_id}/${payload.user_id}/alerts/${payload.alert_id}`,
      ),

   createAlert: (payload) =>
      dashboardApiAgent.post(
         `/alerting-agent/${payload.client_id}/${payload.user_id}/alerts`,
         payload,
      ),

   updateAlert: (payload) =>
      dashboardApiAgent.put(
         `/alerting-agent/${payload.client_id}/${payload.user_id}/alerts/${payload.alert_id}`,
         payload,
      ),

   deleteAlert: (payload) =>
      dashboardApiAgent.delete(
         `/alerting-agent/${payload.client_id}/${payload.user_id}/alerts/${payload.alert_id}`,
      ),

   deleteMultipleAlerts: (payload) =>
      dashboardApiAgent.delete(
         `/alerting-agent/${payload.client_id}/${payload.user_id}/alerts`,
         { data: payload },
      ),

   pauseUnpauseAlert: (payload) =>
      dashboardApiAgent.put(
         `/alerting-agent/${payload.client_id}/${payload.user_id}/alerts/${payload.alert_id}/pause`,
      ),

   updateRecipients: (payload) =>
      dashboardApiAgent.put(
         `/alerting-agent/${payload.client_id}/${payload.user_id}/alerts/${payload.alert_id}/update-recipients`,
         {
            alert_id: payload.alert_id,
            recipients: payload.recipients,
         },
      ),
};

export default alertingAgentEndpoints;
