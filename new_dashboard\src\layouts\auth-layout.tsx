import { Navigate, Outlet, useLocation } from 'react-router-dom';

import { Box, Stack, useColorModeValue } from '@chakra-ui/react';
import { appStrings } from '../utils/strings/app-strings';
import { LocalStorageService, Keys } from '../utils/local-storage';

function AuthLayout() {
   // TODO: Need to write token logic here
   // TODO: Need to add sass file and manage styles for all auth screens there
   const location = useLocation();

   const token = LocalStorageService.getItem(Keys.Token);
   const authUser = LocalStorageService.getItem(Keys.FlableUserDetails);

   const currentPath = location.pathname;

   const isSetPasswordPage = currentPath.includes('/auth/set-password');

   if (token && authUser && !isSetPasswordPage) {
      return <Navigate to={'/'} replace />;
   }
   return (
      <Box
         display='flex'
         justifyContent='center'
         alignItems='center'
         // bg='#FFFFFF'
         height='100vh'
         className='auth-layout'
         bg={useColorModeValue('white', 'var(--background)')}
      >
         <Stack
            backgroundColor={useColorModeValue(
               'linear-gradient(to right, #EBEBEB 60%, rgb(117, 117, 117) 100%)',
               'var(--background-surface)',
            )}
            direction={['column', 'row']}
            spacing={['0', '0']}
            width={['90%', '60%']}
            height='90%'
            maxHeight='600px'
            minHeight='400px'
            justifyContent='center'
            alignItems='center'
            boxShadow={['none', 'lg']}
            // bg='linear-gradient(to right, #EBEBEB 60%, rgb(117, 117, 117) 100%)'
            borderTopRightRadius={['0', '8']}
            borderBottomRightRadius={['0', '8']}
            borderTopLeftRadius={['0', '8']}
            borderBottomLeftRadius={['0', '8']}
            className='auth-layout__container'
         >
            <Box
               display='flex'
               flexDirection='column'
               flexWrap='nowrap'
               width='100%'
               height='100%'
               maxW='450px'
               pt={[3, 7, 12, 15]}
               pl={[3, 7, 7, 12, 20]}
               pr={[3, 7, 7, 12, 20]}
               boxShadow='lg'
               bg={useColorModeValue('white', 'var(--background-surface)')}
               borderTopLeftRadius={['0', '8']}
               borderBottomLeftRadius={['0', '8']}
               className='auth-layout__form'
            >
               <Outlet />
            </Box>
            <Box
               width='100%'
               height='100%'
               justifyContent='center'
               alignItems='center'
               display={['none', 'none', 'grid']}
               className='auth-layout__welcome'
            >
               <Box width='100%' maxW='600px' justifyContent={'center'}>
                  <Box
                     fontSize={[null, null, 'xl', '2xl', '3xl']}
                     fontWeight='bold'
                     textAlign='center'
                  >
                     {appStrings.appGreet}
                  </Box>
                  <Box
                     fontSize={[null, null, 'xs', 'sm', 'md']}
                     textAlign='center'
                  >
                     {appStrings.appTagLine}
                  </Box>
               </Box>
            </Box>
         </Stack>
      </Box>
   );
}

export default AuthLayout;
