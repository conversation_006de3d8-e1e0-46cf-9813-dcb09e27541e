import { FormEvent, useCallback, useState } from 'react';
import { channelNames, facebookAdsIntegrationSteps } from '../utils/constant';
import { connectToFbAds } from '../utils';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { useNavigate } from 'react-router-dom';
import { useApiMutation } from '../../../hooks/react-query-hooks';

import CommerceIntegrationLayout from './commerce-integration-layout';
import image from '../images/integrations/meta_ads_logo.png';
import StoreConnectionSteps from './woocommerce-steps';
import Input from './Input';

import Config from '../../../config';
import endPoints from '../apis/agent';
import useBlockNavigation from '../../../hooks/use-block-navigation';
import { AuthUser } from '../../../types/auth';

interface GeneralInformationProps {
   onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
   accessToken: string;
   accountIds: string;
   trying: boolean;
   onConnect: (e: React.FormEvent<HTMLFormElement>) => void;
   apiError: { success: boolean; message: string } | null;
}

export interface ApiError {
   response: {
      data: {
         message: string | null;
      };
   };
}

const GeneralInformation: React.FC<GeneralInformationProps> = ({
   onChange,
   accessToken,
   accountIds,
   trying,
   onConnect,
   apiError,
}) => (
   <div className='general-information'>
      <div className='general-information__headings'>
         <h5 className='title'>General Information</h5>
         <p className='description'>
            Please fill in the below details to connect your Facebook Ads
            account:
         </p>
      </div>
      <form className='general-information__form' onSubmit={onConnect}>
         <div className='inputs'>
            <Input
               id='access-token'
               label='Access Token'
               name='accessToken'
               value={accessToken}
               onChange={onChange}
            />
            <Input
               id='account-ids'
               label='Account IDs'
               name='accountIds'
               value={accountIds}
               onChange={onChange}
               placeholder='Comma separated..'
            />
         </div>
         {apiError && (
            <div
               className={`api-response ${apiError.success ? 'api-response__success' : 'api-response__error'}`}
            >
               <h3 className='api-response__message'>{apiError.message}</h3>
            </div>
         )}
         <button
            disabled={trying}
            className='commerce-integration__button'
            type='submit'
         >
            {trying ? 'Connecting...' : 'Connect'}
         </button>
      </form>
   </div>
);

interface FormFields {
   accessToken: string;
   accountIds: string;
}

function FacebookAdsForm() {
   const [formFields, setFormFields] = useState<FormFields>({
      accessToken: '',
      accountIds: '',
   });
   const [apiError, setApiError] = useState<{
      success: boolean;
      message: string;
   } | null>(null);

   const client_id = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   )?.client_id;

   const [connectorDetails, setConnectorDetails] = useState<{
      sourceId: string;
      destinationId: string;
   }>({
      sourceId: '',
      destinationId: '',
   });
   const navigate = useNavigate();

   const { isPending: isPendingDestination, mutate: createDestination } =
      useApiMutation({
         mutationFn: endPoints.createDestination,
         onSuccessHandler: ({ destinationId }) => {
            console.log('DESTINATION ID ', destinationId);
            setConnectorDetails((prev) => ({ ...prev, destinationId }));

            if (connectorDetails.sourceId) {
               const payload = {
                  name: 'facebooks-ads-connection-' + client_id,
                  sourceId: connectorDetails.sourceId,
                  destinationId,
                  prefix: `${client_id?.toLowerCase()}_`,
               };

               createConnection(payload);
            }
         },
      });

   const { isPending: isPendingSource, mutate } = useApiMutation({
      mutationFn: endPoints.createSource,
      onSuccessHandler: ({ sourceId }) => {
         console.log('SOURCE ID ', sourceId);
         const workspaceId = Config.VITE_AIRBYTE_WORKSPACE_ID;
         const name = `${client_id?.toLowerCase()}_metaads_postgre_destination`;
         const schema = 'meta_ads';
         setConnectorDetails((prev) => ({ ...prev, sourceId }));

         createDestination({ workspaceId, name, schema });
      },
   });

   const handleError = (error: ApiError, defaultMessage: string) => {
      const errMessage = error.response?.data?.message || defaultMessage;
      setApiError({
         success: false,
         message: errMessage,
      });
   };

   const handleSync = async (connectionId: string) => {
      try {
         await endPoints.triggerSync({ connectionId });
         await connectToFbAds({
            channel_name: channelNames.FACEBOOK_ADS,
            client_id: client_id!,
            admin_access_token: formFields.accessToken,
            account_ids: formFields.accountIds,
            isConnect: true,
         });
         setApiError({
            success: true,
            message: 'Connection Established, Redirecting...',
         });
         setTimeout(() => {
            navigate('/integrations');
         }, 3000);
      } catch (err) {
         handleError(err as ApiError, 'Error connecting to Facebook Ads');
      }
   };

   // create Connection
   const { isPending: isPendingConnection, mutate: createConnection } =
      useApiMutation({
         mutationFn: endPoints.createConnection,
         onSuccessHandler: async ({ connectionId }) => {
            console.log('CONNECTION ID ', connectionId);
            await handleSync(connectionId);
         },
      });

   const handleChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
         const { name, value } = e.target;
         setFormFields((prev) => ({ ...prev, [name]: value }));
      },
      [],
   );

   const leftContent = (
      <StoreConnectionSteps steps={facebookAdsIntegrationSteps} />
   );

   function handleSubmit(e: FormEvent<HTMLFormElement>) {
      e.preventDefault();

      if (!client_id) return;
      if (!formFields.accessToken || !formFields.accountIds)
         return alert('Please provide details');

      const payload = {
         workspaceId: Config.VITE_AIRBYTE_WORKSPACE_ID,
         accessToken: formFields.accessToken,
         accountIds: formFields.accountIds
            .split(',')
            .map((item) => item.trim()),
         sourceType: 'facebook-marketing',
         name: 'meta-source-' + client_id,
         secretId: '',
      };

      mutate(payload);
   }
   useBlockNavigation(
      isPendingSource || isPendingDestination || isPendingConnection,
   );

   return (
      <CommerceIntegrationLayout leftContent={leftContent} image={image}>
         <GeneralInformation
            onChange={handleChange}
            accessToken={formFields.accessToken}
            accountIds={formFields.accountIds}
            onConnect={(e) => void handleSubmit(e)}
            apiError={apiError}
            trying={
               isPendingSource || isPendingDestination || isPendingConnection
            }
         />
      </CommerceIntegrationLayout>
   );
}

export default FacebookAdsForm;
