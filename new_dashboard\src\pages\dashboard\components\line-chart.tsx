import { useEffect, useState } from 'react';
import Chart from 'react-apexcharts';
import { ChartProp, KPIData } from '../utils/interface';
import { ApexOptions } from 'apexcharts';
import { useAppSelector } from '../../../store/store';
import { getChartDateLabel, getFormattedVal, toHHMMSS } from '../utils/helpers';
import { noZeroKPI } from '../../../utils/strings/kpi-constants';
import './line-chart.scss';
import { useColorMode } from '@chakra-ui/react';

function LineChart(props: ChartProp) {
   const { kpiDetails, value } = props;
   const { groupBy } = useAppSelector((state) => state.kpi);
   const categories = getChartDateLabel(kpiDetails.allData, groupBy);
   const colorMode = useColorMode();
   const chartColor =
      value === true ? '#0E9F6E' : value === false ? '#FF5630' : '';

   const [chartData, setchartData] = useState({
      options: {
         chart: {
            id: kpiDetails.displayName,
            toolbar: {
               show: false,
            },
            zoom: {
               enabled: false,
            },
         },
         tooltip: {
            y: {
               formatter: function (value: number) {
                  if (
                     !value &&
                     noZeroKPI.includes(kpiDetails.allData[0].kpi_names)
                  )
                     return 'N/A';
                  return kpiDetails.unit == 'time'
                     ? toHHMMSS(value)
                     : getFormattedVal(Math.round(value * 100) / 100);
               },
            },
         },
         scales: {
            y: {
               display: false,
            },
         },
         xaxis: {
            //type: groupBy == 'day' ? 'datetime' : 'string',
            categories: categories,
            labels: {
               show: false,
               style: {
                  colors: colorMode ? '#FFFFFF' : '#000000',
               },
            },
         },
         yaxis: {
            labels: {
               show: false,
               style: {
                  colors: colorMode ? '#FFFFFF' : '#000000',
               },
            },
         },
         stroke: {
            curve: 'smooth',
         },
         dataLabels: {
            enabled: false,
         },
         grid: {
            show: false,
         },
         colors: chartColor ? [chartColor] : undefined,
      },
      series: [
         {
            name: kpiDetails.displayName,
            data: kpiDetails.allData.map((x: KPIData) =>
               Number(x.kpi_value?.toFixed(2)),
            ),
         },
      ],
   });
   useEffect(() => {
      setchartData({
         options: {
            chart: {
               id: kpiDetails.displayName,
               toolbar: {
                  show: false,
               },
               zoom: {
                  enabled: false,
               },
            },
            scales: {
               y: {
                  display: false,
               },
            },
            tooltip: {
               y: {
                  formatter: function (value: number) {
                     if (
                        !value &&
                        noZeroKPI.includes(kpiDetails.allData[0].kpi_names)
                     )
                        return 'N/A';
                     return kpiDetails.unit == 'time'
                        ? toHHMMSS(value)
                        : getFormattedVal(Math.round(value * 100) / 100);
                  },
               },
            },
            xaxis: {
               //type: groupBy == 'day' ? 'datetime' : 'string',
               categories: categories,
               labels: {
                  show: false,
                  style: {
                     colors: colorMode ? '#FFFFFF' : '#000000',
                  },
               },
            },
            yaxis: {
               labels: {
                  show: false,
                  style: {
                     colors: colorMode ? '#FFFFFF' : '#000000',
                  },
               },
            },
            stroke: {
               curve: 'smooth',
            },
            dataLabels: {
               enabled: false,
            },
            grid: {
               show: false,
            },
            colors: chartColor ? [chartColor] : undefined,
         },
         series: [
            {
               name: kpiDetails.displayName,
               data: kpiDetails.allData.map((x: KPIData) =>
                  Number(x.kpi_value?.toFixed(2)),
               ),
            },
         ],
      });
   }, [kpiDetails.allData, colorMode]);

   return (
      <>
         <div
            style={{
               paddingTop: 25,
               flexGrow: 1,
               color: 'black',
               width: 0,
            }}
         >
            <Chart
               options={chartData.options as ApexOptions}
               series={chartData.series}
               type='area'
               height='120'
               width='100%'
            />
         </div>
      </>
   );
}

export default LineChart;
