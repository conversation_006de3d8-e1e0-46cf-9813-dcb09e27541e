import { Box, Heading, Text, Button, useToast, Flex } from '@chakra-ui/react';

import { flablePixelStep } from '../../../../utils/strings/onboarding-strings';
import './flable-pixel-step.scss';
import { Keys, LocalStorageService } from '../../../../utils/local-storage';
import { useApiMutation } from '../../../../hooks/react-query-hooks';
import onboardingEndpoints from '../../../../api/service/onboarding';
import FlabelPixelBody from './flable-pixel-body';
import keys from '../../../../utils/strings/query-keys';

const FlablePixelStep = () => {
   const toast = useToast();

   const registerProgMutation = useApiMutation({
      mutationFn: onboardingEndpoints.updateRegisterProgress,
      onError(msg) {
         toast({
            title: 'Error',
            description: msg,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      },
      invalidateCacheQuery: [keys.fetchUserDetails],
   });

   const handleBack = () => {
      registerProgMutation.mutate({
         email_address: LocalStorageService.getItem(Keys.UserName) as string,
         client_id: LocalStorageService.getItem(Keys.ClientId) as string,
         register_progress: 'Step 3',
      });
   };

   const handleSkip = () => {
      registerProgMutation.mutate({
         email_address: LocalStorageService.getItem(Keys.UserName) as string,
         client_id: LocalStorageService.getItem(Keys.ClientId) as string,
         register_progress: 'Step 5',
      });
   };

   return (
      <Box
         width='100%'
         height='100%'
         className='flable-pixel-step'
         display='flex'
         alignItems='center'
         justifyContent='start'
         flexDirection='column'
      >
         <Box width='80%' marginTop='50px'>
            <Flex
               direction='column'
               alignItems='center'
               justifyContent='center'
               gap='10px'
            >
               <Heading fontSize='32px' fontWeight='500' color='#424242'>
                  {flablePixelStep.title}
               </Heading>
               <Text fontSize='14px' fontWeight='400' color='gray'>
                  {flablePixelStep.info}
               </Text>
            </Flex>
         </Box>
         <FlabelPixelBody />
         <Box display='flex' gap={5} mt={12} mb={3}>
            <Button
               onClick={handleBack}
               disabled={registerProgMutation.isPending}
               colorScheme='gray'
            >
               <Text fontSize='16px' fontWeight={500}>
                  Back
               </Text>
            </Button>
            <Button
               onClick={handleSkip}
               disabled={registerProgMutation.isPending}
               colorScheme='gray'
            >
               <Text fontSize='16px' fontWeight={500}>
                  Skip
               </Text>
            </Button>
         </Box>
      </Box>
   );
};

export default FlablePixelStep;
