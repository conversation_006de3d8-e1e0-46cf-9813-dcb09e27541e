export const METRICS_MAP: Record<string, string> = {
   ctr: 'Click-Through Rate (CTR)',
   roas: 'Return on Ad Spend (ROAS)',
   cpc: 'Cost Per Click (CPC)',
   cpm: 'Cost Per Mille (CPM)',
   cpa: 'Cost Per Action (CPA)',
   cpp: 'Cost Per Purchase (CPP)',
   cpv: 'Cost Per View (CPV)',
   cpl: 'Cost Per Lead (CPL)',
   cptp: 'Cost Per ThruPlay (CPTP)',
   vtr: 'View Through Rate (VTR)',
   leads: 'Leads',
   leads_conversion_rate: 'Lead Conversion Rate',
   video_view: 'Video Views',
   video_watch_25_percent: 'Video Watched 25%',
   video_watch_50_percent: 'Video Watched 50%',
   video_watch_75_percent: 'Video Watched 75%',
   video_watch_95_percent: 'Video Watched 95%',
   video_watch_100_percent: 'Video Watched 100%',
   '75_percent_video_views': '75% Video Views',
   '95_percent_video_views': '95% Video Views',
   '50_percent_video_views': '50% Video Views',
   '25_percent_video_views': '25% Video Views',
   '100_percent_video_views': '100% Video Views',
   thruplays: 'ThruPlays',
   frequency: 'Ad Frequency',
   impressions: 'Impressions',
   reach: 'Reach',
   clicks: 'Clicks',
   unique_clicks: 'Unique Clicks',
   unique_ctr: 'Unique Click-Through Rate (Unique CTR)',
   landing_page_view: 'Landing Page Views',
   link_click: 'Link Clicks',
   page_engagement: 'Page Engagement',
   post_engagement: 'Post Engagement',
   purchase: 'Purchases',
   purchase_rate: 'Purchase Rate',
   spend: 'Total Spend',
   amazon_cpc: 'Amazon Cost Per Click (CPC)',
   amazon_ads_gross_sales: 'Amazon Gross Sales (Ads)',
   amazon_total_clicks: 'Amazon Total Clicks',
   amazon_acos: 'Advertising Cost of Sales (ACOS)',
   amazon_total_impressions: 'Amazon Total Impressions',
   amazon_ads_spent: 'Amazon Ads Spend',
   amazon_ads_total_purchase: 'Amazon Ads Total Purchases',
   amazon_ctr: 'Amazon Click-Through Rate (CTR)',
   amazon_vcpm: 'Amazon vCPM (Viewable CPM)',
   amazon_canceled_orders: 'Amazon Canceled Orders',
   amazon_total_orders: 'Amazon Total Orders',
   amazon_new_customers: 'Amazon New Customers',
   amazon_return_customers: 'Amazon Returning Customers',
   amazon_discounts: 'Amazon Discounts',
   amazon_total_returns: 'Amazon Total Returns',
   amazon_total_sessions: 'Amazon Total Sessions',
   amazon_return_amount: 'Amazon Return Amount',
   amazon_gross_sales: 'Amazon Gross Sales',
   amazon_units_ordered: 'Amazon Units Ordered',
   amazon_order_revenue: 'Amazon Order Revenue',
   amazon_conversion_rate: 'Amazon Conversion Rate',
   amazon_taxes: 'Amazon Taxes',
   amazon_net_sales: 'Amazon Net Sales',
   amazon_shipping_charges: 'Amazon Shipping Charges',
   amazon_average_order_value: 'Amazon Average Order Value (AOV)',
   amazon_total_page_views: 'Amazon Total Page Views',
   google_total_conversions: 'Google Total Conversions',
   google_total_clicks: 'Google Total Clicks',
   google_cpm: 'Google Cost Per Mille (CPM)',
   google_total_spend: 'Google Total Spend',
   google_total_impressions: 'Google Total Impressions',
   google_cpa: 'Google Cost Per Action (CPA)',
   google_cpc: 'Google Cost Per Click (CPC)',
   google_ctr: 'Google Click-Through Rate (CTR)',
   google_conversion_rate: 'Google Conversion Rate',
   google_interactions: 'Google Interactions',
   google_roas: 'Google Return on Ad Spend (ROAS)',
   avg_session_duration: 'Average Session Duration',
   bounce_rate: 'Bounce Rate',
   new_users: 'New Users',
   total_users: 'Total Users',
   avg_pages_per_session: 'Average Pages per Session',
   cost_per_session: 'Cost per Session',
   conversion_rate: 'Conversion Rate',
   total_sessions: 'Total Sessions',
   total_orders: 'Total Orders',
   returning_customers: 'Returning Customers',
   average_order_value: 'Average Order Value (AOV)',
   abandoned_checkout: 'Abandoned Checkout',
   gross_sales: 'Gross Sales',
   shipping_charges: 'Shipping Charges',
   new_customers: 'New Customers',
   taxes: 'Taxes',
   discounts: 'Discounts',
   order_revenue: 'Order Revenue',
   blended_cpa: 'Blended Cost Per Action (CPA)',
   blended_total_revenue: 'Blended Total Revenue',
   blended_ad_spend: 'Blended Ad Spend',
   cogs: 'Cost of Goods Sold (COGS)',
   blended_total_orders: 'Blended Total Orders',
   variable_expenses: 'Variable Expenses',
   payment_gateway_cost: 'Payment Gateway Cost',
   fixed_expenses: 'Fixed Expenses',
   net_shipping_charges: 'Net Shipping Charges',
   blended_roas: 'Blended Return on Ad Spend (ROAS)',
   blended_gross_sales: 'Blended Gross Sales',
   mer: 'Marketing Efficiency Ratio (MER)',
};
