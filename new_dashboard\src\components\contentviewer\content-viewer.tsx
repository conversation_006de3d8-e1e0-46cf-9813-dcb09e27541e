import { useState } from 'react';
import {
   <PERSON>,
   <PERSON><PERSON>,
   <PERSON><PERSON>,
   <PERSON>b<PERSON>ist,
   Tab,
   TabPanels,
   TabPanel,
   Text,
} from '@chakra-ui/react';
import parse from 'html-react-parser';

import { useAppDispatch, useAppSelector } from '../../store/store';
import { ImageWithDeleteIcon } from '../tabContent/tab-content';
import { setSelectedTab } from '../../store/reducer/user-details-reducer';
import { socialLabel } from '../../utils/strings/socialwatch-strings';
import useIntegrationConnectionDetails from '../../hooks/use-integration-connection-details';

import './content-viewer.scss';

function ContentView() {
   const { rawFiles } = useAppSelector((state) => state.media);
   const { aiText } = useAppSelector((state) => state.config);
   const { selectedTab } = useAppSelector((state) => state.media);
   const [shortText, setshortText] = useState(true);

   function renderImages() {
      if (!rawFiles[selectedTab]) return null;

      return (
         <div style={{ display: 'flex', gap: 7, alignItems: 'center' }}>
            {rawFiles[selectedTab].map((file) =>
               file.startsWith('data:video/') ? (
                  <video width={106} controls>
                     <source src={file} type='video/mp4' />
                  </video>
               ) : (
                  file && (
                     <ImageWithDeleteIcon
                        key={file}
                        src={file}
                        alt='Preview'
                        preview={true}
                     />
                  )
               ),
            )}
         </div>
      );
   }
   let previewText = shortText
      ? aiText[selectedTab]?.substring(0, 350)
      : aiText[selectedTab];
   previewText = previewText || ''; // This guarantees that safeText is never null or undefined
   // Replace words that start with # by wrapping them in a <span>
   previewText = previewText.replace(/#[\w]+/g, (match) => {
      return `<span style="font-weight: 600;color: #0a66c2">${match}</span>`;
   });

   function getSocialText(selectedTab: string, previewText: string) {
      return (
         <pre className='preview-text'>
            {parse(previewText)}{' '}
            {aiText[selectedTab]?.length >= 351 && (
               <span
                  className='see-more'
                  onClick={() => setshortText(!shortText)}
               >
                  {shortText ? 'See more' : 'See less'}
               </span>
            )}
         </pre>
      );
   }

   return (
      <Stack spacing={4} height={'100%'}>
         <Text
            fontSize={16}
            fontWeight={700}
            p={4}
            borderBottom={'1px solid #A0A4A888'}
         >
            Note: Preview approximates how your content will display when
            published.
         </Text>
         {getSocialText(selectedTab, previewText)}

         <Box position='relative' display='inline-block' p={4}>
            {renderImages()}
         </Box>
      </Stack>
   );
}

interface TabValue {
   label: string;
   value: string;
   children: React.ReactNode;
}

const tabValues: TabValue[] = [
   {
      label: 'Twitter',
      value: 'twitter',
      children: <ContentView />,
   },
   {
      label: 'Linkedin',
      value: 'linkedin',
      children: <ContentView />,
   },
   {
      label: 'Instagram',
      value: 'instagram',
      children: <div>Instagram</div>,
   },
];

function ContentViewer() {
   const { mediaTypes } = useIntegrationConnectionDetails();

   const { selectedTab } = useAppSelector((state) => state.media);
   const dispatch = useAppDispatch();
   function handleTabClick(tab: string) {
      dispatch(setSelectedTab(tab));
   }

   return (
      <Box height={'100%'} pt={10}>
         <Tabs
            index={mediaTypes.findIndex((x) => x == selectedTab)}
            style={{
               width: '100%',
               boxSizing: 'border-box',
               resize: 'none',
               height: '100%',
            }}
         >
            <TabList>
               {mediaTypes.map((tab) => (
                  <Tab
                     onClick={() => handleTabClick(tab)}
                     key={tab}
                     fontSize={'20px'}
                  >
                     {socialLabel[tab] || tab}
                  </Tab>
               ))}
            </TabList>
            <TabPanels maxHeight={'80vh'} overflow={'auto'}>
               {tabValues.map((tab) => (
                  <TabPanel height={'90%'} key={tab.value} p={0}>
                     {tab.children}
                  </TabPanel>
               ))}
            </TabPanels>
         </Tabs>
      </Box>
   );
}

export default ContentViewer;
