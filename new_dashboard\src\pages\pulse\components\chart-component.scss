.chart-container {
   background: #fff;
   padding: 20px;
   border-radius: 10px;
   box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

   .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
         font-size: 1.25rem;
         margin: 0;
      }

      .tab-options {
         display: flex;
         gap: 15px;

         span {
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 5px;
            &.active {
               background-color: #007bff;
               color: white;
            }
         }
      }

      .chart-dropdown {
         margin-left: 15px;
      }
   }

   .data-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 15px;

      th,
      td {
         border: 1px solid #ddd;
         padding: 8px;
         text-align: left;
      }

      th {
         background-color: #f2f2f2;
      }
   }
}
