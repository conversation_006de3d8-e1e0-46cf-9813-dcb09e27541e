import { Box, Flex, useToast, useColorMode } from '@chakra-ui/react';
import { Heading } from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import kpiService from '../../api/service/kpi/index';
import { KPICategory } from '../../utils/strings/kpi-constants';
import { lazy } from 'react';
import DateRangeSelect from './components/date-select';
import { useApiQuery } from '../../hooks/react-query-hooks';
import KPIQueryKeys from './utils/query-keys';
import { Keys, LocalStorageService } from '../../utils/local-storage';
import { useAppSelector } from '../../store/store';
import PinnedKPI from './components/pinned';
import './dashboard.scss';
import TooltipIcon from '../../components/info-icon-content/tooltip-message';
import { content } from '../../components/info-icon-content/info-content';
import introJs from 'intro.js';
import { TooltipPosition } from 'intro.js/src/packages/tooltip';
import { useAppDispatch } from '../../store/store';
import { componentNames, setFlag } from '../../store/reducer/tour-reducer';
import IntegrateInfo from '../../components/integrate-info/integrate-info';
import { integrationInfoStrings } from '../../utils/strings/integrate-info-strings';
import { resetDateRange } from '../../store/reducer/kpi-reducer';
import { Loading } from '@/components';
import { anomalyKeys } from './utils/helpers';
import { anomalyResult } from './utils/interface';
type KpiCategoryKeys = keyof typeof KPICategory;
const Category = lazy(() => import('./components/category'));
function Dashboard() {
   const { dateRange, prevRange, groupBy, updatedPins } = useAppSelector(
      (state) => state.kpi,
   );
   const [initialLoadStarted, setInitialLoadStarted] = useState(false);
   const { optimisationsStatus } = useAppSelector((state) => state.onboarding);
   const toast = useToast();

   const {
      data: aggCategoryData,
      isFetching: first,
      isLoading: firstAggCategoryDataLoading,
      errorMessage,
      refetch: getInitialKpiData,
   } = useApiQuery({
      queryKey: [KPIQueryKeys.kpiData],
      queryFn: () => kpiService.getKpiData(getPayload()),
      getInitialData: () => undefined,
      enabled: false,
   });
   const {
      data: comparedAgg,
      isFetching: last,
      isLoading: lastAggCategoryDataLoading,
      errorMessage: err2,
      refetch: getKpiData,
   } = useApiQuery({
      queryKey: [KPIQueryKeys.prevKpiData],
      queryFn: () => kpiService.getKpiData(getPrevPayload()),
      getInitialData: () => undefined,
      enabled: false,
   });
   const {
      data: metaData,
      isFetching: metaLoad,
      errorMessage: metaErr,
      refetch: getMetaData,
   } = useApiQuery({
      queryKey: [KPIQueryKeys.kpiMeta],
      queryFn: () =>
         kpiService.getKpiMeta(
            LocalStorageService.getItem(Keys.ClientId) as string,
         ),
      getInitialData: () => [],
      enabled: false,
   });

   const getPayload = () => {
      return {
         clientId: LocalStorageService.getItem(Keys.ClientId) as string,
         startDate: dateRange.start,
         endDate: dateRange.end,
         prevStartDate: prevRange.start,
         prevEndDate: prevRange.end,
         compareBy: groupBy,
      };
   };
   const getPrevPayload = () => {
      return {
         clientId: LocalStorageService.getItem(Keys.ClientId) as string,
         startDate: prevRange.start,
         endDate: prevRange.end,
         prevStartDate: dateRange.start,
         prevEndDate: dateRange.end,
         compareBy: groupBy,
      };
   };
   useEffect(() => {
      setInitialLoadStarted(true);
      getInitialKpiData().catch(console.log);
      getKpiData().catch(console.log);
   }, [dateRange, prevRange, groupBy]);
   useEffect(() => {
      getMetaData().catch(console.log);
   }, [updatedPins]);
   if (errorMessage || err2 || metaErr) {
      toast({
         title: 'Error fetching KPI data',
         description: errorMessage || err2 || metaErr,
         status: 'error',
         duration: 5000,
         isClosable: true,
      });
   }
   const { dashboard } = useAppSelector((state) => state.tour);

   const dispatch = useAppDispatch();
   const intro = introJs();

   const steps: {
      element: string;
      intro: string;
      position: TooltipPosition;
      available: boolean;
   }[] = [
      {
         element: '#dateId',
         intro: 'This dropdown allows you to easily select the time range that matters most to your analysis.',
         position: 'top',
         available: true,
      },
      {
         element: '#rangeId',
         intro: 'Choose aggregation period for the data analysis.',
         position: 'bottom',
         available: true,
      },
      {
         element: '#pinnedId',
         intro: 'Use the Pinned feature to keep your favorite KPIs front and center. Simply select the metrics that matter most to you and pin them to your dashboard.',
         position: 'bottom',
         available: true,
      },
      {
         element: '#dashboard-amazon_ads',
         intro: 'In Amazon Ads, one can easily monitor key performance KPIs with real-time insights and optimize ad performance strategy.',
         position: 'top',
         available: !!(aggCategoryData && aggCategoryData?.amazon_ads),
      },
      {
         element: '#dashboard-amazon_selling_partner',
         intro: 'In Amazon Seller Central, one can easily monitor key marketplace KPIs with real-time insights and optimize e-commerce strategy.',
         position: 'top',
         available: !!(
            aggCategoryData && aggCategoryData?.amazon_selling_partner
         ),
      },
      {
         element: '#dashboard-facebookads',
         intro: 'In Meta Ads you can effortlessly monitor essential Meta KPIs like CPC and more. Stay on top of your ad performance and make data-driven decisions to maximize your ROI.',
         position: 'top',
         available: !!(aggCategoryData && aggCategoryData?.facebookads),
      },
      {
         element: '#dashboard-overall_metrics',
         intro: 'The Dashboard provides a unified view of your business metrics, including web analytics, Shopify performance, ad performance across multiple platforms, costs, and more—all in one place.',
         position: 'top',
         available: !!(aggCategoryData && aggCategoryData?.overall_metrics),
      },
      {
         element: '#dashboard-store',
         intro: 'In Shopify Store easily monitor key Shopify KPIs. Keep track of your store’s performance metrics and optimize your e-commerce strategy with real-time insights.',
         position: 'top',
         available: !!(aggCategoryData && aggCategoryData?.store),
      },
      {
         element: '#dashboard-web',
         intro: 'In web analytics, you can view key web analytics kpis like traffic, user behavior and much more.',
         position: 'bottom',
         available: !!(aggCategoryData && aggCategoryData?.web),
      },
   ];

   const startTour = () => {
      const availableSteps = steps.filter((step) => step.available);

      if (availableSteps.length > 0) {
         intro.setOptions({ steps: availableSteps });
         void intro.start();

         dispatch(
            setFlag({
               componentName: componentNames.DASHBOARD,
               flag: false,
            }),
         );
      } else {
         console.warn('No available steps for the tour.');
      }
   };

   useEffect(() => {
      if (dashboard) startTour();
   }, [dashboard]);

   const { colorMode } = useColorMode();

   if (
      !optimisationsStatus.complete &&
      !optimisationsStatus.channels_marketplace &&
      !optimisationsStatus.flable_pixel &&
      !optimisationsStatus.ads_account
   ) {
      return (
         <IntegrateInfo
            feature={integrationInfoStrings.dashboard.title}
            text={integrationInfoStrings.dashboard.description}
         />
      );
   }

   useEffect(() => {
      return () => {
         dispatch(resetDateRange());
      };
   }, []);

   const isLoading = firstAggCategoryDataLoading || lastAggCategoryDataLoading;

   return (
      <>
         {!initialLoadStarted || isLoading ? (
            <Loading />
         ) : (
            <Box
               p={4}
               backgroundColor={
                  colorMode === 'dark' ? 'var(--background)' : '#fafafa'
               }
               height={'calc(100vh - 48px)'}
               overflow={'auto'}
            >
               <Flex direction={'column'} gap={5}>
                  <Flex justifyContent={'space-between'}>
                     <Flex alignItems='center'>
                        <Heading
                           fontSize={24}
                           size={'xl'}
                           fontWeight={600}
                           color={colorMode === 'dark' ? 'white' : 'inherit'}
                        >
                           Dashboard
                        </Heading>
                        <TooltipIcon
                           label={content.dashboard}
                           fontSize='small'
                           iconColor='#437EEB'
                           ml={2}
                           mt={1}
                        />
                     </Flex>
                     <DateRangeSelect dateId='dateId' rangeId='rangeId' />
                  </Flex>
                  <Flex
                     direction={'column'}
                     justifyContent={'flex-start'}
                     gap={5}
                  >
                     <PinnedKPI
                        pinnedId='pinnedId'
                        metaData={metaData || []}
                        aggCategoryData={aggCategoryData || {}}
                        comparedAgg={comparedAgg || {}}
                        loading={{ first: metaLoad, last: last }}
                     />
                     {aggCategoryData &&
                        Object.entries(aggCategoryData)
                           .filter(([key]) => key !== 'anomaly')
                           .sort()
                           .map(([category, val]) => {
                              const key: KpiCategoryKeys =
                                 category.trim() as KpiCategoryKeys;
                              return (
                                 <>
                                    <Category
                                       id={`dashboard-${category.trim().toLowerCase().replace(/\s+/g, '-')}`}
                                       key={category}
                                       metaData={metaData || []}
                                       head={category}
                                       data={val}
                                       loading={{ first: first, last: last }}
                                       prevData={
                                          comparedAgg
                                             ? comparedAgg[key] || {}
                                             : {}
                                       }
                                       anomaly={
                                          aggCategoryData['anomaly'] &&
                                          anomalyKeys[
                                             category as keyof typeof anomalyKeys
                                          ]
                                             ? (
                                                  aggCategoryData['anomaly']
                                                     ?.result as unknown as anomalyResult
                                               )?.[
                                                  anomalyKeys[
                                                     category as keyof typeof anomalyKeys
                                                  ]
                                               ]
                                             : undefined
                                       }
                                    />
                                 </>
                              );
                           })}
                  </Flex>
               </Flex>
            </Box>
         )}
      </>
   );
}

export default Dashboard;
