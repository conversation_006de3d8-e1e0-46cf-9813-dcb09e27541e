import {
   Button,
   Checkbox,
   Input,
   InputGroup,
   InputLeftElement,
   NumberInput,
   NumberInputField,
   Table,
   TableCaption,
   TableContainer,
   Tbody,
   Td,
   Th,
   Thead,
   Tooltip,
   Tr,
   useColorModeValue,
} from '@chakra-ui/react';
import { FaIndianRupeeSign } from 'react-icons/fa6';
import { LiaPenAltSolid } from 'react-icons/lia';
import { MdDeleteOutline } from 'react-icons/md';
import { useAppSelector } from '../../../store/store';
import DatePicker from 'react-datepicker';
import { useDispatch } from 'react-redux';
import { openModal } from '../../../store/reducer/modal-reducer';
import { modalTypes } from '../../../components/modals/modal-types';
import { FixedExpenseData } from '../interface';

function FixedExpenses() {
   const { fixedExpenses } = useAppSelector((state) => state.cfo);
   if (fixedExpenses.length == 0) return null;
   const dispatch = useDispatch();
   const handleDelete = (record: FixedExpenseData) => {
      dispatch(
         openModal({
            modalType: modalTypes.DELETE_RECORD,
            modalProps: {
               data: {
                  title: record.title,
                  cost: record.cost,
                  source: record.source,
                  categories: JSON.parse(record.categories) as string[],
                  startDate: record.start_date,
                  endDate: record.end_date,
                  id: record.id,
                  recurringDays: record.recurring_days,
                  selCategory: record.sel_category,
                  adSpend: record.ad_spend,
               },
               type: 'fixed',
            },
         }),
      );
   };
   const handleEdit = (record: FixedExpenseData) => {
      dispatch(
         openModal({
            modalType: modalTypes.FIXED_EXPENSE_MODAL,
            modalProps: {
               title: record.title,
               cost: record.cost ?? '',
               source: record.source,
               categories: JSON.parse(record.categories) as string[],
               startDate: record.start_date,
               endDate: record.end_date,
               id: record.id,
               recurringDays: record.recurring_days ?? '',
               selCategory: record.sel_category,
               adSpend: record.ad_spend,
            },
         }),
      );
   };

   return (
      <TableContainer
         className='ce-table'
         my={5}
         mx={4}
         border={'none !important'}
      >
         <Table variant='simple'>
            <TableCaption
               textAlign={'left'}
               color={useColorModeValue('black', 'white')}
               fontSize={'16px'}
               px={0}
               placement='top'
            >
               Fixed Expenses
            </TableCaption>
            <Thead>
               <Tr>
                  <Th>Name</Th>
                  <Th>Category</Th>
                  <Th>Cost</Th>
                  <Th>Source</Th>
                  <Th> Start Date</Th>
                  <Th> End Date</Th>
                  <Th>Recurring</Th>
                  <Th>Ad Spend</Th>
                  <Th>Cost Per Day</Th>
                  <Th></Th>
               </Tr>
            </Thead>
            <Tbody>
               {fixedExpenses.map((fw) => {
                  const startDate = fw.start_date
                     ? new Date(fw.start_date as string)
                     : null;
                  const endDate = fw.end_date
                     ? new Date(fw.end_date as string)
                     : null;
                  const costPerDay = (
                     Number(fw.cost) / Number(fw.recurring_days)
                  ).toFixed(2);
                  return (
                     <Tr key={fw.id}>
                        <Td> {fw.title}</Td>
                        <Td>{fw.sel_category}</Td>

                        <Td>
                           {' '}
                           <InputGroup
                              backgroundColor={useColorModeValue(
                                 'white',
                                 'var(--controls)',
                              )}
                           >
                              <InputLeftElement pointerEvents='none'>
                                 {' '}
                                 <FaIndianRupeeSign />
                              </InputLeftElement>
                              <NumberInput
                                 width={'100%'}
                                 minWidth={'100px'}
                                 defaultValue={fw.cost}
                                 isDisabled
                              >
                                 <NumberInputField pl={8} />
                              </NumberInput>
                           </InputGroup>
                        </Td>
                        <Td>
                           {' '}
                           <Tooltip label={fw.source} hasArrow gutter={20}>
                              <Input
                                 defaultValue={fw.source}
                                 minWidth={'100px'}
                                 type='text'
                                 disabled
                              />
                           </Tooltip>
                        </Td>
                        <Td>
                           {' '}
                           <DatePicker
                              dateFormat='MMM d, yyyy'
                              disabled
                              selected={startDate}
                              onChange={console.log}
                           />
                        </Td>
                        <Td>
                           <DatePicker
                              dateFormat='MMM d, yyyy'
                              disabled
                              selected={endDate}
                              onChange={console.log}
                           />
                        </Td>
                        <Td>{fw.recurring_days || ''}</Td>
                        <Td>
                           <Checkbox
                              isDisabled
                              defaultChecked={fw.ad_spend}
                              height={'100%'}
                              width={'100%'}
                           ></Checkbox>
                        </Td>
                        <Td>
                           <InputGroup
                              backgroundColor={useColorModeValue(
                                 'white',
                                 'var(--controls)',
                              )}
                           >
                              <InputLeftElement pointerEvents='none'>
                                 {' '}
                                 <FaIndianRupeeSign />
                              </InputLeftElement>
                              <NumberInput
                                 width={'100%'}
                                 defaultValue={costPerDay}
                                 isDisabled
                              >
                                 <NumberInputField
                                    pl={8}

                                    // value={cogsPerc}
                                    // onChange={(event: React.ChangeEvent<HTMLInputElement>) =>
                                    //    setcogsPerc(event.target.value)
                                    // }
                                 />
                              </NumberInput>
                           </InputGroup>
                        </Td>
                        <Td width={0}>
                           <Button
                              onClick={() => handleEdit(fw)}
                              height={'30px'}
                              width={'30px'}
                              mr={3}
                              backgroundColor={useColorModeValue(
                                 'white',
                                 'var(--controls)',
                              )}
                              p={0}
                              border={'1px solid #C2CBD4'}
                              borderRadius={'5px'}
                           >
                              <LiaPenAltSolid />
                           </Button>
                           <Button
                              onClick={() => handleDelete(fw)}
                              height={'30px'}
                              width={'30px'}
                              background={'none'}
                              p={0}
                              border={'1px solid #C2CBD4'}
                              borderRadius={'5px'}
                           >
                              <MdDeleteOutline />
                           </Button>
                        </Td>
                     </Tr>
                  );
               })}
            </Tbody>
         </Table>
      </TableContainer>
   );
}

export default FixedExpenses;
