/* eslint-disable @typescript-eslint/no-explicit-any */
import digitalAssitantAgent from '../apis/digital-assitant-agent';

interface TelegramServices {
   checkExistingUser: (
      client_id: string,
      dashboard_login_user_id: string,
   ) => Promise<any>;
   disconnectUser: (dashboard_login_user_id: string) => Promise<any>;
}

const telegramServices: TelegramServices = {
   checkExistingUser: (client_id, dashboard_login_user_id) =>
      digitalAssitantAgent.post('/check_existing_users', {
         client_id,
         dashboard_login_user_id,
      }),
   disconnectUser: (dashboard_login_user_id) =>
      digitalAssitantAgent.post('/disconnect_existing_users', {
         dashboard_login_user_id,
      }),
};

export default telegramServices;
