const KPIQueryKeys = {
   kpiData: 'kpiData',
   kpiMeta: 'kpiMeta',
   prevKpiData: 'prevKpiData',
   kpiSummary: 'kpiSummary',
   kpiPinned: 'kpiPinned',
   kpiVisible: 'kpiVisible',
   pinVisibleOrder: 'pinVisibleOrder',
};

export const SocialWatchQueries = {
   linkedinPages: 'linkedinPages',
   linkedinSetPages: 'linkedinSetPages',
   linkedinUser: 'linkedinUser',
};

export const loadingStateChannel = {
   TWITTER: 'twitter',
   LINKEDIN: 'linkedin',
};

export default KPIQueryKeys;

export const SettingsQueryKeys = {
   generalSettings: 'settings/generalSettings',
   competitors: 'settings/competitors',
   language: 'settings/language',
   timezone: 'settings/timezone',
   industry: 'settings/industry',
   industryOptions: 'settings/industryOptions',
   verify: 'settings/verify',
   sendEmailReport: 'sendEmailReport',
   getEmailReports: 'getEmailReports',
   createEmailReport: 'createEmailReport',
   updateEmailReport: 'updateEmailReport',
   deleteEmailReport: 'deleteEmailReport',
   pauseAutoReport: 'pauseAutoReport',
};

export const OverviewQueryKeys = {
   daywiseCampaign: 'daywiseCampaign',
   kpiwiseCampaign: 'kpiwiseCampaign',
   objectives: 'objectives',
   metrics: 'metrics',
   adsets: 'adsets',
};

export const CFOKeys = {
   getCogs: 'getCogs',
   upsertCogs: 'upsertCogs',
   getPaymentMethods: 'getPaymentMethods',
   upsertPaymentMethod: 'upsertPaymentMethod',
   getShippingCosts: 'getShippingCosts',
   getShippingCostsByOrderId: 'getShippingCostsByOrderId',
   getShippingProfiles: 'getShippingProfiles',
   getVariableExpenses: 'getVariableExpenses',
   getFixedExpenses: 'getFixedExpenses',
   upsertShippingCost: 'upsertShippingCost',
   upsertShippingCostByOrderId: 'upsertShippingCostByOrderId',
   upsertShippingProfile: 'upsertShippingProfile',
   upsertFixedExpense: 'upsertFixedExpense',
   upsertVariableExpense: 'upsertVariableExpense',
   deleteRecord: 'deleteRecord',
};

/** PULSE - PERFORMANCE INSIGHTS - META ADS **/

export const pulseMetaKeys = {
   metaObjectives: 'metaObjectives',
   trackedCampaigns: 'trackedCampaigns',
   metaCampaigns: 'metaCampaigns',
   metaAdsets: 'metaAdsets',
   metaAds: 'metaAds',
   metaTargeting: 'metaTargeting',
   budgetSpend: 'budgetSpend',
   chartInsights: 'chartInsights',
   benchmarkInsights: 'benchmarkInsights',
   adsetInsights: 'adsetInsights',
   targetingInsights: 'targetingInsights',
};
