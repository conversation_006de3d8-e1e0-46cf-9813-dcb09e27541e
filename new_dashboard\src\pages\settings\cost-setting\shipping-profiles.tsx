import {
   Button,
   Flex,
   Heading,
   Input,
   InputGroup,
   InputLeftElement,
   Table,
   TableContainer,
   Tbody,
   Td,
   Th,
   Thead,
   Tr,
   useColorModeValue,
} from '@chakra-ui/react';
import { useAppSelector } from '../../../store/store';
import { LiaPenAltSolid } from 'react-icons/lia';
import { MdDeleteOutline } from 'react-icons/md';
import {
   setShippingProfile,
   ShippingProfiles,
} from '../../../store/reducer/cfo-reducer';
import { openModal } from '../../../store/reducer/modal-reducer';
import { modalTypes } from '../../../components/modals/modal-types';
import { useDispatch } from 'react-redux';
import { ICountry } from 'countries-list';
import { ChangeEvent, useState } from 'react';
import { IoSearchSharp } from 'react-icons/io5';

function ShippingProfilesList() {
   const { shippingProfiles } = useAppSelector((state) => state.cfo);
   const [search, setSearch] = useState<string>('');
   if (shippingProfiles.length == 0) return null;
   const dispatch = useDispatch();
   const getZoneNames = (zone: string) => {
      if (zone == 'worldwide') {
         return 'Worldwide';
      }
      const arrZone = JSON.parse(zone) as ICountry[];
      return `${arrZone.length} countries`;
   };
   const getShippingRates = (
      isFixed: boolean,
      minWeights: number[],
      maxWeights: number[],
      rates: number[],
      measures: string[],
   ) => {
      if (isFixed) {
         return rates[0];
      }
      return minWeights
         .map(
            (min, idx) =>
               `${rates[idx]}-Order ${min}-${maxWeights[idx]} ${measures[idx]}`,
         )
         .join(', ');
   };
   const handleShippingProfileEdit = (profile: ShippingProfiles) => {
      dispatch(
         openModal({
            modalType: modalTypes.SHIPPING_PROFILE,
            modalProps: {
               edit: true,
            },
         }),
      );
      dispatch(
         setShippingProfile({
            name: profile.profile_name,
            zones:
               profile.zone == 'worldwide'
                  ? profile.zone
                  : (JSON.parse(profile.zone) as ICountry[]),
            fixedRate: !profile.max_weights[0]
               ? profile.rates[0].toString()
               : '',
            weightBased: profile.max_weights[0]
               ? profile.max_weights.map((max, idx) => ({
                    rate: profile.rates[idx].toString(),
                    minWeight: profile.min_weights[idx].toString(),
                    maxWeight: max.toString(),
                    measure: profile.measures[idx],
                 }))
               : [],
         }),
      );
   };
   const handleShippingProfileDelete = (profile: ShippingProfiles) => {
      dispatch(
         openModal({
            modalType: modalTypes.DELETE_RECORD,
            modalProps: {
               type: 'shippingProfile',
               data: profile,
            },
         }),
      );
   };
   const handleSearch = (e: ChangeEvent<HTMLInputElement>) => {
      setSearch(e.target.value);
   };
   return (
      <Flex direction={'column'} width={'100%'} gap={4}>
         <Heading className='panel' fontSize={'18px'}>
            Shipping Profiles
         </Heading>
         <InputGroup
            backgroundColor={useColorModeValue('white', 'var(--controls)')}
         >
            <InputLeftElement pointerEvents='none'>
               <IoSearchSharp />
            </InputLeftElement>
            <Input
               placeholder='Search Countries'
               value={search}
               onChange={handleSearch}
            />
         </InputGroup>
         <TableContainer className='cog-table' border={'none !important'}>
            <Table variant='simple'>
               <Thead>
                  <Tr>
                     <Th>Name</Th>
                     <Th>Zone</Th>
                     <Th> Setting</Th>
                     <Th>Shipping Rate</Th>
                     <Th></Th>
                  </Tr>
               </Thead>
               <Tbody>
                  {shippingProfiles
                     .filter((p) => {
                        return p.zone
                           .toLowerCase()
                           .includes(search.toLowerCase());
                     })
                     .map((profile) => (
                        <Tr borderTop={'1px solid #C2CBD4'}>
                           <Td>{profile.profile_name}</Td>
                           <Td>{getZoneNames(profile.zone)}</Td>
                           <Td>
                              {profile.is_fixed ? 'Fixed Rate' : 'Weight Based'}
                           </Td>
                           <Td>
                              {getShippingRates(
                                 profile.is_fixed,
                                 profile.min_weights,
                                 profile.max_weights,
                                 profile.rates,
                                 profile.measures,
                              )}
                           </Td>
                           <Td width={0}>
                              <Button
                                 onClick={() =>
                                    handleShippingProfileEdit(profile)
                                 }
                                 height={'30px'}
                                 width={'30px'}
                                 mr={3}
                                 background={'none'}
                                 p={0}
                                 border={'1px solid #C2CBD4'}
                                 borderRadius={'5px'}
                              >
                                 <LiaPenAltSolid />
                              </Button>
                              <Button
                                 onClick={() =>
                                    handleShippingProfileDelete(profile)
                                 }
                                 height={'30px'}
                                 width={'30px'}
                                 background={'none'}
                                 p={0}
                                 border={'1px solid #C2CBD4'}
                                 borderRadius={'5px'}
                              >
                                 <MdDeleteOutline />
                              </Button>
                           </Td>
                        </Tr>
                     ))}
               </Tbody>
            </Table>
         </TableContainer>
      </Flex>
   );
}

export default ShippingProfilesList;
