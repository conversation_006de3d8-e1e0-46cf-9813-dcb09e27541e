import { useState, useEffect } from 'react';
import { useAppSelector } from '../../store/store';
import {
   AnomalyCause,
   KPIDetails,
} from '../../pages/dashboard/utils/interface';
import kpiService from '../../api/service/kpi';
import { Keys, LocalStorageService } from '../../utils/local-storage';
import { UserDetails } from '../chatbox/interface';
import {
   HStack,
   Icon,
   Modal,
   ModalBody,
   ModalCloseButton,
   ModalContent,
   ModalHeader,
   ModalOverlay,
   SkeletonText,
   Tab,
   TabList,
   TabPanel,
   TabPanels,
   Tabs,
   Text,
   VStack,
} from '@chakra-ui/react';
import { FiZap } from 'react-icons/fi';

interface NestedPopupProps {
   isOpen: boolean;
   onClose: () => void;
   kpiDetails: KPIDetails;
}
const AnomalyCausePopup: React.FC<NestedPopupProps> = ({
   isOpen,
   onClose,
   kpiDetails,
}) => {
   const userDetails =
      LocalStorageService.getItem<UserDetails>(Keys.FlableUserDetails) ||
      ({} as UserDetails);
   const { dateRange } = useAppSelector((state) => state.kpi);
   const [anomalyRootCause, setanomalyRootCause] =
      useState<AnomalyCause | null>(null);
   const [isanomalyRootCauseLoading, setanomalyRootCauseLoading] =
      useState<boolean>(true);

   useEffect(() => {
      setanomalyRootCauseLoading(true);
      const fetchSummary = async () => {
         if (isOpen) {
            const payload = {
               client_id: userDetails?.client_id,
               start_date: dateRange?.start.split('T')[0],
               end_date: dateRange?.end.split('T')[0],
               kpi: kpiDetails?.kpi_names,
            };

            const response = await kpiService.getAnomalyCause(payload);
            if (response) {
               setanomalyRootCause(response.data);
               setanomalyRootCauseLoading(false);
            }
         }
      };

      void fetchSummary();
   }, [isOpen]);

   const result = anomalyRootCause?.result;
   const sectionRegex = /(\d+\.\s+[A-Z\s]+:)/g;
   const parsedSections = [];
   if (result) {
      const rawSections = result.split(sectionRegex).filter(Boolean);

      for (let i = 0; i < rawSections.length; i += 2) {
         const title = rawSections[i]
            .replace(/^\d+\.\s+/, '')
            .replace(':', '')
            .trim();
         const content = rawSections[i + 1]?.trim();
         parsedSections.push({ title, content });
      }
   }

   return (
      <Modal
         isOpen={isOpen}
         onClose={onClose}
         size='3xl'
         isCentered
         motionPreset='none'
      >
         <ModalOverlay />
         <ModalContent
            position='fixed'
            top='-100px'
            left='50%'
            transform='translateX(-50%)'
            borderRadius='xl'
            boxShadow='lg'
            bg='white'
            maxHeight={`calc(100vh - 100px)`}
         >
            <ModalHeader
               fontWeight='700'
               fontSize='2xl'
               display='flex'
               alignItems='center'
               gap={2}
            >
               <Icon as={FiZap} color='blue.600' />
               {kpiDetails?.displayName}
            </ModalHeader>
            <ModalCloseButton />
            <ModalBody pt={0} pb={6} overflow='auto'>
               {isanomalyRootCauseLoading ? (
                  <SkeletonText mt={4} noOfLines={35} spacing={3} />
               ) : parsedSections && parsedSections.length > 0 ? (
                  <Tabs variant='soft-rounded' colorScheme='blue'>
                     <TabList>
                        {parsedSections.map((section, idx) => (
                           <Tab
                              key={idx}
                              fontSize='sm'
                              _selected={{
                                 backgroundColor: 'blue.600',
                                 color: 'white',
                              }}
                           >
                              {section.title}
                           </Tab>
                        ))}
                     </TabList>
                     <TabPanels pt={4}>
                        {parsedSections.map((section, idx) => (
                           <TabPanel key={idx} px={1}>
                              <VStack align='start' spacing={4}>
                                 {section.content
                                    .split('\n')
                                    .map((line, idx) => {
                                       let cleanLine = line.trim();

                                       if (cleanLine.startsWith('-')) {
                                          cleanLine = cleanLine.slice(1).trim();
                                       } else if (cleanLine.startsWith('*')) {
                                          cleanLine = cleanLine.slice(1).trim();
                                       }

                                       const parts = cleanLine
                                          .split(/(".*?")/g)
                                          .map((part, index) => {
                                             if (
                                                part.startsWith('"') &&
                                                part.endsWith('"')
                                             ) {
                                                return (
                                                   <Text
                                                      as='span'
                                                      fontWeight='bold'
                                                      key={index}
                                                   >
                                                      {part.slice(1, -1)}{' '}
                                                   </Text>
                                                );
                                             }
                                             return part;
                                          });
                                       if (line.trim().startsWith('-')) {
                                          return <Text key={idx}>{parts}</Text>;
                                       } else if (line.trim().startsWith('*')) {
                                          return (
                                             <HStack key={idx} align='start'>
                                                <Text
                                                   as='span'
                                                   fontSize='lg'
                                                   lineHeight='1'
                                                >
                                                   •
                                                </Text>
                                                <Text as='span'>{parts}</Text>
                                             </HStack>
                                          );
                                       } else {
                                          return <Text key={idx}>{parts}</Text>;
                                       }
                                    })}
                              </VStack>
                           </TabPanel>
                        ))}
                     </TabPanels>
                  </Tabs>
               ) : (
                  <Text fontSize='sm' color='gray.500'>
                     No data available.
                  </Text>
               )}
            </ModalBody>
         </ModalContent>
      </Modal>
   );
};

export default AnomalyCausePopup;
