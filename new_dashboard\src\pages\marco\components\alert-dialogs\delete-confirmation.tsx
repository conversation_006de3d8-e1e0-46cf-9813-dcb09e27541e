import { CustomAlert } from '@/api/service/custom-alerts';
import {
   AlertDialog,
   AlertDialogCancel,
   AlertDialogAction,
   AlertDialogContent,
   AlertDialogDescription,
   AlertDialogFooter,
   AlertDialogHeader,
   AlertDialogTitle,
   AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { useAppDispatch, useAppSelector } from '@/store/store';
import {
   useDeleteCustomAlert,
   useDeleteMultipleCustomAlerts,
   useFetchAllAlerts,
} from '../../apis/custom-alerts-apis';
import { setDeleteMultipleAlerts } from '@/store/reducer/custom-alerts-reducer';

interface DeleteConfirmationProps {
   children?: React.ReactNode;
   selectedAlerts: number[];
   alert?: CustomAlert;
   setSelectedAlerts?: (alerts: number[]) => void;
}

const DeleteConfirmation = (props: DeleteConfirmationProps) => {
   const dispatch = useAppDispatch();

   const { deleteMultipleAlerts } = useAppSelector(
      (state) => state.customAlerts,
   );

   const { refetch: refetchAllAlerts } = useFetchAllAlerts();

   const { mutateAsync: deleteAlert } = useDeleteCustomAlert();

   const { mutateAsync: deleteAlerts } = useDeleteMultipleCustomAlerts();

   const handleConfirm = async () => {
      if (deleteMultipleAlerts) {
         await deleteAlerts(props.selectedAlerts);
      } else if (props.alert && props.alert && props.alert.alert_id) {
         await deleteAlert(Number(props.alert.alert_id));
      }

      if (props.setSelectedAlerts) {
         props.setSelectedAlerts([]);
      }

      dispatch(setDeleteMultipleAlerts(false));
      await refetchAllAlerts();
   };

   return (
      <AlertDialog>
         <AlertDialogTrigger asChild>
            {props.children || (
               <Button
                  variant='default'
                  size='lg'
                  className='bg-red-600 hover:bg-red-700 text-white'
               >
                  Delete {props.selectedAlerts.length} Alert
                  {props.selectedAlerts.length > 1 ? 's' : ''}
               </Button>
            )}
         </AlertDialogTrigger>
         <AlertDialogContent className='bg-white dark:bg-gray-800'>
            <AlertDialogHeader>
               <AlertDialogTitle className='font-bold text-xl'>
                  Delete Alerts
               </AlertDialogTitle>
               <AlertDialogDescription>
                  Are you sure you want to delete these alerts? This action
                  cannot be undone.
               </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
               <AlertDialogCancel className='font-semibold !text-sm'>
                  Cancel
               </AlertDialogCancel>
               <AlertDialogAction
                  className='font-normal !text-sm bg-red-600 hover:bg-red-700 text-white'
                  onClick={() => void handleConfirm()}
               >
                  Confirm
               </AlertDialogAction>
            </AlertDialogFooter>
         </AlertDialogContent>
      </AlertDialog>
   );
};

export default DeleteConfirmation;
