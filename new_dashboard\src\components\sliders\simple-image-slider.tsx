import React, { useState } from 'react';
import './simple-slider.scss';

interface ImageSliderProps {
   images: string[];
}

const SimpleImageSlider: React.FC<ImageSliderProps> = ({ images }) => {
   const [currentIndex, setCurrentIndex] = useState(0);
   const prevSlide = () => {
      const newIndex =
         currentIndex === 0 ? images.length - 1 : currentIndex - 1;
      setCurrentIndex(newIndex);
   };

   const nextSlide = () => {
      const newIndex =
         currentIndex === images.length - 1 ? 0 : currentIndex + 1;
      setCurrentIndex(newIndex);
   };

   return (
      <div className='image-slider'>
         <div className='image-slider__slides'>
            <img
               style={{
                  width: 220,
                  height: 220,
                  objectFit: 'cover',
               }}
               src={images[currentIndex]}
               alt={`Slide ${currentIndex}`}
            />
         </div>
         <button
            className='image-slider__button image-slider__button--prev'
            onClick={prevSlide}
         >
            ❮
         </button>
         <button
            className='image-slider__button image-slider__button--next'
            onClick={nextSlide}
         >
            ❯
         </button>
      </div>
   );
};

export default SimpleImageSlider;
