import React, { useEffect, useRef, useState } from 'react';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { AuthUser } from '../../../types/auth';
import alertingAgentIcon from '../../../assets/image/agents/Koa - Reporting Agent.png';
import { IoSend } from 'react-icons/io5';
import { useAppDispatch, useAppSelector } from '../../../store/store';
import { IconButton } from '@chakra-ui/react';
import { Input, InputGroup, InputRightElement } from '@chakra-ui/react';
import {
   sendAlertPrompt,
   SendAlertPromptPayload,
} from '../../../api/service/agentic-workflow/alerting-agent';
import {
   getSmartSuggestions,
   getValue,
} from '../utils/alerting-agent/agents-helpers';
import { QUESTION_SUGGESTIONS } from '../utils/alerting-agent/constants';
import { toast } from 'sonner';
import {
   useAddChatToSessionHistoryMutation,
   useCreateAlertMutation,
   useFetchAlertingHistoryQuery,
   useFetchAlertsBySessionIdQuery,
   useFetchHistoryBySessionIdQuery,
   useTrackFeatureUsageMutation,
} from '../apis/alerting-agent-apis';
import { setCurrentSessionID } from '@/store/reducer/alerting-agent-reducer';
import AlertingAgentChat from '../components/chat-bubble/alerting-agent-chat';
import Tooltips from '@/components/tooltip';

const AlertingAgent = () => {
   const dispatch = useAppDispatch();

   const { email, client_id, user_id } =
      LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails) ?? {};
   const messagesEndRef = useRef<HTMLDivElement>(null);

   const { currentSessionID } = useAppSelector((state) => state.alertingAgent);
   const { connectionDetails } = useAppSelector((state) => state.media);
   const { generalSettings } = useAppSelector((state) => state.settings);

   const [prompt, setPrompt] = useState<string>('');
   const [suggestions, setSuggestions] = useState<string[]>([]);

   const { refetch: refetchAlertingHistory } = useFetchAlertingHistoryQuery();

   const {
      data: currentSessionHistory,
      refetch: refetchSessionHistory,
      isFetching: isFetchingSessionHistory,
   } = useFetchHistoryBySessionIdQuery();

   const {
      data: currentSessionAlerts,
      refetch: refetchSessionAlerts,
      isFetching: isFetchingSessionAlerts,
   } = useFetchAlertsBySessionIdQuery();

   const {
      mutateAsync: addToSessionHistory,
      isPending: isPendingAddToSessionHistory,
   } = useAddChatToSessionHistoryMutation();

   const { mutateAsync: createAlert, isPending: isPendingCreateAlert } =
      useCreateAlertMutation();

   const { mutateAsync: updateFeatureUsage } = useTrackFeatureUsageMutation();

   const handleSampleQuestionClick = (question: string) => {
      handleSendPrompt(question).catch(console.log);
   };

   const handlePromptChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      setPrompt(event.target.value);
   };

   const handleKeyDown = async (
      event: React.KeyboardEvent<HTMLInputElement>,
   ) => {
      if (event.key === 'Enter') {
         await handleSendPrompt();
      }
   };

   const handleSendPrompt = async (question?: string) => {
      if (!prompt && !question) {
         toast.error('Please enter a prompt.');
         return;
      }

      const sessionId = currentSessionID || `Z${new Date().getTime()}`;
      const newChatId = `${new Date().getTime()}`;
      setPrompt('');

      dispatch(setCurrentSessionID(sessionId));

      const addToSessionHistoryPayload = {
         client_id: String(client_id),
         user_id: String(user_id),
         session_id: String(sessionId),
         chat_id: String(newChatId),
         user_query: question || prompt,
         response_status: 'pending' as 'success' | 'error' | 'pending',
         final_response: '',
         session_summary: '',
         response_time: 0,
      };

      await addToSessionHistory(addToSessionHistoryPayload);
      await refetchSessionHistory();
      await refetchAlertingHistory();

      const startTime = performance.now();

      const payload: SendAlertPromptPayload = {
         client_id: client_id || '',
         chat: prompt || question || '',
         mode: 'prompt',
      };

      const agentResponse = await sendAlertPrompt(payload);

      const endTime = performance.now();
      const responseTime = Math.round(endTime - startTime);

      if (!agentResponse) {
         toast.error('Unexpected error. Please try again.');

         const updatedSessionHistoryPayload = {
            ...addToSessionHistoryPayload,
            chat_id: newChatId,
            response_status: 'error' as 'success' | 'error' | 'pending',
            final_response: '',
            session_summary: '',
            response_time: responseTime,
         };

         await addToSessionHistory(updatedSessionHistoryPayload);
         await refetchSessionHistory();

         return;
      }

      const { data, options, response } = agentResponse;

      if (agentResponse.status === 'success') {
         const alertSaveResponse = await createAlert({
            client_id: String(client_id),
            user_id: String(user_id),
            session_id: String(sessionId),
            chat_id: String(newChatId),
            alert_name: data.title,
            alert_description: data.description,
            alert_instruction: data.instruction,
            recipients: email as string,
            channel: data.channel,
            campaigns: Array.isArray(data?.campaign) ? data.campaign : [],
            kpi: data.kpi,
            trend: data.trend,
            value: data.value,
            value_type: data.value_type,
            comparison: getValue(data.comparison),
            comparison_type: getValue(data.comparison_type),
            alert_status: 'ACTIVE',
            alert_time: '09:00:00',
            alert_timeframe: agentResponse.options.time,
            user_timezone: generalSettings.timezone_name || 'UTC',
            emails_triggered: 0,
            timestamps_when_triggered: '',
         });

         const updatedSessionHistoryPayload = {
            ...addToSessionHistoryPayload,
            chat_id: newChatId,
            response_status: 'success' as 'success' | 'error' | 'pending',
            final_response:
               JSON.stringify({
                  alert_id: alertSaveResponse.alert_id,
                  alert_name: alertSaveResponse.alert_name,
                  alert_description: alertSaveResponse.alert_description,
                  options: options,
                  alert_setup_status: 'success',
                  meta_data: { ...agentResponse },
               }) || '',
            session_summary: '',
            response_time: responseTime,
         };

         const updateFeatureUsagePayload = {
            client_id: client_id || '',
            user_id: user_id || '',
            feature_name: 'alerting_agent',
            feature_type: 'agent',
         };

         await addToSessionHistory(updatedSessionHistoryPayload);
         await refetchSessionHistory();
         await refetchSessionAlerts();
         await updateFeatureUsage(updateFeatureUsagePayload);
      } else if (agentResponse.status === 'missing') {
         const updatedSessionHistoryPayload = {
            ...addToSessionHistoryPayload,
            chat_id: newChatId,
            response_status: 'success' as 'success' | 'error' | 'pending',
            final_response:
               JSON.stringify({
                  alert_id: '',
                  alert_name: '',
                  alert_description: '',
                  alert_setup_status: 'missing',
                  options: options,
                  meta_data: { ...agentResponse },
               }) || '',
            session_summary: '',
            response_time: responseTime,
         };

         const updateFeatureUsagePayload = {
            client_id: client_id || '',
            user_id: user_id || '',
            feature_name: 'alerting_agent',
            feature_type: 'agent',
         };

         await addToSessionHistory(updatedSessionHistoryPayload);
         await refetchSessionHistory();
         await updateFeatureUsage(updateFeatureUsagePayload);
      } else {
         const updatedSessionHistoryPayload = {
            ...addToSessionHistoryPayload,
            chat_id: newChatId,
            response_status: 'error' as 'success' | 'error' | 'pending',
            final_response: response || '',
            session_summary: '',
            response_time: responseTime,
         };

         await addToSessionHistory(updatedSessionHistoryPayload);
         await refetchSessionHistory();
      }
   };

   useEffect(() => {
      const giveSuggestions = () => {
         const randomFourSuggestions = getSmartSuggestions(
            connectionDetails,
            QUESTION_SUGGESTIONS,
         );

         return randomFourSuggestions;
      };

      setSuggestions(giveSuggestions());
   }, []);

   useEffect(() => {
      setTimeout(() => {
         messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 0);
   }, [currentSessionHistory]);

   return (
      <div
         className={`w-full h-full flex flex-col items-center ${!currentSessionHistory || currentSessionHistory.length < 1 ? 'justify-center' : ''}`}
      >
         <div
            className={`w-[95%] max-w-[900px] flex flex-col items-center gap-[5px] overflow-auto pt-[0px] md:pt-[20px] ${currentSessionHistory && currentSessionHistory.length > 0 ? 'h-full' : ''}`}
         >
            {(!currentSessionHistory ||
               currentSessionHistory?.length === 0) && (
               <>
                  <div className='w-40 h-40 overflow-hidden'>
                     <img
                        src={alertingAgentIcon}
                        alt='cropped'
                        className='w-36'
                     />
                  </div>
                  <p className='w-full text-center font-bold text-[16px] md:text-[20px] text-black p-[10px] max-w-[750px]'>
                     Alerting Agent
                  </p>
                  <p
                     className='w-full text-center text-[12px] md:text-[16px] text-black p-[10px] max-w-[500px]'
                     onClick={() => setPrompt('')}
                  >
                     Hello! Welcome to the{' '}
                     <span className='font-bold'>Alerting Agent</span>. This
                     tool helps you monitor key metrics and get notified when
                     something important happens. You can ask questions, set up
                     custom alerts, or explore insights across your platforms.
                     Let's get started!
                     <p className='text-center text-[12px] md:text-[14px] text-black mt-3 md:mt-5 mb-2'>
                        👉 Try a sample question to get started.
                     </p>
                  </p>
                  <div className='flex flex-wrap gap-2 items-center justify-center max-w-[900px] text-[12px] md:text-[14px] mx-4 mb-10'>
                     {suggestions.map((text, idx) => (
                        <>
                           <Tooltips
                              content={text}
                              className='bg-gray-500 text-white w-max max-w-[400px] fit-c para5'
                           >
                              <div
                                 className='flex-1 w-full md:w-[180px] md:min-h-[95px] max-h-[95px] border-[1px] border-gray-300 text-left rounded-[15px] px-[15px] py-[5px] \ cursor-pointer shadow-md font-semibold bg-white hover:bg-gray-100 hover:text-black line-clamp-4 overflow-hidden text-ellipsis'
                                 key={idx}
                                 onClick={() =>
                                    void handleSampleQuestionClick(text)
                                 }
                              >
                                 {text}
                              </div>
                           </Tooltips>
                        </>
                     ))}
                  </div>
               </>
            )}
            {currentSessionHistory &&
               currentSessionHistory.length > 0 &&
               currentSessionAlerts && (
                  <div className='w-full flex-1 justify-center overflow-y-auto'>
                     <div className='w-full flex flex-col justify-start max-w-[950px] overflow-auto'>
                        <div className='message'>
                           <AlertingAgentChat
                              currentSessionAlerts={currentSessionAlerts}
                              currentSessionChats={currentSessionHistory}
                           />
                           <div ref={messagesEndRef}></div>
                        </div>
                     </div>
                  </div>
               )}
         </div>
         <div className='flex flex-col items-center w-[95%] max-w-[900px] my-[10px] md:my-[20px] flex-nowrap justify-between gap-2 bg-white'>
            <InputGroup width='100%'>
               <Input
                  className='!text-[12px] md:!text-[16px] font-semibold'
                  placeholder='Set an alert'
                  height='50px'
                  value={prompt}
                  onChange={handlePromptChange}
                  onKeyDown={(e) => void handleKeyDown(e)}
                  boxShadow='0px 0px 10px 3px rgba(0,0,0,0.15)'
               />
               <InputRightElement mr='5px' mt='5px'>
                  <IconButton
                     aria-label='Send'
                     icon={<IoSend />}
                     background='#437eeb'
                     color='white'
                     disabled={
                        isPendingAddToSessionHistory ||
                        isPendingCreateAlert ||
                        isFetchingSessionHistory ||
                        isFetchingSessionAlerts ||
                        (currentSessionHistory &&
                           currentSessionHistory?.filter(
                              (chat) => chat?.response_status === 'pending',
                           ).length > 0)
                     }
                     sx={{
                        _hover: {
                           background: '#437eeb',
                           cursor: 'pointer',
                           transform: 'scale(1.01)',
                           transition: 'transform 0.2s',
                        },
                     }}
                     onClick={() => void handleSendPrompt()}
                  />
               </InputRightElement>
            </InputGroup>
            <p className='text-[10px] md:text-[12px] text-gray-500 text-center'>
               Agent can make mistakes. Please double-check responses.
            </p>
         </div>
      </div>
   );
};

export default AlertingAgent;
