/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { useState, useEffect, useCallback } from 'react';
import telegramServices from '../services/telegram';
import Config from '../../../config';

export interface User {
   client_id: string;
   email: string;
}

interface ConnectionDetails {
   ok: boolean;
   message: string;
}

interface TelegramConnection {
   connectionDetails: ConnectionDetails;
   isFetching: boolean;
   isTryConnect: boolean;
   error: string | null;
   navigateToTelegram: () => void;
   disconnect: () => Promise<void>;
}

function encodeToBase64(stringToBeEncoded: string): string {
   const buff = Buffer.from(stringToBeEncoded, 'utf8');
   const base64 = buff.toString('base64').replace(/=+$/, ''); // Remove ending '=';
   return base64;
}

function useTelegramConnectionDetails(user: User): TelegramConnection {
   const [connectionDetails, setConnectionDetails] =
      useState<ConnectionDetails>({
         ok: false,
         message: '',
      });
   const [isFetching, setIsFetching] = useState<boolean>(false);
   const [isTryConnect, setIsTryConnect] = useState<boolean>(false);
   const [error, setError] = useState<string | null>(null);

   const checkExistingUser = useCallback(async () => {
      try {
         if (!Object.keys(user).length) return;
         setIsFetching(true);
         const { data } = await telegramServices.checkExistingUser(
            user.client_id,
            user.email,
         );
         setConnectionDetails(data as ConnectionDetails); // Type assertion here
      } catch (err) {
         const error = err as any;
         setError((error?.message as string) || 'Something went wrong');
      } finally {
         setIsFetching(false);
      }
   }, [user.client_id, user.email]);

   useEffect(() => {
      void checkExistingUser();
   }, [checkExistingUser]);

   useEffect(() => {
      let timer: NodeJS.Timeout;

      if (isTryConnect) {
         timer = setInterval(() => {
            console.log('TRYING TO CONNECT, REFETCHING...');
            if (connectionDetails.ok) {
               clearInterval(timer);
               console.log('CANCELLING THE TIMER AS USER REGISTERED');
            } else {
               void checkExistingUser();
            }
         }, 50000);
      }

      return () => {
         if (timer) clearInterval(timer);
      };
   }, [isTryConnect, connectionDetails.ok, checkExistingUser]);

   function navigateToTelegram() {
      setIsTryConnect(true);
      const base64 = encodeToBase64(`${user.client_id}_${user.email}`);
      const botKey = Config.VITE_TELEGRAM_BOT_KEY;

      if (!botKey) {
         alert('Could not connect');
         return;
      }

      window.open(`https://t.me/${botKey}?start=${base64}`, '_blank');
   }

   async function disconnect() {
      try {
         await telegramServices.disconnectUser(user.client_id);
         setConnectionDetails((prev) => ({
            ...prev,
            ok: false,
            message: '',
         }));
      } catch (err) {
         console.log('err occurred', err);
      }
   }

   return {
      connectionDetails,
      isFetching,
      isTryConnect,
      error,
      navigateToTelegram,
      disconnect,
   };
}

export default useTelegramConnectionDetails;
