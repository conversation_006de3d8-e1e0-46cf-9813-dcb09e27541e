import React from 'react';
import {
   Popover,
   PopoverTrigger,
   Portal,
   PopoverContent,
   PopoverBody,
   PopoverFooter,
   PlacementWithLogical,
} from '@chakra-ui/react';
import { useDispatch } from 'react-redux';
import { closePopover } from '../../store/reducer/popover-reducer';

interface Props {
   children: React.ReactNode;
   closeOnBlur?: boolean | undefined;
   parentClassName?: string;
   trigger: React.ReactNode;
   footer?: React.ReactNode | null;
   placement?: PlacementWithLogical | undefined;
}

function PopoverWrapper(props: Props) {
   const {
      closeOnBlur,
      trigger,
      children,
      parentClassName,
      footer,
      placement,
   } = props;
   const dispatch = useDispatch();

   function onClose() {
      dispatch(closePopover());
   }

   return (
      <Popover
         placement={placement}
         closeOnBlur={closeOnBlur}
         onClose={onClose}
      >
         <PopoverTrigger>{trigger}</PopoverTrigger>
         <Portal>
            <PopoverContent className={parentClassName}>
               <PopoverBody>{children}</PopoverBody>

               {footer && <PopoverFooter>{footer}</PopoverFooter>}
            </PopoverContent>
         </Portal>
      </Popover>
   );
}

export default PopoverWrapper;
