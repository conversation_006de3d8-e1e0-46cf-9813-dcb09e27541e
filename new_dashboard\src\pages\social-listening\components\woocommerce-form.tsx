/* eslint-disable @typescript-eslint/no-misused-promises */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import image from '../images/integrations/woocommerce.png';
import Input from './Input';
import StoreConnectionSteps from './woocommerce-steps';
import CommerceIntegrationLayout from './commerce-integration-layout';

import { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { connectDisconnectToWoocommerce } from '../utils';
import { channelNames, woocommerceIntegrationSteps } from '../utils/constant';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { AuthUser } from '../../../types/auth';

import './woocommerce-form.scss';

interface FormFields {
   brandName: string;
   storeUrl: string;
   consumerKey: string;
   consumerSecret: string;
}
interface GeneralInformationProps {
   onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
   brandName: string;
}

const GeneralInformation: React.FC<GeneralInformationProps> = ({
   onChange,
   brandName,
}) => (
   <div className='general-information'>
      <div className='general-information__headings'>
         <h5 className='title'>General Information</h5>
         <p className='description'>
            Please fill in the below details to connect your WooCommerce store:
         </p>
      </div>
      <form className='general-information__form'>
         <div className='inputs'>
            <Input
               id='brand-name'
               label='Communication Brand Name'
               name='brandName'
               value={brandName}
               onChange={onChange}
            />
         </div>
      </form>
   </div>
);
interface SellerPanelProps {
   onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
   trying: boolean;
   onConnect: (e: React.FormEvent<HTMLFormElement>) => void;
   storeUrl: string;
   consumerKey: string;
   consumerSecret: string;
   apiError: { success: boolean; message: string } | null;
}

const SellerPanel: React.FC<SellerPanelProps> = ({
   onChange,
   trying,
   onConnect,
   storeUrl,
   consumerKey,
   consumerSecret,
   apiError,
}) => (
   <div className='seller-panel'>
      <div className='seller-panel__headings'>
         <h5 className='title'>Seller Panel</h5>
         <p className='description'>
            Please provide the following credentials for WooCommerce:
         </p>
      </div>
      <form className='seller-panel__form' onSubmit={onConnect}>
         <div className='inputs'>
            <Input
               id='store-url'
               label='Store URL'
               placeholder='Enter your store URL...'
               name='storeUrl'
               value={storeUrl}
               onChange={onChange}
            />
            <Input
               id='consumer-key'
               label='Consumer Key'
               placeholder='eg. xxxxxxxx'
               name='consumerKey'
               value={consumerKey}
               onChange={onChange}
            />
            <Input
               id='consumer-secret'
               label='Consumer Secret'
               placeholder='eg. xxxxxxxx'
               name='consumerSecret'
               value={consumerSecret}
               onChange={onChange}
            />
         </div>
         {apiError && (
            <div
               className={`api-response ${apiError.success ? 'api-response__success' : 'api-response__error'}`}
            >
               <h3 className='api-response__message'>{apiError.message}</h3>
            </div>
         )}
         <button
            disabled={trying}
            className='commerce-integration__button'
            type='submit'
         >
            {trying ? 'Connecting...' : 'Connect'}
         </button>
      </form>
   </div>
);

const WoocommerceForm: React.FC = () => {
   const navigate = useNavigate();
   const [trying, setTrying] = useState(false);
   const [apiError, setApiError] = useState<{
      success: boolean;
      message: string;
   } | null>(null);
   const [formFields, setFormFields] = useState<FormFields>({
      brandName: '',
      storeUrl: '',
      consumerKey: '',
      consumerSecret: '',
   });
   const client_id = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   )?.client_id;

   const handleChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
         const { name, value } = e.target;
         setFormFields((prev) => ({ ...prev, [name]: value }));
      },
      [],
   );

   const handleConnect = useCallback(
      async (e: React.FormEvent<HTMLFormElement>) => {
         if (!client_id) return;
         e.preventDefault();

         const { consumerKey, consumerSecret, storeUrl } = formFields;

         try {
            setTrying(true);
            await connectDisconnectToWoocommerce({
               channel_name: channelNames.WOOCOMMERCE,
               client_id,
               consumer_key: consumerKey,
               consumer_secret: consumerSecret,
               store_url: storeUrl,
               isConnect: true,
            });
            setFormFields({
               brandName: '',
               storeUrl: '',
               consumerKey: '',
               consumerSecret: '',
            });
            setApiError({
               success: true,
               message: 'Connection Established, Redirecting...',
            });
            setTimeout(() => {
               navigate('/integrations');
            }, 3000);
         } catch (err) {
            const error = err as any;
            const errMessage =
               error.response?.data?.error || 'Error connecting to WooCommerce';
            setApiError({
               success: false,
               message: errMessage,
            });
         } finally {
            setTrying(false);
         }
      },
      [formFields, history],
   );

   useEffect(
      () => () => {
         setFormFields({
            brandName: '',
            storeUrl: '',
            consumerKey: '',
            consumerSecret: '',
         });
         setApiError(null);
      },
      [],
   );

   const leftContent = (
      <StoreConnectionSteps steps={woocommerceIntegrationSteps} />
   );

   return (
      <CommerceIntegrationLayout leftContent={leftContent} image={image}>
         <GeneralInformation
            onChange={handleChange}
            brandName={formFields.brandName}
         />
         <SellerPanel
            onChange={handleChange}
            trying={trying}
            onConnect={handleConnect}
            storeUrl={formFields.storeUrl}
            consumerKey={formFields.consumerKey}
            consumerSecret={formFields.consumerSecret}
            apiError={apiError}
         />
      </CommerceIntegrationLayout>
   );
};

export default WoocommerceForm;
