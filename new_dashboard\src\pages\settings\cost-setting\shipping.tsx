import {
   Button,
   <PERSON>lapse,
   Flex,
   Heading,
   Radio,
   RadioGroup,
   Text,
   useColorModeValue,
   useOutsideClick,
} from '@chakra-ui/react';
import { useRef, useState } from 'react';
import { AiOutlineInfoCircle } from 'react-icons/ai';
import { SHIPPING_STRINGS } from '../../../utils/strings/cfo';
import { TbDownload } from 'react-icons/tb';
import { FiPlusCircle } from 'react-icons/fi';
import { useDispatch } from 'react-redux';
import { openModal } from '../../../store/reducer/modal-reducer';
import { modalTypes } from '../../../components/modals/modal-types';
import { CFOKeys } from '../../dashboard/utils/query-keys';
import endPoints, { OutNewId } from '../../../api/service/cfo';
import { useApiMutation } from '../../../hooks/react-query-hooks';
import { useAppSelector } from '../../../store/store';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { setshippingCost } from '../../../store/reducer/cfo-reducer';
import ShippingProfilesList from './shipping-profiles';
function Shipping() {
   const { shippingCost } = useAppSelector((state) => state.cfo);
   const [shipping, setshipping] = useState<string>(
      shippingCost.is_default !== null
         ? shippingCost.is_default
            ? '2'
            : '1'
         : '',
   );
   const dispatch = useDispatch();
   const shipppingCostRef = useRef(null);
   const handleShippingValue = (nextValue: string) => {
      setshipping(nextValue);
   };
   useOutsideClick({
      ref: shipppingCostRef,
      handler: () => {
         if (
            (shipping == '2' && !shippingCost.is_default) ||
            (shippingCost.is_default && shipping == '1')
         ) {
            updateFixedRate({
               clientId: LocalStorageService.getItem(Keys.ClientId) as string,
               is_default:
                  shipping == '2' ? true : shipping == '1' ? false : null,
               id: shippingCost.id || null,
            });

            dispatch(
               setshippingCost({
                  ...shippingCost,
                  is_default: shipping === '2',
               }),
            );
         }
      },
   });
   const handleShippingProfileOpen = () => {
      dispatch(
         openModal({
            modalType: modalTypes.SHIPPING_PROFILE,
         }),
      );
   };
   const handleImportCostOpen = () => {
      dispatch(
         openModal({
            modalType: modalTypes.IMPORT_COST_MODAL,
         }),
      );
   };
   const handleSuccess = (data: OutNewId[]) => {
      if (data) {
         dispatch(
            setshippingCost({
               is_default:
                  shipping == '2' ? true : shipping == '1' ? false : null,
               id: data[0].new_id || null,
               clientId: LocalStorageService.getItem(Keys.ClientId) as string,
            }),
         );
      }
   };
   const { mutate: updateFixedRate } = useApiMutation({
      queryKey: [CFOKeys.upsertShippingCost],
      mutationFn: endPoints.upsertShippingCost,
      onSuccessHandler: handleSuccess,
   });
   return (
      <Flex
         flexDirection={'column'}
         border={'1px solid #C2CBD4'}
         p={4}
         borderRadius={'8px'}
         gap={3}
         m={3}
      >
         <RadioGroup
            ref={shipppingCostRef}
            onChange={handleShippingValue}
            value={shipping}
            color={useColorModeValue('black', 'white')}
            defaultValue={
               shippingCost.is_default !== null
                  ? shippingCost.is_default
                     ? '2'
                     : '1'
                  : ''
            }
            display={'flex'}
            flexDirection={'column'}
            gap={4}
         >
            <Radio className='panel' fontSize={'16px'} value='1'>
               {SHIPPING_STRINGS.shippingCharges}
            </Radio>

            <Text fontSize={'14px'} className='panel'>
               {SHIPPING_STRINGS.shippingChargesDesc}
            </Text>
            <Radio fontSize={'16px'} value='2' className='panel'>
               {SHIPPING_STRINGS.defaultShipping}
            </Radio>
            <Text
               className='defaultShippping'
               display={'flex'}
               gap={2}
               fontSize={'14px'}
               p={4}
               border={'1px solid #DDE9FF'}
               borderRadius={'6px'}
               backgroundColor={'#DDE9FF'}
               // color={'#062E77'}
            >
               <AiOutlineInfoCircle /> {SHIPPING_STRINGS.defaultShippingDesc}
            </Text>
            <Collapse in={shipping === '2'} animateOpacity>
               <Flex
                  direction={'column'}
                  gap={5}
                  p={4}
                  className='order-dynamic'
               >
                  <Flex justifyContent={'space-between'} alignItems={'center'}>
                     <Flex direction={'column'} gap={3}>
                        <Heading
                           fontSize={'16px'}
                           fontWeight={'500'}
                           className='panel'
                        >
                           {SHIPPING_STRINGS.orderId}
                        </Heading>
                        <Text fontSize={'14px'} className='panel'>
                           {SHIPPING_STRINGS.orderIdDesc}
                        </Text>
                     </Flex>
                     <Button
                        leftIcon={<TbDownload />}
                        fontSize={'14px'}
                        background={'none'}
                        border={'1px solid #C2CBD4'}
                        borderRadius={'7px'}
                        padding={'6px 10px'}
                        height={'fit-content'}
                        onClick={handleImportCostOpen}
                     >
                        Import costs
                     </Button>
                  </Flex>
                  <Flex justifyContent={'space-between'} alignItems={'center'}>
                     <Flex direction={'column'} gap={3}>
                        <Heading
                           fontSize={'16px'}
                           fontWeight={'500'}
                           className='panel'
                        >
                           {SHIPPING_STRINGS.dynamicRate}
                        </Heading>
                        <Text fontSize={'14px'} className='panel'>
                           {SHIPPING_STRINGS.dynamicRateDesc}
                        </Text>
                     </Flex>
                     <Button
                        onClick={handleShippingProfileOpen}
                        leftIcon={<FiPlusCircle />}
                        fontSize={'14px'}
                        background={'none'}
                        border={'1px solid #C2CBD4'}
                        borderRadius={'7px'}
                        padding={'6px 10px'}
                        height={'fit-content'}
                     >
                        Create a Shipping Profile
                     </Button>
                  </Flex>
                  <ShippingProfilesList />
               </Flex>
            </Collapse>
         </RadioGroup>
      </Flex>
   );
}

export default Shipping;
