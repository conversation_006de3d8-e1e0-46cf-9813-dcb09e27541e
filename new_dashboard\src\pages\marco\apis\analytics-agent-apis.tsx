import React from 'react';
import { AuthUser } from '@/types/auth';
import { AppDispatch, RootState, useAppSelector } from '@/store/store';
import { useApiMutation, useApiQuery } from '@/hooks/react-query-hooks';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import analyticAgentAPI, {
   AnalyticsAgentChat,
   ChunkData,
} from '../../../api/service/agentic-workflow/analytics-agent';
import analyticsAgentAPI from '../../../api/service/agentic-workflow/analytics-agent';
import { useMutation } from '@tanstack/react-query';
import Config from '@/config';
import { handleStreamedResponse } from '../utils/analytics-agent/helpers';

interface StartStreamPayload {
   client_id: string;
   user_id: string;
   session_id: string;
   text: string;
   message_history: { role: string; content: string }[];
   context_variables: Record<string, string>;
}

export const useFetchSessionHistoryQuery = () => {
   const { currentSessionID } = useAppSelector((state) => state.analyticsAgent);

   const { client_id, user_id } =
      LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails) ?? {};

   const fetchSessionHistoryPayload = {
      client_id: client_id || '',
      session_id: currentSessionID || '',
      user_id: user_id || '',
   };

   return useApiQuery({
      queryKey: ['sessionHistory', currentSessionID],
      queryFn: () =>
         analyticsAgentAPI.fetchSessionHistory(fetchSessionHistoryPayload),
      enabled: !!currentSessionID,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      refetchOnMount: false,
   });
};

export const useAddToSessionHistoryMutation = () => {
   return useApiMutation({
      queryKey: ['addToSessionHistory'],
      mutationFn: analyticsAgentAPI.addToSessionHistory,
   });
};

export const useFetchFeatureUsageQuery = () => {
   const { client_id, user_id } =
      LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails) ?? {};

   const fetchFeatureUsagePayload = {
      client_id: client_id || '',
      user_id: user_id || '',
      feature_name: 'analytics_agent',
      feature_type: 'agent',
   };

   return useApiQuery({
      queryKey: ['featureUsage', 'analytics-agent'],
      queryFn: () =>
         analyticsAgentAPI.fetchUserFeatureUsage(fetchFeatureUsagePayload),
      enabled: !!client_id && !!user_id,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      refetchOnMount: false,
   });
};

export const useTrackFeatureUsageMutation = () => {
   return useApiMutation({
      queryKey: ['trackFeatureUsage', 'analytics-agent'],
      mutationFn: analyticsAgentAPI.trackFeatureUsage,
   });
};

export const useFetchSessionInsights = () => {
   const { currentSessionID } = useAppSelector((state) => state.analyticsAgent);

   const { client_id, user_id } =
      LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails) ?? {};

   const fetchSessionInsightsPayload = {
      client_id: client_id || '',
      session_id: currentSessionID || '',
      user_id: user_id || '',
   };

   return useApiQuery({
      queryKey: ['sessionInsights', currentSessionID],
      queryFn: () =>
         analyticsAgentAPI.fetchSessionInsightsByID(
            fetchSessionInsightsPayload,
         ),
      enabled: !!currentSessionID,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      refetchOnMount: false,
   });
};

export const useAddInsightsToSessionHistoryMutation = () => {
   return useApiMutation({
      queryKey: ['addInsightsToSessionHistory'],
      mutationFn: analyticsAgentAPI.addInsightsToSessionHistory,
   });
};

export const useLikeDislikeChatMutation = () => {
   return useApiMutation({
      queryKey: ['likeDislikeChat'],
      mutationFn: analyticsAgentAPI.likeDislikeChat,
   });
};

export const useUpdateChatRewrittenMutation = () => {
   return useApiMutation({
      queryKey: ['updateChatRewritten'],
      mutationFn: analyticsAgentAPI.updateChatRewritten,
   });
};

export const useUpdateChatCopiedMutation = () => {
   return useApiMutation({
      queryKey: ['updateChatCopied'],
      mutationFn: analyticsAgentAPI.updateChatCopied,
   });
};

export const useUpdateChatFeedbackMutation = () => {
   return useApiMutation({
      queryKey: ['updateChatFeedback'],
      mutationFn: analyticsAgentAPI.updateChatFeedback,
   });
};

export const useStreamResponse = (
   dispatch: AppDispatch,
   getState: () => RootState,
) => {
   const resolveRef = React.useRef<((chunks: ChunkData[]) => void) | null>(
      null,
   );

   const { mutateAsync: _startStream, isPending: isStreaming } = useMutation({
      mutationFn: async (payload: StartStreamPayload) => {
         const response = await fetch(
            `${Config.VITE_XI_AGENT_API}/query_stream`,
            {
               method: 'POST',
               body: JSON.stringify(payload),
               headers: {
                  'Content-Type': 'application/json',
               },
            },
         );

         if (!response.body)
            throw new Error('Readable stream not supported by your browser');

         return response.body.getReader();
      },
      onSuccess: async (reader) => {
         const decoder = new TextDecoder('utf-8');
         const allChunks: ChunkData[] = [];
         let buffer = '';

         const read = async () => {
            try {
               const { done, value } = await reader.read();

               if (done) {
                  if (resolveRef.current) {
                     resolveRef.current(allChunks);
                  }
                  return;
               }

               buffer += decoder.decode(value, { stream: true });
               const lines = buffer.split('\n');
               buffer = lines.pop() || '';

               for (const line of lines) {
                  if (!line.trim()) continue;
                  try {
                     const parsed = JSON.parse(line) as ChunkData;
                     allChunks.push(parsed);

                     handleStreamedResponse(parsed, dispatch, getState);
                  } catch (err) {
                     console.warn('Invalid JSON chunk', err, line);
                  }
               }

               await read();
            } catch (err) {
               console.error('Stream read error:', err);
               if (resolveRef.current) {
                  resolveRef.current(allChunks);
               }
            }
         };

         await read();
      },
   });

   const startStream = (payload: StartStreamPayload): Promise<ChunkData[]> => {
      return new Promise<ChunkData[]>((resolve) => {
         resolveRef.current = resolve;
         void _startStream(payload);
      });
   };

   return {
      startStream,
      isStreaming,
   };
};

export const useFetchAnalyticsHistoryQuery = () => {
   const { client_id, user_id } =
      LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails) || {};

   const { currentAgent } = useAppSelector((state) => state.marco);
   const { currentPage } = useAppSelector((state) => state.analyticsAgent);

   return useApiQuery<AnalyticsAgentChat[]>({
      queryKey: ['analytics-chat-history', currentAgent, String(currentPage)],
      queryFn: async () =>
         analyticAgentAPI.fetchAllSessionsHistory({
            client_id: client_id || '',
            user_id: user_id || '',
            page: currentPage,
         }),
      enabled: !!client_id && !!user_id && currentAgent === 'analytics-agent',
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
   });
};
