import { useRef, useState } from 'react';
import { Navigate, useLocation, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../store/store';

import {
   Flex,
   Text,
   Button,
   FormControl,
   Box,
   useToast,
   Avatar,
   useColorModeValue,
} from '@chakra-ui/react';
import { Input, FormErrorMessage, TableContainer } from '@chakra-ui/react';
import { Table, Thead, Tbody, Tr, Th, Td, Select } from '@chakra-ui/react';

import { addEditUserPageStrings } from '../../utils/strings/user-managament-strings';
import { setSelectedUser } from '../../store/reducer/user-management-reducer';
import { Keys, LocalStorageService } from '../../utils/local-storage';
import userManagementEndpoints, {
   AllUserDetails,
   CreateUpdateUserPayload,
} from '../../api/service/users';

import './table.scss';
import { useApiMutation } from '../../hooks/react-query-hooks';
import { userManagementKeys } from '../../utils/strings/query-keys';
import { retrieveOptions } from '../onboarding/utils/helper';
import {
   decodeImage,
   encodeImage,
} from '../../utils/helpers/image-upload-heplers';

interface UserDetails {
   full_name: string;
   email_address: string;
   country: string;
   language: string;
   user_role: 'Admin' | 'Contributor';
}

const AddEditUserPage = () => {
   const toast = useToast();
   const navigate = useNavigate();
   const location = useLocation();
   const dispatch = useAppDispatch();

   const fileInputRef = useRef<HTMLInputElement>(null);

   const { masterList } = useAppSelector((state) => state.onboarding);
   const { allUsers, selectedUser } = useAppSelector(
      (state) => state.userManagement,
   );

   const [userDetails, setUserDetails] = useState<UserDetails>(
      selectedUser
         ? {
              full_name: selectedUser.full_name,
              email_address: selectedUser.email_address,
              country: selectedUser.country,
              language: selectedUser.language,
              user_role: selectedUser.user_role,
           }
         : {
              full_name: '',
              email_address: '',
              country: '',
              language: '',
              user_role: 'Contributor',
           },
   );
   const [imageUrl, setImageUrl] = useState<string>(
      'https://bit.ly/broken-link',
   );
   const [errorMessage, setErrorMessage] = useState<string>('');

   const countryOptions: string[] | undefined = retrieveOptions(
      masterList,
      'Country',
   );

   const showToast = (
      title: string,
      description: string,
      status: 'success' | 'error' | 'warning',
   ) => {
      toast({
         title,
         description,
         status,
         duration: 5000,
         isClosable: true,
      });
   };

   const createUserMutation = useApiMutation({
      queryKey: [userManagementKeys.createUser],
      mutationFn: userManagementEndpoints.createUser,
      onSuccessHandler: () => {
         toast({
            title: 'Success',
            description: addEditUserPageStrings.roleConfirmation,
            status: 'success',
            duration: 5000,
            isClosable: true,
         });

         navigate('/user/manage-users');
      },
      onError: () => {
         toast({
            title: 'Failed',
            description: addEditUserPageStrings.createUserFailed,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      },
   });

   const updateUserMutation = useApiMutation({
      queryKey: [userManagementKeys.updateUser],
      mutationFn: userManagementEndpoints.updateUser,
      onSuccessHandler: () => {
         toast({
            title: 'Success',
            description: addEditUserPageStrings.updateUserSuccess,
            status: 'success',
            duration: 5000,
            isClosable: true,
         });

         navigate('/user/manage-users');
      },
      onError: () => {
         toast({
            title: 'Failed',
            description: addEditUserPageStrings.updateUserFailed,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      },
   });

   const validateURLs = (name: string, value: string) => {
      if (name !== 'email_address') return;

      if (!addEditUserPageStrings.emailRegex.test(value)) {
         setErrorMessage(addEditUserPageStrings.incorrectURL);
      } else {
         setErrorMessage('');
      }
   };

   const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = e.target.files;

      if (files && files.length > 0) {
         void handleUpload(files[0]);
      }
   };

   const handleUpload = async (selectedFile: File) => {
      if (!selectedFile) {
         showToast(
            'No File Selected',
            'Please select an image file to upload.',
            'warning',
         );
         return;
      }
      try {
         const response = await encodeImage(selectedFile);

         if (response) {
            const decoded = await decodeImage({ image_url: response.uri });

            if (!decoded) {
               showToast('Error', 'Failed to decode image.', 'error');
            } else {
               setImageUrl(decoded?.uri);
            }
         } else {
            showToast('Error', 'Failed to upload image.', 'error');
         }
      } catch (error) {
         showToast(
            'Error',
            'An error occurred while uploading image.',
            'error',
         );
      }
   };

   const handleInputChange = (
      event:
         | React.ChangeEvent<HTMLInputElement>
         | React.ChangeEvent<HTMLSelectElement>,
   ) => {
      const inputEvent = event as React.ChangeEvent<HTMLInputElement>;
      const { name, value } = inputEvent.target;
      setUserDetails((userDetails) => ({ ...userDetails, [name]: value }));
      validateURLs(name, value);
   };

   const handleDiscard = () => {
      navigate('/user/manage-users');
      dispatch(setSelectedUser(null));
   };

   const handleCreateUpdateUser = () => {
      if (!addEditUserPageStrings.emailRegex.test(userDetails.email_address)) {
         toast({
            title: 'Failed',
            description: addEditUserPageStrings.incorrectURL,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });

         return;
      }

      if (!userDetails.user_role || !userDetails.email_address) {
         toast({
            title: 'Failed',
            description: addEditUserPageStrings.requiredFields,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });

         return;
      }

      const createUpdateUserPayload: CreateUpdateUserPayload = {
         client_id: LocalStorageService.getItem(Keys.ClientId) as string,
         email_address: userDetails.email_address,
         cb_product_updates: 'Y',
         company_url: allUsers[0].company_url,
         country: userDetails.country,
         create_date: new Date().toISOString(),
         full_name: userDetails.full_name,
         language: userDetails.language,
         last_update_date: new Date().toISOString(),
         user_active: 'Y',
         user_role: userDetails.user_role,
         is_active: true,
         profile_image: imageUrl,
      };

      location.pathname === '/user/edit'
         ? updateUserMutation.mutate(createUpdateUserPayload)
         : createUserMutation.mutate({
              ...createUpdateUserPayload,
              user_confirmed: 'N',
           });
   };

   if (allUsers.length === 0) {
      return <Navigate replace to='/user/manage-users' />;
   }

   if (location.pathname === '/user/edit' && !selectedUser) {
      return <Navigate replace to='/user/manage-users' />;
   }

   return (
      <Box margin='70px 70px 0px 70px'>
         <Text
            fontSize='20px'
            fontWeight='bold'
            color={useColorModeValue('black', 'white')}
         >
            {addEditUserPageStrings.addUser}
         </Text>
         <Flex direction='column' margin='20px' width='500px' gap={8}>
            <Flex alignItems='center' justifyContent='space-between'>
               <Flex alignItems='center' justifyContent='space-between' gap={4}>
                  <Text
                     fontSize='14px'
                     fontWeight={500}
                     color={useColorModeValue('black', 'white')}
                  >
                     {addEditUserPageStrings.userProfilePhoto}
                  </Text>
                  <Avatar
                     name={userDetails.full_name}
                     src={imageUrl}
                     size='lg'
                  />
               </Flex>
               <Button
                  variant='outline'
                  onClick={() => fileInputRef?.current?.click()}
               >
                  <Text
                     fontSize='14px'
                     color={useColorModeValue('black', 'white')}
                  >
                     {addEditUserPageStrings.chooseImage}
                  </Text>
                  <Input
                     hidden
                     ref={fileInputRef}
                     type='file'
                     accept='image/*'
                     onChange={handleImageChange}
                  />
               </Button>
            </Flex>
            <FormControl>
               <Input
                  variant='flushed'
                  placeholder={addEditUserPageStrings.fullName}
                  fontSize='14px'
                  name='full_name'
                  value={userDetails.full_name}
                  onChange={handleInputChange}
                  backgroundColor={useColorModeValue(
                     'white',
                     'var(--controls)',
                  )}
               />
            </FormControl>
            <FormControl
               id='email_address'
               isInvalid={!!errorMessage}
               backgroundColor={useColorModeValue('white', 'var(--controls)')}
            >
               <Input
                  variant='flushed'
                  placeholder={`${addEditUserPageStrings.emailAddress} *`}
                  fontSize='14px'
                  name='email_address'
                  isDisabled={selectedUser !== null}
                  value={userDetails.email_address}
                  onChange={handleInputChange}
               />
               {errorMessage && (
                  <FormErrorMessage>{errorMessage}</FormErrorMessage>
               )}
            </FormControl>
            <FormControl>
               <Select
                  variant='flushed'
                  fontSize='14px'
                  value={userDetails.country}
                  name='country'
                  onChange={handleInputChange}
               >
                  <option value='' selected disabled>
                     Country
                  </option>
                  {countryOptions?.map((country: string) => (
                     <option key={country} value={country}>
                        {country}
                     </option>
                  ))}
               </Select>
            </FormControl>
            <FormControl>
               <Select
                  backgroundColor={useColorModeValue(
                     'white',
                     'var(--controls)',
                  )}
                  fontSize='14px'
                  name='language'
                  variant='flushed'
                  value={userDetails.language}
                  onChange={handleInputChange}
               >
                  <option value='' selected disabled>
                     Language
                  </option>
                  <option
                     key={addEditUserPageStrings.languageOptions.english}
                     value={addEditUserPageStrings.languageOptions.english}
                  >
                     {addEditUserPageStrings.languageOptions.english}
                  </option>
                  <option
                     key={addEditUserPageStrings.languageOptions.german}
                     value={addEditUserPageStrings.languageOptions.german}
                     disabled
                  >
                     {addEditUserPageStrings.languageOptions.german}
                  </option>
               </Select>
            </FormControl>
            <Flex direction='column' gap={4}>
               <Text mt={2} fontWeight={500} fontSize='14px'>
                  {addEditUserPageStrings.business}
               </Text>
               <TableContainer className='t-container'>
                  <Table variant='simple'>
                     <Thead>
                        <Tr>
                           <Th className='t-header-small'>
                              {addEditUserPageStrings.currentBusiness}
                           </Th>
                           <Th className='t-header-small'>
                              {`${addEditUserPageStrings.accessRole} *`}
                           </Th>
                        </Tr>
                     </Thead>
                     <Tbody>
                        <Tr>
                           <Td className='t-data'>
                              <Text fontSize='14px' fontWeight='bold'>
                                 {selectedUser?.company_url ||
                                    allUsers[0]?.company_url}
                              </Text>
                           </Td>
                           <Td className='t-data'>
                              <Select
                                 fontSize='14px'
                                 name='user_role'
                                 variant='flushed'
                                 onChange={handleInputChange}
                                 value={userDetails.user_role}
                                 disabled={
                                    allUsers?.filter(
                                       (user: AllUserDetails) =>
                                          user.user_role &&
                                          user.login_user_active === 'Y',
                                    ).length >= 5
                                 }
                              >
                                 <option value='' selected disabled>
                                    User Role
                                 </option>

                                 {/* Check if 2 Admins already exist */}
                                 {allUsers.filter(
                                    (user) =>
                                       user.user_role === 'Admin' &&
                                       user.role_is_active &&
                                       user.login_user_active === 'Y',
                                 ).length < 2 && (
                                    <option key='Admin' value='Admin'>
                                       {addEditUserPageStrings.admin}
                                    </option>
                                 )}

                                 {/* Check if 3 Contributors already exist */}
                                 {allUsers.filter(
                                    (user) =>
                                       user.user_role === 'Contributor' &&
                                       user.role_is_active &&
                                       user.login_user_active === 'Y',
                                 ).length < 3 && (
                                    <option
                                       key='Contributor'
                                       value='Contributor'
                                    >
                                       {addEditUserPageStrings.contributor}
                                    </option>
                                 )}
                              </Select>
                           </Td>
                        </Tr>
                     </Tbody>
                  </Table>
               </TableContainer>
            </Flex>

            <Flex alignItems='center' justifyContent='start' gap={4}>
               {location.pathname === '/user/edit' ? (
                  <Button
                     fontSize='14px'
                     colorScheme='blue'
                     isDisabled={
                        updateUserMutation.isPending ||
                        !userDetails.email_address ||
                        !userDetails.user_role ||
                        !!errorMessage
                     }
                     isLoading={
                        createUserMutation.isPending ||
                        updateUserMutation.isPending
                     }
                     onClick={handleCreateUpdateUser}
                  >
                     {addEditUserPageStrings.saveUser}
                  </Button>
               ) : (
                  <Button
                     fontSize='14px'
                     colorScheme='blue'
                     isDisabled={
                        createUserMutation.isPending ||
                        !userDetails.email_address ||
                        !userDetails.user_role ||
                        !!errorMessage
                     }
                     isLoading={
                        createUserMutation.isPending ||
                        updateUserMutation.isPending
                     }
                     onClick={handleCreateUpdateUser}
                  >
                     {addEditUserPageStrings.createUser}
                  </Button>
               )}

               <Button
                  fontSize='14px'
                  variant='outline'
                  isDisabled={
                     createUserMutation.isPending ||
                     updateUserMutation.isPending
                  }
                  onClick={handleDiscard}
               >
                  {addEditUserPageStrings.discard}
               </Button>
            </Flex>
         </Flex>
      </Box>
   );
};

export default AddEditUserPage;
