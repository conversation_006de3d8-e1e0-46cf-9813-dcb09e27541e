import { contentIdeationStrings } from '../../utils/strings/content-ideation';
import PillContentAccordian from './pill-content-accordian';
import './pill-content.scss';
import { TrendData } from './interface';
import { useAppSelector } from '../../store/store';
function PillContent({
   width,
   trends,
   handleBackToWrapper,
}: {
   width: number;
   trends: TrendData[];
   handleBackToWrapper: () => void;
}) {
   const { clickedPillId } = useAppSelector((state) => state.contentIdeation);
   return (
      <div className='PillContent' style={{ width }}>
         <h4 className='header'>
            {clickedPillId === 'Trends'
               ? contentIdeationStrings.trends
               : contentIdeationStrings.industryTrends}
         </h4>
         <div className='accordian-container'>
            {trends.length > 0 ? (
               trends.map((trend, index) => (
                  <PillContentAccordian
                     key={index}
                     title={trend.key}
                     description={trend.title}
                     postLink={trend.url}
                     snippet={trend.snippet}
                     handleBackToWrapper={handleBackToWrapper}
                  />
               ))
            ) : (
               <p>Loading...</p>
            )}
         </div>
      </div>
   );
}
export default PillContent;
