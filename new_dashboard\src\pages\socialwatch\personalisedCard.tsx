import React from 'react';
import { useAppDispatch } from '../../store/store';
import { openModal } from '../../store/reducer/modal-reducer';
import { modalTypes } from '../../components/modals/modal-types';
import { ImageTextData, PersonalisedData } from './interface';
import useIntegrationConnectionDetails from '../../hooks/use-integration-connection-details';

import './personalisedCard.scss';

interface Props {
   data: PersonalisedData | ImageTextData | null;
   handleBackToWrapper: () => void;
   width: number;
}

const PersonalisedCard: React.FC<Props> = ({
   data,
   handleBackToWrapper,
   width,
}) => {
   const dispatch = useAppDispatch();

   const { mediaTypes } = useIntegrationConnectionDetails();

   function handleOpen(idea: string) {
      const text = `Using this idea "${idea}", create an engaging and unique post that highlights my company's expertise and relevance in the industry. The content should seamlessly integrate my company name and align with industry trends. If the topic seems off-track, creatively adapt it to ensure it remains relevant to my company's focus and industry standards.`;
      dispatch(
         openModal({
            modalType: modalTypes.SOCIAL_MEDIA_CHANNEL_MODAL,
            modalProps:
               mediaTypes.length > 0
                  ? { options: mediaTypes, text, handleBackToWrapper }
                  : { undefined },
         }),
      );
   }
   const isPersonalisedData = (
      data: PersonalisedData | ImageTextData,
   ): data is PersonalisedData => {
      return data && (data as PersonalisedData).Instragram !== undefined;
   };
   const renderPersonalisedData = (data: PersonalisedData) => {
      return Object.entries(data).map(
         ([, ideas]: [unknown, string[]], index: number) => (
            <>
               <div key={index} className='platformContent' style={{ width }}>
                  <ul>
                     {ideas.map((idea: string, idx: number) => (
                        <div key={idx} className='personalised-ideas'>
                           <li>{idea}</li>
                           <button
                              className='generateBtn'
                              onClick={() => handleOpen(idea)}
                           >
                              Generate
                           </button>
                        </div>
                     ))}
                  </ul>
               </div>
            </>
         ),
      );
   };
   const renderImageTextData = (data: ImageTextData) => {
      console.log(data, 'ImageTextData');
      return (
         <div className='platformContent' style={{ width }}>
            <ul>
               {Object.values(data).map((idea: string, index: number) => (
                  <div key={index} className='personalised-ideas'>
                     <li>{idea}</li>
                     <button
                        className='generateBtn'
                        onClick={() => handleOpen(idea)}
                     >
                        Generate
                     </button>
                  </div>
               ))}
            </ul>
         </div>
      );
   };
   return (
      <div className='PersonalisedCard'>
         <div className='personalised-header'>
            {data && isPersonalisedData(data)
               ? 'Personal Content ideas'
               : 'Image based captions'}
         </div>
         {data && isPersonalisedData(data) && renderPersonalisedData(data)}
         {data && !isPersonalisedData(data) && renderImageTextData(data)}
      </div>
   );
};

export default PersonalisedCard;
