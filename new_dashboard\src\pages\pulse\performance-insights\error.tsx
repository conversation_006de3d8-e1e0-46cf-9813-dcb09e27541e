import '../pulse.scss';
import error from '../../../assets/icons/pulse/error.png';
import CustomdropDown from '../../../components/customDropdown/choose-dropdown';
import { errorPage } from '../../../utils/strings/pulse-strings';
import { Link } from 'react-router-dom';
import Dropdown from '../../../components/customDropdown/choose-dropdown';
export const dropdownOptions = [
   { value: '1', label: ' Last 1 Day' },
   { value: '7', label: ' Last  7 Days' },
   { value: '30', label: 'Last 30 Days' },
   { value: '60', label: 'Last 60 Days' },
];

const handleDaysSelect = (value: string) => {
   value;
};
const Errorpage: React.FC = () => (
   <div className='Pulse'>
      <div className='Main'>
         <h4>
            <b>Performance Insights </b>
         </h4>
      </div>
      <div className='overview'>
         <Link to=''>Overview</Link>
         <Link to=''>Tracked</Link>
         <Dropdown
            options={dropdownOptions}
            onSelect={handleDaysSelect}
            initialValue={'1'}
         />
      </div>
      <div className='Dropdowns'>
         <CustomdropDown
            options={dropdownOptions}
            onSelect={handleDaysSelect}
            initialValue={'Choose'}
         />
         <CustomdropDown
            options={dropdownOptions}
            onSelect={handleDaysSelect}
            initialValue={'Choose'}
         />
         <CustomdropDown
            options={dropdownOptions}
            onSelect={handleDaysSelect}
            initialValue={'Choose'}
         />
      </div>
      <img src={error} className='error'></img>
      <div className='text'>
         <h2>{errorPage.no_data_capture_msg}</h2>
         <h2>{errorPage.what_next_msg}</h2>
         <ul>
            <li>{errorPage.refresh_web_app}</li>
            <li>{errorPage.wait_15_relogin_msg} </li>
            <li>{errorPage.contact_support}</li>
         </ul>
      </div>
   </div>
);

export default Errorpage;
