/* eslint-disable @typescript-eslint/no-unsafe-assignment */
// import { useEffect, useState } from 'react';
// import { useNavigate } from 'react-router-dom';
// import { connectDisconnectToWoocommerce } from '../utils';
// import { channelNames } from '../utils/constant';
// import { Keys, LocalStorageService } from '../../../utils/local-storage';
// import { AuthUser } from '../../../types/auth';

import Card from './Card';
import image from '../images/integrations/woocommerce.png';
// import endPoints from '../apis/agent';

// interface ConnectionDetails {
//    is_active: boolean;
// }

// interface ApiResponse {
//    data: {
//       details: ConnectionDetails;
//    };
// }

const Woocommerce: React.FC = () => {
   // const navigate = useNavigate();

   // const client_id = LocalStorageService.getItem<AuthUser>(
   //    Keys.FlableUserDetails,
   // )?.client_id;

   // const [isConnected, setIsConnected] = useState<boolean>(false);
   // const [isFetching, setIsFetching] = useState<boolean>(false);

   // const handleNavigation = () => {
   //    navigate('/integrations/woocommerce');
   // };

   // useEffect(() => {
   //    if (!client_id) return;

   //    const fetchData = async () => {
   //       setIsFetching(true);
   //       try {
   //          const response = (await endPoints.checkConnectionDetails({
   //             client_id,
   //             channel_name: channelNames.WOOCOMMERCE,
   //          })) as ApiResponse;

   //          const details = response.data.details;
   //          setIsConnected(details?.is_active || false);
   //       } catch (error) {
   //          console.error('Error fetching connection details:', error);
   //       } finally {
   //          setIsFetching(false);
   //       }
   //    };

   //    void fetchData();
   // }, [client_id]);

   // const handleDisconnect = async () => {
   //    if (!client_id) return;
   //    try {
   //       await connectDisconnectToWoocommerce({
   //          channel_name: channelNames.WOOCOMMERCE,
   //          isConnect: false,
   //          client_id,
   //       });
   //       setIsConnected(false);
   //    } catch (error) {
   //       console.error('Error disconnecting from WooCommerce:', error);
   //    }
   // };

   return (
      <Card
         // isConnected={isConnected}
         // isFetching={isFetching}
         heading='Woocommerce'
         commingSoon
         src={image}
         // onButtonClick={isConnected ? handleDisconnect : handleNavigation}
      />
   );
};

export default Woocommerce;
