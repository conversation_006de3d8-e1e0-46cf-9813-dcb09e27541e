import { Campaign, KpiwiseAggregate } from '../../../api/service/pulse';
import { DaywiseAdsetKPIsCalculated } from '../../../api/service/pulse/performance-insights/meta-ads';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { UserDetails } from '../components/interface';

export interface Kpis {
   kpi: string;
   kpi_previous: string | number | null;
   kpi_current: string | number | null;
   tracked?: boolean;
}

export interface CampaignKpis {
   campaign_id: number;
   campaign_name: string;
   campaign_status: string;
   currency: string;
   kpis: Kpis[];
}
interface ProcessedCampaignData {
   client_id: string;
   current_campaign_name: string;
   current_campaign_id: number;
   current_campaign_data: CampaignKpis;
   other_campaign_data: CampaignKpis[];
}
// interface AdsetKpi {
//    kpi: string;
//    kpi_previous: string | number;
//    kpi_current: string | number;
//    age_targeting: string;

//    age_current: string;
//    placement_targeting: string;

//    placement_current: string;

//    audience_behaviors: string;
//    audience_interests: string;
//    region_current: string;
// }
// interface Adset {
//    adset_id: number;
//    adset_name: string;
//    adset_status: string;
//    currency: string;
//    performance_category: {
//       category: string;
//       insights: string;
//    };
//    kpis: AdsetKpi[];
// }
interface ProcessedAdsetData {
   client_id: string;
   current_adset_id: number;
   current_adset_name: string;
   adset_data: DaywiseAdsetKPIsCalculated[];
   objective: string;
}

interface GoogleProccessedCampaignChartData {
   client_id: string;
   campaign_name: string;
   campaign_id: number;
   // campaign_data: CampaignKpis;
   chartData: KpiwiseAggregate;
}
interface GoogleProccessedCampaignAdgroupData {
   client_id: string;
   campaign_name: string;
   campaign_id: number;
   // campaign_data: CampaignKpis;
   // chartData: KpiwiseAggregate;
}

export function payloadGenCampaign(
   campaigns: CampaignKpis[],
   selectedCampaignId: number,
   selectedKpi: string,
): ProcessedCampaignData {
   const userDetails =
      LocalStorageService.getItem<UserDetails>(Keys.FlableUserDetails) ||
      ({} as UserDetails);
   const currentCampaign = campaigns.find(
      (campaign) => campaign.campaign_id === selectedCampaignId,
   );

   if (!currentCampaign) {
      throw new Error('Selected campaign not found');
   }

   const otherCampaigns = campaigns.filter(
      (campaign) => campaign.campaign_id !== selectedCampaignId,
   );

   const sortedOtherCampaigns = otherCampaigns.sort((a, b) => {
      const aKpi = a.kpis.find((kpi) => kpi.kpi === selectedKpi);
      const bKpi = b.kpis.find((kpi) => kpi.kpi === selectedKpi);

      const aKpiValue =
         typeof aKpi?.kpi_current === 'number'
            ? aKpi.kpi_current
            : typeof aKpi?.kpi_previous === 'number'
              ? aKpi.kpi_previous
              : 0;
      const bKpiValue =
         typeof bKpi?.kpi_current === 'number'
            ? bKpi.kpi_current
            : typeof bKpi?.kpi_previous === 'number'
              ? bKpi.kpi_previous
              : 0;

      return bKpiValue - aKpiValue;
   });

   const top10Campaigns = sortedOtherCampaigns.slice(0, 10);

   return {
      client_id: userDetails.client_id,
      current_campaign_name: currentCampaign.campaign_name,
      current_campaign_id: currentCampaign.campaign_id,
      current_campaign_data: currentCampaign,
      other_campaign_data: top10Campaigns,
   };
}
export function payloadGenAdset(
   adsets: DaywiseAdsetKPIsCalculated[],
   selectedAdsetId: number,
   selectedKpi: string,
   objective: string,
): ProcessedAdsetData {
   const userDetails =
      LocalStorageService.getItem<UserDetails>(Keys.FlableUserDetails) ||
      ({} as UserDetails);
   const currentAdset = adsets.find(
      (adset) => Number(adset.adset_id) === selectedAdsetId,
   );

   if (!currentAdset) {
      throw new Error('Selected adset not found');
   }

   const sortedAdsets = adsets.sort((a, b) => {
      const aKpi = a.kpis.find((kpi) => kpi.kpi_name === selectedKpi);
      const bKpi = b.kpis.find((kpi) => kpi.kpi_name === selectedKpi);

      const aKpiValue =
         typeof aKpi?.kpi_current === 'number'
            ? aKpi.kpi_current
            : typeof aKpi?.kpi_previous === 'number'
              ? aKpi.kpi_previous
              : 0;
      const bKpiValue =
         typeof bKpi?.kpi_current === 'number'
            ? bKpi.kpi_current
            : typeof bKpi?.kpi_previous === 'number'
              ? bKpi.kpi_previous
              : 0;

      return bKpiValue - aKpiValue;
   });

   let top10Adsets = sortedAdsets.slice(0, 10);

   const currentAdsetInTop10 = top10Adsets.some(
      (adset) => Number(adset.adset_id) === selectedAdsetId,
   );

   if (!currentAdsetInTop10) {
      top10Adsets.push(currentAdset);
      top10Adsets = top10Adsets
         .sort((a, b) => {
            const aKpi = a.kpis.find((kpi) => kpi.kpi_name === selectedKpi);
            const bKpi = b.kpis.find((kpi) => kpi.kpi_name === selectedKpi);

            const aKpiValue =
               typeof aKpi?.kpi_current === 'number'
                  ? aKpi.kpi_current
                  : typeof aKpi?.kpi_previous === 'number'
                    ? aKpi.kpi_previous
                    : 0;
            const bKpiValue =
               typeof bKpi?.kpi_current === 'number'
                  ? bKpi.kpi_current
                  : typeof bKpi?.kpi_previous === 'number'
                    ? bKpi.kpi_previous
                    : 0;

            return bKpiValue - aKpiValue;
         })
         .slice(0, 10);
   }

   return {
      client_id: userDetails.client_id,
      current_adset_id: Number(currentAdset.adset_id),
      current_adset_name: currentAdset.adset_name,
      adset_data: top10Adsets,
      objective: objective,
   };
}

export function GooglePayloadGenChart(
   data: Campaign[],
   selectedCampaignId: number,
   metricOptions: { value: string; label: string }[],
   selectedKpi: string,
   campaignKpiData: KpiwiseAggregate,
): GoogleProccessedCampaignChartData {
   const userDetails =
      LocalStorageService.getItem<UserDetails>(Keys.FlableUserDetails) ||
      ({} as UserDetails);
   const transformData = data.map((campaign) => {
      const {
         campaign_id,
         campaign_name,
         campaign_status,
         currency,
         total_val,
         prev_total_val,
      } = campaign;

      const kpiKeys = metricOptions.map((value) => value.value);

      const kpis = kpiKeys.map((kpi) => ({
         kpi,
         kpi_previous: prev_total_val?.[kpi] ?? null,
         kpi_current: total_val?.[kpi] ?? null,
      }));

      return {
         campaign_id,
         campaign_name,
         campaign_status,
         currency: `['${currency}']`,
         kpis,
      };
   });

   const currentCampaign = transformData.find(
      (c) => c.campaign_id === selectedCampaignId,
   );

   const spendData = campaignKpiData.spend ?? [];
   const metric = campaignKpiData[selectedKpi] ?? [];

   const chartData = {
      spend: spendData,
      [selectedKpi]: metric,
   };

   return {
      client_id: userDetails.client_id,
      campaign_name: currentCampaign!.campaign_name,
      campaign_id: currentCampaign!.campaign_id,
      // campaign_data: currentCampaign!,
      chartData: chartData,
   };
}
export function GooglePayloadGenAdgroup(
   data: Campaign[],
   selectedCampaignId: number,
   metricOptions: { value: string; label: string }[],
   // selectedKpi: string,
   // campaignKpiData: KpiwiseAggregate,
): GoogleProccessedCampaignAdgroupData {
   const userDetails =
      LocalStorageService.getItem<UserDetails>(Keys.FlableUserDetails) ||
      ({} as UserDetails);
   const transformData = data.map((campaign) => {
      const {
         campaign_id,
         campaign_name,
         campaign_status,
         currency,
         total_val,
         prev_total_val,
      } = campaign;

      const kpiKeys = metricOptions.map((value) => value.value);

      const kpis = kpiKeys.map((kpi) => ({
         kpi,
         kpi_previous: prev_total_val?.[kpi] ?? null,
         kpi_current: total_val?.[kpi] ?? null,
      }));

      return {
         campaign_id,
         campaign_name,
         campaign_status,
         currency: `['${currency}']`,
         kpis,
      };
   });

   const currentCampaign = transformData.find(
      (c) => c.campaign_id === selectedCampaignId,
   );

   // const spendData = campaignKpiData.spend ?? [];
   // const metric = campaignKpiData[selectedKpi] ?? [];

   // const chartData = {
   //    spend: spendData,
   //    [selectedKpi]: metric,
   // };

   return {
      client_id: userDetails.client_id,
      campaign_name: currentCampaign!.campaign_name,
      campaign_id: currentCampaign!.campaign_id,
      // campaign_data: currentCampaign!,
      // chartData: chartData,
   };
}
