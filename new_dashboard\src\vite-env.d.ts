/// <reference types="vite/client" />

interface ImportMetaEnv {
   readonly VITE_MARCO_API: string;
   readonly VITE_BE_API: string;
   readonly VITE_AGENTIC_API: string;
   readonly VITE_SOCIAL_LISTENING_API: string;
   readonly VITE_TELEGRAM_BOT_KEY: string;
   readonly VITE_TELEGRAM_BOT_API: string;
   readonly VITE_AIRBYTE_WORKSPACE_ID: string;
   readonly VITE_AIRBYTE_DESTINATION_ID_G_ADS: string;
   readonly VITE_AIRBYTE_DESTINATION_ID_META_ADS: string;
   readonly VITE_FACEBOOK_ADS_VERSION: string;
   readonly VITE_XI_AGENT_API: string;
}

interface ImportMeta {
   readonly env: ImportMetaEnv;
}
