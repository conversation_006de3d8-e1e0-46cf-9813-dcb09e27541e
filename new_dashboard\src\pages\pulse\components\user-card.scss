@use '../../../sass/variable.scss';
.WebInsightCardWrapper {
   border: 1px solid rgba(160, 164, 168, 0.22);
   background-color: #def8fc;
   width: 32.2%;
   padding: 7px 10px;
   border-radius: 15px;
   display: flex;
   flex-direction: column;
   justify-content: space-between;

   // [data-theme='dark'] & {
   //    background-color: $background_surface;
   // }

   .card-content {
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .heading-container {
         display: flex;
         align-items: center;
         justify-content: space-between;

         .icon {
            width: 25px;
            height: 25px;
            background-color: transparent;
            margin-left: 15px;
         }
      }
   }

   .heading {
      color: #0000ff;
      font-size: 1.5rem; /* 110% */
      margin: 0;
      font-weight: bold;
      // [data-theme='dark'] & {
      //    color: $text_highlight;
      // }
   }

   .usercard-container {
      display: flex;
      justify-content: space-between;

      .subheading {
         font-size: 0.875rem;
         color: #000;
         flex: 1;
      }
      .icon {
         width: 25px;
         height: 25px;
         background-color: transparent;
         margin-left: 15px;
      }
   }

   .bottom {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: 5px;

      .tracking {
         font-size: 1rem;
         display: flex;
         align-items: center;
         cursor: pointer;
      }
   }
}
