export const COGS_STRINGS = {
   fixedTitle: ' Enable COGS as % of Gross Sales',
   fixedHelpText:
      'if a fixed COGS percentage is entered any product cost entered on a product of variant level will be ignored. The cost is set as the percent of the gross sales total. Please note it can take up to 24 hours to update all of your orders',
   fixedSubTitle: 'Fixed COGS %',
};

export const SHIPPING_STRINGS = {
   shippingCharges: 'Use Shipping Chargers for Shipping Costs',
   shippingChargesDesc:
      'Enable this setting if your shipping Costs are equal to what your customers have been charged for shipping.',
   defaultShipping: ' Default Shipping Costs',
   defaultShippingDesc:
      'If you use multiple default shipping option they will be prioritized according to the order below.',
   orderId: 'Shipping costs by Order ID',
   dynamicRate: 'Shipping Costs by Dynamic Rate',
   orderIdDesc: 'Import shipping charges based on Order ID as seen in Shopify',
   dynamicRateDesc:
      'Create a fulfillment profile to dashboard dynamic shipping costs based on zone weight-tiers and more',
};

export const PAYMENT_GATEWAY_STRINGS = {
   fees: 'Fees are imported directly from Shopify',
};

export const CUSTOM_EXPENSES_STRING = {
   title: 'There is a maximum limit of 5000 custom expenses per shop',
   campaignAtVariableExp:
      'Campaign level attribution is not avialble for variable expenses yet. If you want to attribute this expense to a specific campaign, please create a fixed custom ad spend',
   adSpendHelpText:
      'This will be included in total ad spend, and will affects all metrics that included blended ads in their calculation',
   costPerDay:
      'Effective cost per day for each custom expensebased an amount spend and selected time parameters. For monthly recuring expenses, this will show an approximate daily cost.',
   editFixedExpense:
      "Editing this fixed expense will affect past data. if you don't want to affect past data, pleasecreate anew fixed expense. You can end this expense by selecting today as the end date",
   editVairableExpense:
      "Editing this variable expense will affect past data. if you don't want toaffect past data, pleasecreate anew variable expense. You can end this expense by selecting today as the end date",
   deleteFixedExpense:
      "Delete this fixed expense will remove it form all past data. This action can't be undone, areyou sure you want to delete ?",
   deleteVariableExpense:
      "Delete this variable expense will remove it form all past data. This action can't be undone, areyou sure you want to delete ?",
};

export const FILE_TYPE = [
   'text/csv',
   'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
];
export const FILE_EXTENSION = ['csv', 'xlsx'];
export const HEADING = [['Order_Id', 'Shipping_Cost']];
export const METRIC_LIST = [
   'Total Revenue',
   'Blended Ads',
   'Ads (Meta)',
   'Ads (Google)',
   'Ads (Snapchat)',
   'Ads (TikTok)',
   'Ads (Twitter)',
   'Ads (Pinterest)',
   'Ads (Microsoft)',
   'Ads (criteo)',
   'COGS',
   'Net Sales',
   'Total Refunds',
   'Orders',
];
