import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export const componentNames = {
   DASHBOARD: 'dashboard',
   MARCO: 'marco',
   PERFORMANCE_INISGHT: 'performanceInsight',
   WEB_INSIGHT: 'webInsight',
   CONTENT_CREATOR: 'contentCreator',
   CONTENT_CALENDER: 'contentCalender',
   INTEGRATION: 'integrations',
   CONTENT_GENERATION: 'contentGeneration',
   CONTENT_CREATION: 'contentCreation',
   CONTENT_IDEATION: 'contentIdeation',
   TRACKED: 'tracked',
   SETTINGS: 'settings',
   OPTIMISATIONS: 'optimisations',
};

interface InitialState {
   dashboard: boolean;
   marco: boolean;
   performanceInsight: boolean;
   webInsight: boolean;
   contentCreator: boolean;
   contentCalender: boolean;
   integration: boolean;
   contentGeneration: boolean;
   contentCreation: boolean;
   contentIdeation: boolean;
   tracked: boolean;
   settings: boolean;
   optimisations: boolean;
}

const initialState: InitialState = {
   tracked: false,
   dashboard: false,
   marco: false,
   performanceInsight: false,
   webInsight: false,
   contentCreator: false,
   contentCalender: false,
   integration: false,
   contentGeneration: false,
   contentIdeation: false,
   contentCreation: false,
   settings: false,
   optimisations: false,
};

const tourSlice = createSlice({
   name: 'tour',
   initialState,
   reducers: {
      setFlag: (
         state: InitialState,
         action: PayloadAction<{ componentName: string; flag: boolean }>,
      ) => {
         const { componentName, flag } = action.payload;

         switch (componentName) {
            case componentNames.DASHBOARD:
               state.dashboard = flag;
               break;
            case componentNames.MARCO:
               state.marco = flag;
               break;
            case componentNames.PERFORMANCE_INISGHT:
               state.performanceInsight = flag;
               break;
            case componentNames.WEB_INSIGHT:
               state.webInsight = flag;
               break;
            case componentNames.CONTENT_CREATOR:
               state.contentCreator = flag;
               break;
            case componentNames.CONTENT_CALENDER:
               state.contentCalender = flag;
               break;
            case componentNames.INTEGRATION:
               state.integration = flag;
               break;
            case componentNames.CONTENT_GENERATION:
               state.contentGeneration = flag;
               break;
            case componentNames.CONTENT_CREATION:
               state.contentCreation = flag;
               break;
            case componentNames.CONTENT_IDEATION:
               state.contentIdeation = flag;
               break;
            case componentNames.TRACKED:
               state.tracked = flag;
               break;
            case componentNames.SETTINGS:
               state.settings = flag;
               break;
            case componentNames.OPTIMISATIONS:
               state.optimisations = flag;
               break;
         }
      },
   },
});

export const { setFlag } = tourSlice.actions;

export default tourSlice.reducer;
