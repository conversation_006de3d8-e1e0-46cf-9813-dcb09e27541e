import { useNavigate } from 'react-router-dom';
import noIntegrationsImage from '../../assets/image/no-integrations-image.svg';
import { Button } from '../ui/button';

interface Props {
   feature: string;
   text: string;
}

const IntegrateInfo = (props: Props) => {
   const navigate = useNavigate();

   const handleButtonClick = () => {
      navigate('/integrations');
   };

   const { feature, text } = props;

   return (
      <div className='w-full h-full flex items-center justify-center p-4'>
         <div className='flex flex-col min-w-[280px] max-w-[400px] items-center justify-center text-center gap-2'>
            <img src={noIntegrationsImage} alt='Integrate Info' />
            <p className='text-black text-xl font-extrabold'>
               Connect to Unlock {feature}
            </p>
            <p className='mt-2 text-black text-md'>{text}</p>
            <Button
               className='mt-4 text-white bg-[#3c76e1] hover:text-white hover:bg-[#3c76e1] hover:cursor-pointer'
               onClick={handleButtonClick}
            >
               Go to Integrations
            </Button>
         </div>
      </div>
   );
};

export default IntegrateInfo;
