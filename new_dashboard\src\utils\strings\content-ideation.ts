export const contentIdeationStrings = {
   contentIdeationHeader: 'Content Ideation',
   contentIdeationDescription:
      'Get content Idea from emerging trends and competitor posts',
   uploadImgText: 'Upload Image',
   trends: 'Google Search Trends',
   industryTrends: 'Industry Trends',
   postTitle: 'Post Title',
   platform: 'Platform',
   engagement: 'Engagement',
   link: 'Link',
   action: 'Action',
   ImageTextPrompt: `generate 6 social media content ideas for Instagram and LinkedIn.
   Please limit each content idea in 6 words based on the image provided.
   Please give your response in the following json format only and give the json data only as the response (means start your response with '{' and end with '}'):
   {'idea1': 'ideaname1',
    'idea2': 'ideaname2',
    'idea3': 'ideaname3',
    'idea4': 'ideaname4',
    'idea5': 'ideaname5',
    'idea6': 'ideaname6'}.`,
   CompetitorPrompt: `now take the refernce competitor post below to create a fresh post without using competitor's sepcific names and hashtags from below reference, included personlaised company specific hastags :`,
   Imageformat: `Supported Photo Format: JPG,PNG. Photo Size Limit: Up to 20MB.
    Resolution: Recommended between 552 * 368 and 7680 * 4320`,
};
