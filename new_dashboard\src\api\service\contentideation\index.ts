import { AxiosResponse, AxiosError } from 'axios';
import {
   GetTrendsPayload,
   GetTrendsResponse,
   GetPersonalisedResponse,
   CompetitorData,
   GetTextImagePayload,
   GetTextImageResponse,
} from './interface';
import { marcoClient } from '../marco';

export const getContent = async (
   payload: GetTrendsPayload | GetTextImagePayload,
): Promise<
   | GetTrendsResponse
   | GetPersonalisedResponse
   | CompetitorData
   | GetTextImageResponse
   | null
> => {
   try {
      const response: AxiosResponse<
         GetTrendsResponse | GetPersonalisedResponse | GetTextImageResponse
      > = await marcoClient.post('/handle-get-content', payload);
      if (!response.data) {
         throw new Error('Failed to fetch content');
      }
      return response.data;
   } catch (error: unknown) {
      const err = error as AxiosError;
      console.error('Error fetching data:', err);
      return null;
   }
};
