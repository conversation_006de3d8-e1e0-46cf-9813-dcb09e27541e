import { <PERSON>, Spinner } from '@chakra-ui/react';
import { useFetchAllAlerts } from '../apis/custom-alerts-apis';
import { Button } from '@/components/ui/button';
import { useAppDispatch } from '@/store/store';
import { useNavigate } from 'react-router-dom';
import { Checkbox, Flex, IconButton, Image, Tbody } from '@chakra-ui/react';
import { Table, TableContainer, Tag, TagLabel } from '@chakra-ui/react';
import { Td, Text, Th, Thead, Tooltip, Tr } from '@chakra-ui/react';
import { LuArrowDownUp, LuArrowUp, LuArrowDown } from 'react-icons/lu';
import { useEffect, useState } from 'react';
import { CustomAlert } from '@/api/service/custom-alerts';
import { MdOutlineModeEditOutline } from 'react-icons/md';
import { RiDeleteBinLine } from 'react-icons/ri';
import {
   ALERT_STATUS,
   CHANNELS_WITH_ICONS,
   splitAndUppercase,
   TABLE_COLUMNS,
} from '../utils/custom-alerts/custom-alerts-helpers';
import DeleteConfirmation from '../components/alert-dialogs/delete-confirmation';
import {
   handleFormChange,
   setDeleteMultipleAlerts,
   setEditMode,
} from '@/store/reducer/custom-alerts-reducer';
import { setCurrentAgent } from '@/store/reducer/marco-reducer';

interface SortableThProps {
   columnKey: string;
   secondaryKey?: string;
   label: string;
   align?: 'start' | 'center';
}

const ViewCustomAlerts = () => {
   const dispatch = useAppDispatch();
   const navigate = useNavigate();

   const [alerts, setAlerts] = useState<CustomAlert[]>([]);
   const [selectedAlerts, setSelectedAlerts] = useState<number[]>([]);
   const [sortDetails, setSortDetails] = useState<{
      column: string | null;
      order: string | null;
   }>({
      column: null,
      order: null,
   });

   const { data: allAlerts, isFetching: isAllAlertsFetching } =
      useFetchAllAlerts();

   const handleEdit = (alert: CustomAlert) => {
      navigate(`/marco/custom-alerts/create-alert`);
      dispatch(setEditMode(true));

      const updatedFields = {
         alert_id: alert.alert_id as string,
         alert_name: alert.alert_name,
         alert_description: alert.alert_description,
         email_recipients: alert.email_recipients.map((email) => ({
            email,
            valid: true,
         })),
         alert_time: alert.alert_time,
         alert_status: alert.alert_status,
         channel: alert.alert_conditions.channel,
         campaigns: alert.alert_conditions.campaigns,
         metrics: alert.alert_conditions.metrics,
         target_period: alert.alert_conditions.target_period,
         reference_period: alert.alert_conditions.reference_period,
         kpi_rules: alert.alert_conditions.kpi_rules,
      };

      Object.entries(updatedFields).forEach(([name, value]) => {
         dispatch(
            handleFormChange({
               name: name as keyof typeof updatedFields,
               value,
            }),
         );
      });
   };

   const handleDeleteSingle = () => {
      dispatch(setDeleteMultipleAlerts(false));
   };

   const handleDeleteMultiple = () => {
      dispatch(setDeleteMultipleAlerts(true));
   };

   const handleCheckboxChange = (alert_id: number) => {
      if (selectedAlerts.includes(alert_id)) {
         setSelectedAlerts(selectedAlerts.filter((id) => id !== alert_id));
      } else {
         setSelectedAlerts([...selectedAlerts, alert_id]);
      }
   };

   const handleOverallCheckboxChange = (
      e: React.ChangeEvent<HTMLInputElement>,
   ) => {
      if (e.target.checked && alerts) {
         setSelectedAlerts(alerts?.map((alert) => Number(alert.alert_id)));
      } else {
         setSelectedAlerts([]);
      }
   };

   const sortData = (
      alerts: CustomAlert[],
      column: keyof CustomAlert,
      order: 'asc' | 'desc',
   ) => {
      return [...alerts].sort((a, b) => {
         const getSortableValue = (val: unknown) => {
            if (val == null) return '';
            if (typeof val === 'object') return JSON.stringify(val);
            if (typeof val === 'string') return val.toLowerCase();
            if (typeof val === 'number' || typeof val === 'boolean')
               return val.toString();
            return '';
         };

         const aVal = getSortableValue(a[column]);
         const bVal = getSortableValue(b[column]);

         if (aVal < bVal) return order === 'asc' ? -1 : 1;
         if (aVal > bVal) return order === 'asc' ? 1 : -1;
         return 0;
      });
   };

   const handleSort = (column: keyof CustomAlert) => {
      if (sortDetails.column === column) {
         const newSortOrder = sortDetails.order === 'asc' ? 'desc' : 'asc';

         setSortDetails({
            column,
            order: newSortOrder,
         });

         const sortedData = sortData(alerts, column, newSortOrder);
         setAlerts(sortedData);
      } else {
         setSortDetails({ column, order: 'asc' });

         const sortedData = sortData(alerts, column, 'asc');
         setAlerts(sortedData);
      }
   };

   const SortableTh = ({
      columnKey,
      label,
      align = 'start',
   }: SortableThProps): JSX.Element => {
      const isActive = sortDetails.column === columnKey;
      const arrowIcon = isActive ? (
         sortDetails.order === 'asc' ? (
            <LuArrowUp />
         ) : (
            <LuArrowDown />
         )
      ) : (
         <Box
            opacity={0}
            transition='opacity 0.2s ease'
            _groupHover={{ opacity: 1 }}
            ml='4px'
            className='filter-arrow'
         >
            <LuArrowDownUp />
         </Box>
      );

      return (
         <Th
            fontFamily='Nunito Sans'
            height='40px'
            position='sticky'
            cursor='pointer'
            onClick={() => handleSort(columnKey as keyof CustomAlert)}
            _hover={{ '.filter-arrow': { opacity: 1 } }}
         >
            <Box display='flex' alignItems='center' justifyContent={align}>
               {label} {arrowIcon}
            </Box>
         </Th>
      );
   };

   useEffect(() => {
      if (allAlerts) {
         setAlerts(
            allAlerts.sort((a, b) => Number(b.alert_id) - Number(a.alert_id)),
         );
      }
   }, [allAlerts]);

   if (isAllAlertsFetching) {
      return (
         <div className='w-full h-full flex items-center justify-center'>
            <Spinner size='xl' />
         </div>
      );
   }

   return (
      <>
         <div className='w-full h-full flex items-center justify-center'>
            <div className='w-full h-full p-4'>
               <div className='w-full flex items-center justify-between flex-wrap-reverse'>
                  {alerts && alerts.length > 0 ? (
                     <p className='text-lg font-bold'>
                        You have set up {alerts.length}{' '}
                        {alerts.length > 1 ? 'alerts.' : 'alert.'}
                     </p>
                  ) : (
                     ''
                  )}
                  <div className='flex items-center gap-2'>
                     <Button
                        variant='outline'
                        onClick={() => {
                           dispatch(setCurrentAgent('alerting-agent'));
                           navigate('/marco/agentic-workflow');
                        }}
                     >
                        Back
                     </Button>
                     {selectedAlerts.length > 0 && (
                        <DeleteConfirmation
                           selectedAlerts={selectedAlerts}
                           setSelectedAlerts={setSelectedAlerts}
                        >
                           <Button
                              className='bg-red-600'
                              onClick={() => handleDeleteMultiple()}
                           >
                              Delete Selected
                           </Button>
                        </DeleteConfirmation>
                     )}
                     <Button
                        variant='default'
                        onClick={() =>
                           navigate('/marco/custom-alerts/create-alert')
                        }
                     >
                        Create Alert
                     </Button>
                  </div>
               </div>
               <div className='mt-4'>
                  <TableContainer
                     width='100%'
                     height='86%'
                     boxShadow='0 0 4px rgba(0, 0, 0, 0.1)'
                     borderRadius='lg'
                     className='table-container'
                     overflowY='auto'
                     position='relative'
                  >
                     <Table variant='simple' size='sm'>
                        <Thead
                           background='#f2f2f2'
                           position='sticky'
                           top={0}
                           zIndex={10}
                        >
                           <Tr>
                              <Th>
                                 <Checkbox
                                    margin={2}
                                    borderColor='blackAlpha.600'
                                    disabled={alerts?.length === 0}
                                    isChecked={
                                       selectedAlerts.length === alerts?.length
                                    }
                                    onChange={handleOverallCheckboxChange}
                                 ></Checkbox>
                              </Th>
                              <Th
                                 fontFamily='Nunito Sans'
                                 position='sticky'
                                 textAlign='center'
                              >
                                 Actions
                              </Th>
                              {TABLE_COLUMNS.map(({ key, label, align }) => (
                                 <SortableTh
                                    key={key}
                                    columnKey={key}
                                    label={label}
                                    align={align as 'start' | 'center'}
                                 />
                              ))}
                           </Tr>
                        </Thead>
                        <Tbody>
                           {alerts.map((alert) => (
                              <Tr
                                 key={alert?.alert_id}
                                 background={
                                    selectedAlerts.includes(
                                       Number(alert?.alert_id),
                                    )
                                       ? '#D0E8FF'
                                       : ''
                                 }
                              >
                                 <Td textAlign='center' width='40px'>
                                    <Checkbox
                                       margin={2}
                                       isChecked={selectedAlerts.includes(
                                          Number(alert?.alert_id),
                                       )}
                                       borderColor='blackAlpha.600'
                                       onChange={() =>
                                          handleCheckboxChange(
                                             Number(alert?.alert_id),
                                          )
                                       }
                                    ></Checkbox>
                                 </Td>
                                 <Td textAlign='center'>
                                    <Flex
                                       alignItems='center'
                                       justifyContent='center'
                                    >
                                       <Tooltip label='Edit' hasArrow>
                                          <IconButton
                                             size='sm'
                                             _hover={{ cursor: 'pointer' }}
                                             aria-label='Edit'
                                             icon={<MdOutlineModeEditOutline />}
                                             background='none'
                                             onClick={() =>
                                                void handleEdit(alert)
                                             }
                                          />
                                       </Tooltip>
                                       <DeleteConfirmation
                                          alert={alert}
                                          selectedAlerts={[...selectedAlerts]}
                                       >
                                          <IconButton
                                             size='sm'
                                             _hover={{ cursor: 'pointer' }}
                                             aria-label='Delete'
                                             icon={<RiDeleteBinLine />}
                                             background='none'
                                             onClick={handleDeleteSingle}
                                          />
                                       </DeleteConfirmation>
                                    </Flex>
                                 </Td>
                                 <Td textAlign='left'>
                                    <Tooltip hasArrow label={alert?.alert_name}>
                                       {alert?.alert_name.length > 15
                                          ? alert?.alert_name.slice(0, 15) +
                                            '...'
                                          : alert?.alert_name || '-'}
                                    </Tooltip>
                                 </Td>
                                 <Td className='t-data' textAlign='left'>
                                    <Flex
                                       width='100%'
                                       alignItems='center'
                                       gap={2}
                                    >
                                       <Image
                                          width={6}
                                          src={
                                             CHANNELS_WITH_ICONS[
                                                alert?.alert_conditions
                                                   .channel as keyof typeof CHANNELS_WITH_ICONS
                                             ]?.icon
                                          }
                                       />
                                       <Text>
                                          {
                                             CHANNELS_WITH_ICONS[
                                                alert?.alert_conditions
                                                   .channel as keyof typeof CHANNELS_WITH_ICONS
                                             ]?.option
                                          }
                                       </Text>
                                    </Flex>
                                 </Td>
                                 <Td textAlign='left'>
                                    <Tooltip
                                       label={
                                          (alert.alert_conditions.campaigns &&
                                             alert?.alert_conditions.campaigns
                                                .map((c) => c.name)
                                                .join(', ')) ||
                                          ''
                                       }
                                       hasArrow
                                    >
                                       {alert.alert_conditions.campaigns &&
                                          ((alert?.alert_conditions.campaigns
                                             .length > 0 &&
                                             alert?.alert_conditions.campaigns
                                                .map((c) => c.name)
                                                .join(', ')) ||
                                             '')}
                                    </Tooltip>
                                 </Td>
                                 <Td textAlign='left'>
                                    <Tooltip
                                       label={splitAndUppercase(
                                          alert?.alert_conditions.metrics.join(
                                             ', ',
                                          ),
                                       )}
                                       hasArrow
                                    >
                                       {alert?.alert_conditions.metrics.length >
                                       12
                                          ? splitAndUppercase(
                                               alert?.alert_conditions.metrics.join(
                                                  ', ',
                                               ),
                                            ).slice(0, 12) + '...'
                                          : splitAndUppercase(
                                               alert?.alert_conditions.metrics.join(
                                                  ', ',
                                               ),
                                            ) || '-'}
                                    </Tooltip>
                                 </Td>
                                 <Td textAlign='center'>
                                    <Tooltip
                                       label={
                                          alert?.alert_conditions.target_period
                                             .label || '-'
                                       }
                                       hasArrow
                                    >
                                       {alert?.alert_conditions.target_period
                                          .label || '-'}
                                    </Tooltip>
                                 </Td>
                                 <Td textAlign='center'>
                                    <Tooltip
                                       label={
                                          alert?.alert_conditions
                                             .reference_period.label || '-'
                                       }
                                       hasArrow
                                    >
                                       {alert?.alert_conditions.reference_period
                                          .label || '-'}
                                    </Tooltip>
                                 </Td>
                                 <Td textAlign='center'>
                                    <Tag
                                       size='sm'
                                       borderRadius='full'
                                       variant='solid'
                                       background={
                                          ALERT_STATUS[alert?.alert_status]
                                             ?.background
                                       }
                                       color={
                                          ALERT_STATUS[alert?.alert_status]
                                             ?.color
                                       }
                                       gap={1}
                                    >
                                       <TagLabel>
                                          {
                                             ALERT_STATUS[alert?.alert_status]
                                                ?.option
                                          }
                                       </TagLabel>
                                    </Tag>
                                 </Td>
                              </Tr>
                           ))}
                        </Tbody>
                     </Table>
                  </TableContainer>
               </div>
            </div>
         </div>
      </>
   );
};

export default ViewCustomAlerts;
