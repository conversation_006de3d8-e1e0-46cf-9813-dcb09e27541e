import { Box, Text, Button, Image } from '@chakra-ui/react';

import './integration-icons.scss';

interface IntegrationIconProps {
   logo: string;
   name: string;
}

const IntegrationsIcons: React.FC<IntegrationIconProps> = ({ logo, name }) => {
   return (
      <Box
         width='210px'
         height='200px'
         border='1px solid #fff'
         className='integration-icon'
      >
         <Image
            boxSize='80px'
            objectFit='contain'
            src={logo}
            alt={`${name} logo`}
         />
         <Text fontSize={'20px'} fontWeight={500}>
            {name}
         </Text>
         <Button
            padding={'12px 20px 12px 20px'}
            backgroundColor={'#437EEB'}
            color={'#fff'}
         >
            <Text fontSize={'16px'} fontWeight={500}>
               Add
            </Text>
         </Button>
      </Box>
   );
};

export default IntegrationsIcons;
