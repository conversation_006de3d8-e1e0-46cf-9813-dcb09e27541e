export interface KPI {
   kpi_date: string;
   kpi_name: string;
   kpi_value: string | number;
   kpi_current: string | number;
   kpi_previous: string | number;
   campaign_status: string;
   objective?: string;
   currency?: string;
   budget?: string;
   channel?: string;
}

export interface TrackedKPIs {
   client_id: string;
   objective: string;
   campaign_id: string;
   kpi_name: string;
   tracked: boolean;
   channel: string;
}
export interface daywiseKPIs {
   [key: string]: Record<string, number>;
}
export interface Campaign {
   campaign_id: number;
   campaign_name: string;
   kpis: KPI[];
   day_wise_kpis?: daywiseKPIs | null;
   total_val: KPIAggregate;
   prev_total_val: KPIAggregate;
   trackedKPIs: TrackedKPIs[];
}

export interface DayWiseCampaign {
   campaign_id: number;
   campaign_name: string;
   kpis: KPI[];
   total_val: KPIAggregate;
   prev_total_val: KPIAggregate;
   trackedKPIs: TrackedKPIs[];
}

export interface TrackedCampaign extends Campaign {
   campaign_status: 'ACTIVE' | 'PAUSED' | null;
   grouped_kpis?: GroupedKPIAggregate;
   kpi_name: string;
   objective: string;
   channel: string;
   kpi_start_date: string;
   kpi_end_date: string;
   kpi_prev_start_date: string;
   kpi_prev_end_date: string;
}

export interface KPIAggregate {
   [key: string]: number | null;
}

export interface GroupedKPIAggregate {
   [key: string]: KPIAggregate;
}

export interface Kpis {
   kpi: string;
   kpi_previous: string | number | null;
   kpi_current: string | number | null;
   tracked?: boolean;
}
export interface CampaignKpis {
   campaign_id: number;
   campaign_name: string;
   campaign_status: string;
   currency: string;
   kpis: Kpis[];
}
export interface AdsetKpi {
   kpi: string;
   kpi_previous: string | number;
   kpi_current: string | number;
   age_targeting: string;

   age_current: string;
   placement_targeting: string;

   placement_current: string;
   audience_behaviors: string;
   audience_interests: string;
   region_current: string;
}
export interface TrackKPI {
   date: string;
   kpi_value: number;
}
export interface Adset {
   adset_id: number;
   adset_name: string;
   adset_status: string;
   currency: string;
   performance_category: {
      category: string;
      insights: string;
   };
   kpis: AdsetKpi[];
}
export interface AdsetWithKpiDataResponse {
   fn_adset_with_kpi_get: Adset[];
}

export interface AdDetails {
   client_id: string;
   ad_id: string;
   creative_id: string;
   caption: string;
   title: string;
   creative_status: string;
   ad_status: string;
   creative_type: string;
   call_to_action_type: string;
   instagram_permalink_url: string;
   video_thumbnail: string;
   redirect_link: string;
   image_hash: string;
   images_in_carousel: string;
   image_link: string;
   updated_time: string;
   created_time: string;
}

export interface AdData {
   ad_details: AdDetails[];
   ad_id: number;
   ad_name: string;
   ad_status: string;
   currency: string;
   kpis: Kpis[];
}
export interface AdDataResponse {
   fn_ad_with_kpi_get: AdData[];
}
export interface UserDetails {
   client_id: string;
   email: string;
}
export interface KpiPrevData {
   kpi_start_date: string;
   kpi_end_date: string;
   kpi_prev_start_date: string;
   kpi_prev_end_date: string;
   kpi_name: string;
   kpi_value: number;
   kpi_prev_agg_value: number;
   objective: string;
}
export interface CampaignPrevData {
   campaign_id: string;
   campaign_name: string;
   kpis: KpiPrevData[];
}

export interface IndustryBenchmarking {
   current_campaign_values: { [key: string]: number };
   industry_benchmark: { [key: string]: string };
   overall_historical_mean: { [key: string]: number };
   overall_historical_min_max: { [key: string]: string };
   observations: { [key: string]: string };
   timeframe: { [key: string]: string };
}
