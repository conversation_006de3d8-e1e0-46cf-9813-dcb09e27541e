import { useEffect } from 'react';
import { useAppDispatch } from '../store/store';
import {
   setIsFetching,
   setLinkedinConnection,
   setTwitterConnection,
} from '../store/reducer/integration-reducer';
import endPoints, {
   SentimentConnectionDetails,
} from '../pages/social-listening/apis/agent';
import { channelNames } from '../pages/social-listening/utils/constant';
import { Keys, LocalStorageService } from '../utils/local-storage';
import { AuthUser } from '../types/auth';
import { PageInfo } from '../types/social-watch';

function useFetchIntegrationConnections() {
   const client_id = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   )?.client_id;

   const dispatch = useAppDispatch();

   useEffect(() => {
      if (!client_id) return;

      const channelHandlers = {
         [channelNames.TWITTER]: (
            details: SentimentConnectionDetails['details'],
         ) => {
            const { is_active, actual_account_name, meta_data } = details || {};
            if (is_active) {
               const { userId, oauthToken, oauthTokenSecret } =
                  meta_data as Record<string, string>;

               dispatch(
                  setTwitterConnection({
                     actual_account_name: actual_account_name!,
                     oauthToken,
                     oauthTokenSecret,
                     userId,
                     channelName: channelNames.TWITTER,
                  }),
               );
            }
         },
         [channelNames.LINKEDIN]: (
            details: SentimentConnectionDetails['details'],
         ) => {
            const { is_active, meta_data } = details || {};
            if (is_active) {
               const { accessToken, pages } = meta_data as unknown as {
                  accessToken: string;
                  pages: PageInfo[];
               };

               const userDetails = pages.find((page) => page.page === false);
               if (!userDetails) return;

               dispatch(
                  setLinkedinConnection({
                     user: {
                        access_token: accessToken,
                        screenName: userDetails.name,
                        userId: userDetails.id,
                     },
                     pages,
                  }),
               );
            }
         },
      };

      const fetchData = async () => {
         try {
            dispatch(setIsFetching(true));

            const data = await Promise.all(
               Object.keys(channelHandlers).map((channel) =>
                  endPoints.checkConnectionDetails({
                     client_id,
                     channel_name: channel,
                  }),
               ),
            );

            data.forEach((response, index) => {
               const channel = Object.keys(channelHandlers)[index];
               const {
                  data: {
                     details = {} as SentimentConnectionDetails['details'],
                  } = {},
               } = response || {};
               const handler = channelHandlers[channel];
               details && handler?.(details);
            });
         } catch (error) {
            console.error('Error fetching connection details:', error);
         } finally {
            dispatch(setIsFetching(false));
         }
      };

      void fetchData();
   }, [client_id, dispatch]);
}

export default useFetchIntegrationConnections;
