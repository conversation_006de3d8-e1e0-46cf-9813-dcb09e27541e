export const highlightCampaignDetails = (prompt: string) => {
   const campaignNameMatch = prompt.match(
      /(?:named|called|titled)\s+([A-Za-z0-9]+)/i,
   );
   const campaignName = campaignNameMatch ? campaignNameMatch[1] : null;

   const objectiveMatch = prompt.match(/outcome\s+([A-Za-z0-9]+)/i);
   const objective = objectiveMatch ? objectiveMatch[1] : null;

   const regexParts = [];
   if (campaignName) regexParts.push(campaignName);
   if (objective) regexParts.push(objective);

   if (regexParts.length === 0) return prompt;

   const regex = new RegExp(`(${regexParts.join('|')})`, 'gi');
   const parts = prompt.split(regex);

   return parts.map((part, idx) => {
      const lowerPart = part.toLowerCase();
      if (
         (campaignName && lowerPart === campaignName.toLowerCase()) ||
         (objective && lowerPart === objective.toLowerCase())
      ) {
         return (
            <span
               key={idx}
               style={{
                  fontWeight: 700,
                  fontSize: '0.92em',
               }}
            >
               {part}
            </span>
         );
      }
      return (
         <span
            key={idx}
            style={{
               fontSize: '0.92em',
            }}
         >
            {part}
         </span>
      );
   });
};
