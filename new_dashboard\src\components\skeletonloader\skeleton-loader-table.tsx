import { Box, Skeleton, Stack } from '@chakra-ui/react';
interface SkeletonLoaderTableProps {
   width: number;
   clickedPillId: string;
}

const SkeletonLoaderTable: React.FC<SkeletonLoaderTableProps> = ({
   width,
   clickedPillId,
}) => {
   const skeletonItems = Array.from({ length: 8 }, (_, index) => (
      <Stack direction='row' spacing={4} align='center' key={index} gap={15}>
         {clickedPillId === 'Competitor' ? (
            <>
               <Skeleton height='30px' width='10%' />
               <Skeleton height='30px' width='50%' />
               <Skeleton height='30px' width='8%' />
               <Skeleton height='30px' width='8%' />
               <Skeleton height='30px' width='8%' />
               <Skeleton height='30px' width='8%' />
            </>
         ) : (
            <>
               <Skeleton height='30px' width='90%' />
               <Skeleton height='30px' width='10%' />
            </>
         )}
      </Stack>
   ));

   return (
      <Box
         p={8}
         border='1px'
         borderRadius='md'
         borderColor='gray.200'
         width={width}
      >
         <Stack spacing={8}>{skeletonItems}</Stack>
      </Box>
   );
};

export default SkeletonLoaderTable;
