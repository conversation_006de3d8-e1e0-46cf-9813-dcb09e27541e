import { AxiosResponse } from 'axios';
import dashboardApiAgent from '../../agent';

// PAYLOADS
export interface RegisterPayload {
   full_name: string;
   email_address: string;
   password: string;
   cb_product_updates: boolean;
}

export interface LoginPayload {
   email_address: string;
   password: string;
}

export interface SendOtpPayload {
   email_address: string;
   action: 'email-verification' | 'password-reset';
}

export interface VerifyEmailPayload {
   email_address: string;
   email_otp: string;
   action: 'email-verification' | 'password-reset';
}

export interface IsEmailVerifiedPayload {
   email_address: string;
}

export interface ResetPasswordPayload {
   email_address: string;
   password: string;
}

// RESPONSES
export interface RegisterResponse {
   status: 'Success' | 'Failure';
   message: string;
   email_address: string;
   register_progress: string;
}
export interface LoginResponse extends RegisterResponse {
   refreshToken?: string;
   client_id?: string;
   full_name?: string;
}

export interface SendOtpResponse {
   status: 'Success' | 'Failure';
   message: string;
   email_address: string;
}

export interface VerifyEmailResponse {
   status: 'Success' | 'Failure';
   message: string;
   email_address: string;
   register_progress: string;
}

export interface IsEmailVerifiedResponse {
   status: 'Success' | 'Failure';
   message: string;
   email_address: string;
   is_email_verified: boolean;
}

export interface ResetPasswordResponse {
   status: 'Success' | 'Failure';
   message: string;
}

// ENDPOINTS
interface Endpoints {
   register: (
      payload: RegisterPayload,
   ) => Promise<AxiosResponse<RegisterResponse>>;
   login: (payload: LoginPayload) => Promise<AxiosResponse<LoginResponse>>;
   sendOtp: (
      payload: SendOtpPayload,
   ) => Promise<AxiosResponse<SendOtpResponse>>;
   verifyEmail: (
      payload: VerifyEmailPayload,
   ) => Promise<AxiosResponse<VerifyEmailResponse>>;
   isEmailVerified: (
      payload: IsEmailVerifiedPayload,
   ) => Promise<AxiosResponse<IsEmailVerifiedResponse>>;
   resetPassword: (
      payload: ResetPasswordPayload,
   ) => Promise<AxiosResponse<ResetPasswordResponse>>;
}

const authEndpoints: Endpoints = {
   register: (payload) => dashboardApiAgent.post('/auth1/register', payload),
   login: (payload) => dashboardApiAgent.post('/auth1/login', payload),
   sendOtp: (payload) => dashboardApiAgent.post('/auth1/send-otp', payload),
   verifyEmail: (payload) =>
      dashboardApiAgent.post('/auth1/verify-email', payload),
   isEmailVerified: (payload) =>
      dashboardApiAgent.get('/auth1/is-email-verified', {
         params: { email_address: payload.email_address },
      }),
   resetPassword: (payload) =>
      dashboardApiAgent.post('/auth1/reset-password', payload),
};

export default authEndpoints;
