import React from 'react';
import { tabProps } from '@/components/tabSection';
import PlanTabContent from './plan-topups/components/PlanTabContent';
import AlteringImg from '../../../assets/icons/alerting-agent-icon.webp';
import DiagnosticsIMg from '../../../assets/icons/diagnostic-agent.webp';
import AnalyticsIMg from '../../../assets/icons/analytics-agent.webp';
import Usage from './usages-billing/components/Usage';
import Billing from './usages-billing/components/Billing';

export const billingsTab: tabProps = {
   usage: {
      label: 'Usage',
      content: React.createElement(Usage, {}),
   },
   billing: {
      label: 'Billing',
      content: React.createElement(Billing, {}),
   },
};

export type Agents = {
   id: string;
   name: string;
   description: string;
   img: string;
   pricePerToken: number;
   features: string[];
};

export const plansTab: tabProps = {
   plans: {
      label: 'Plans',
      content: React.createElement(PlanTabContent, { attribute: 'plan' }),
   },
   topups: {
      label: 'Top-up Tokens',
      content: React.createElement(PlanTabContent, { attribute: 'topup' }),
   },
};

export const tabAttribute: { [x: string]: { title: string; desc: string } } = {
   plan: {
      title: 'Plans',
      desc: 'Manage your Flable AI plan, view usage, and access your billing history.',
   },
   topup: {
      title: 'Top-up Tokens',
      desc: 'Add More Tokens to Your Current Plan.',
   },
};

export const agentImg = {
   'CMO Mode (AI Recos)': AlteringImg,
   'Diagnostic Agent': DiagnosticsIMg,
   'Analytics Agent': AnalyticsIMg,
};

export const combinedExtraOptionsByAdSpend: { [key: string]: string[] } = {
   '$0 to $2500': ['1 from following', '2 from following'],
   '$2500 to $8500': ['1 from following', '5 from following'],
   '$8500 to $18000': ['1 from following', '10 from following'],
   'above $18000': ['1 from following', 'All platforms'],
};

export const combinedSupportOptionsByAdSpend: { [key: string]: string[] } = {
   '$0 to $2500': ['Self Service', 'Priority Support'],
   '$2500 to $8500': ['Self Service', '1 Call Weekly'],
   '$8500 to $18000': ['Self Service', 'Success Manager'],
   'above $18000': ['Self Service', 'Success Manager'],
};
