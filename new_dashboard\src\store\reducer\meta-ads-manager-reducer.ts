import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import {
   ChatLevels,
   CampaignDetails,
   ExtendedChatHistory,
   CampaignInsights,
   TableData,
   AdsetData,
   AdCreativeData,
} from '../../api/service/agentic-workflow/meta-ads-manager';
import { AdsetDetails } from '../../api/service/agentic-workflow/meta-ads-manager';
import { BudgetDetails } from '../../api/service/agentic-workflow/meta-ads-manager';
interface ExtendedChatHistoryWithPreview extends ExtendedChatHistory {
   id?: string;
   campaignPreview?: CampaignDetails;
   AdsetPreview?: AdsetDetails;
   adSettings?: {
      adCreativeId: string;
      adId: string;
      status: 'ACTIVE' | 'PAUSED';
      campaignName: string;
      adsetName: string;
      dailyBudget: string;
      locations: string;
      ageRange: string;
      gender: string;
      optimizationGoal: string;
      adImage: string;
   };
   campaignInsights?: CampaignInsights | null;
   hasTables?: boolean;
   tableData?: TableData;

   hasBudgetAnalysis?: boolean;
   budgetDetails?: BudgetDetails;
   budgetOptions?: string[];
   showBudopt?: boolean;
}

interface AdCreativeCardState {
   isEditing: boolean;
   editedMessage: string;
   selectedImage: File | null;
   isUploading: boolean;
   previewImage: string | null;
   uploadedImageUri: string | null;
   isExpanded: boolean;
   isPublishing: boolean;
   isSavingDraft: boolean;
   adCreativeId: string | null;
   adId: string | null;
}

interface MetaAdsManagerState {
   session_id: string;
   prompt: string;
   loading: boolean;
   currentChat: ExtendedChatHistoryWithPreview[] | null;
   chatLevel: ChatLevels;
   campaignDetails: CampaignDetails | null;
   AdsetDetails: AdsetDetails | null;
   adsetData: AdsetData | null;
   adsetFinalPrompts: string[];
   adCreativeData: AdCreativeData | null;
   adCreativeId: string | null;
   hasTables: boolean;
   createdAdCreatives: string[];
   isAnyAdCreativeCreated: boolean;
   adsetId: string | null;
   campaignInsights: CampaignInsights | null;
   adCreativeCardState: AdCreativeCardState;
}

const initialState: MetaAdsManagerState = {
   session_id: Date.now().toString(),
   prompt: '',
   loading: false,
   currentChat: null,
   chatLevel: 'campaign',
   campaignDetails: null,
   AdsetDetails: null,
   adsetData: null,
   adsetFinalPrompts: [],
   adCreativeData: null,
   adCreativeId: null,
   hasTables: false,
   createdAdCreatives: [],
   isAnyAdCreativeCreated: false,
   adsetId: null,
   campaignInsights: null,
   adCreativeCardState: {
      isEditing: false,
      editedMessage: '',
      selectedImage: null,
      isUploading: false,
      previewImage: null,
      uploadedImageUri: null,
      isExpanded: false,
      isPublishing: false,
      isSavingDraft: false,
      adCreativeId: null,
      adId: null,
   },
};

const metaAdsManagerSlice = createSlice({
   name: 'metaAdsManager',
   initialState,
   reducers: {
      setSessionId: (state, action: PayloadAction<string>) => {
         state.session_id = action.payload;
      },
      setPrompt: (state, action: PayloadAction<string>) => {
         state.prompt = action.payload;
      },
      setLoading: (state, action: PayloadAction<boolean>) => {
         state.loading = action.payload;
      },
      setCurrentChat: (
         state,
         action: PayloadAction<
            | ExtendedChatHistoryWithPreview[]
            | null
            | ((
                 prev: ExtendedChatHistoryWithPreview[] | null,
              ) => ExtendedChatHistoryWithPreview[] | null)
         >,
      ) => {
         if (typeof action.payload === 'function') {
            state.currentChat = action.payload(state.currentChat);
         } else {
            state.currentChat = action.payload;
         }
      },
      updateCurrentChat: (
         state,
         action: PayloadAction<ExtendedChatHistoryWithPreview>,
      ) => {
         if (state.currentChat) {
            state.currentChat = [...state.currentChat, action.payload];
         } else {
            state.currentChat = [action.payload];
         }
      },
      setChatLevel: (state, action: PayloadAction<ChatLevels>) => {
         state.chatLevel = action.payload;
      },
      setCampaignDetails: (
         state,
         action: PayloadAction<CampaignDetails | null>,
      ) => {
         state.campaignDetails = action.payload;
      },
      setAdsSetDetails: (state, action: PayloadAction<AdsetDetails | null>) => {
         state.AdsetDetails = action.payload;
      },
      setAdsetData: (state, action: PayloadAction<AdsetData | null>) => {
         state.adsetData = action.payload;
      },
      setAdsetFinalPrompts: (state, action: PayloadAction<string[]>) => {
         state.adsetFinalPrompts = action.payload;
      },
      setAdCreativeData: (
         state,
         action: PayloadAction<AdCreativeData | null>,
      ) => {
         state.adCreativeData = action.payload;
      },
      setAdCreativeId: (state, action: PayloadAction<string | null>) => {
         state.adCreativeId = action.payload;
      },
      setHasTables: (state, action: PayloadAction<boolean>) => {
         state.hasTables = action.payload;
      },
      addCreatedAdCreative: (state, action: PayloadAction<string>) => {
         if (!state.createdAdCreatives.includes(action.payload)) {
            state.createdAdCreatives.push(action.payload);
         }
      },
      setIsAnyAdCreativeCreated: (state, action: PayloadAction<boolean>) => {
         state.isAnyAdCreativeCreated = action.payload;
      },
      setAdsetId: (state, action: PayloadAction<string | null>) => {
         state.adsetId = action.payload;
      },
      setCampaignInsights: (
         state,
         action: PayloadAction<CampaignInsights | null>,
      ) => {
         state.campaignInsights = action.payload;
      },
      resetState: () => {
         return initialState;
      },
      updateAdCreativeCardState: (
         state,
         action: PayloadAction<Partial<AdCreativeCardState>>,
      ) => {
         state.adCreativeCardState = {
            ...state.adCreativeCardState,
            ...action.payload,
         };
      },
      resetAdCreativeCardState: (state) => {
         state.adCreativeCardState = initialState.adCreativeCardState;
      },

      resetMetaAdsManagerState: () => initialState,
   },
});

export const {
   setSessionId,
   setPrompt,
   setLoading,
   setCurrentChat,
   updateCurrentChat,
   setChatLevel,
   setCampaignDetails,
   setAdsSetDetails,
   setAdsetData,
   setAdsetFinalPrompts,
   setAdCreativeData,
   setAdCreativeId,
   setHasTables,
   addCreatedAdCreative,
   setIsAnyAdCreativeCreated,
   setAdsetId,
   setCampaignInsights,
   resetState,
   updateAdCreativeCardState,
   resetAdCreativeCardState,
   resetMetaAdsManagerState,
} = metaAdsManagerSlice.actions;

export default metaAdsManagerSlice.reducer;
