import React from 'react';
import './objective-menu.scss';

interface ObjectiveMenuSelectionProps {
   onSelect: (selectedObjective: string) => void;
   dynamicData: string[];
}

const ObjectiveMenuSelection: React.FC<ObjectiveMenuSelectionProps> = ({
   onSelect,
   dynamicData,
}) => {
   const handleSelect = (objective: string) => {
      onSelect(objective);
   };

   return (
      <div className='objective-menu'>
         {dynamicData.map((objective, index) => (
            <button
               key={index}
               className='objective-menu-btn'
               onClick={() => handleSelect(objective)}
            >
               {objective}
            </button>
         ))}
      </div>
   );
};

export default ObjectiveMenuSelection;
