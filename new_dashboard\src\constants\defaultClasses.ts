export const defaultTabListClass =
   'bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[20px]';

export const defaultTabTriggerClass =
   'p-3 data-[state=active]:bg-background' +
   'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring ' +
   'text-foreground inline-flex h-[calc(100%-1px)] flex-1 ' +
   'items-center justify-center gap-1.5 rounded-md border border-transparent text-sm font-medium ' +
   'whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 ' +
   'disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm ' +
   "[&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4";
