import { Box, Image, Heading, Text } from '@chakra-ui/react';

import TopDesignImage from '../../../assets/image/onboarding/setup-guide/top-design.png';
import RightDesignImage from '../../../assets/image/onboarding/setup-guide/right-design.png';
import BottomDesignImage from '../../../assets/image/onboarding/setup-guide/bottom-design.png';
import FlableIcon from '../../../assets/image/onboarding/setup-guide/flable-icon-white.png';
import SetupGuide from './setup-guide/setup-guide.tsx';
import { useAppSelector } from '../../../store/store.ts';

const Sidebar = () => {
   const { accountDetails } = useAppSelector((state) => state.onboarding);

   return (
      <Box
         width='30%'
         height='100%'
         bg='#437EEB'
         position='fixed'
         top={0}
         right={0}
         display='flex'
         alignItems='center'
         justifyContent='center'
         flexDirection='column'
         gap={5}
      >
         <Image
            src={TopDesignImage}
            position='absolute'
            zIndex={1}
            top='0'
            left='7%'
         />
         <Image
            src={RightDesignImage}
            position='absolute'
            zIndex={1}
            right='0'
            top='30%'
         />
         <Image
            src={BottomDesignImage}
            position='absolute'
            zIndex={1}
            bottom='0'
            right='15%'
         />
         <Box
            className='logo-container'
            position='absolute'
            zIndex={2}
            display='flex'
            justifyContent={'center'}
            alignItems='center'
            gap={3}
            top='5%'
            right='10%'
         >
            <Box className='logo'>
               <Image src={FlableIcon} alt='logo' color={'#fff'} />
            </Box>
            <Heading
               className='name'
               fontSize={36}
               fontWeight={400}
               fontFamily={'Poppins'}
               color={'#fff'}
               zIndex={2}
            >
               flable.ai
            </Heading>
         </Box>
         <Text color='#fff' fontSize={24} fontWeight='500' width='87%'>
            Setup Guide
         </Text>
         {accountDetails &&
            accountDetails.map(
               (account) =>
                  account.register_progress === 'Completed' && (
                     <Box
                        width='87%'
                        border='1px solid #fff'
                        borderRadius={8}
                        padding={2}
                     >
                        <Text
                           color='#fff'
                           fontSize={14}
                           fontWeight='500'
                           width='87%'
                        >
                           {`${account.company_name} added successfully`}
                        </Text>
                     </Box>
                  ),
            )}
         <SetupGuide />
         <Text textAlign='left' color='#fff' width='87%'>
            For any issues, contact us at{' '}
            <span style={{ fontWeight: 'bold' }}><EMAIL>.</span>
         </Text>
      </Box>
   );
};

export default Sidebar;
