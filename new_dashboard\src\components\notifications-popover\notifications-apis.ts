import { useApiMutation, useApiQuery } from '@/hooks/react-query-hooks';
import notificationsEndpoints, {
   Notification,
} from '@/api/service/notifications';
import { AuthUser } from '@/types/auth';
import { Keys, LocalStorageService } from '@/utils/local-storage';

export const useFetchAllNotificationsByUserID = () => {
   const { client_id, user_id } =
      LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails) || {};

   return useApiQuery<Notification[]>({
      queryKey: ['notifications', String(client_id), String(user_id)],
      queryFn: () =>
         notificationsEndpoints.fetchAllNotificationsByUserID({
            client_id: client_id || '',
            user_id: user_id || '',
         }),
      enabled: !!client_id && !!user_id,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
   });
};

export const useCreateNotificationMutation = () => {
   return useApiMutation({
      queryKey: ['create-notification'],
      mutationFn: notificationsEndpoints.createNotification,
   });
};

export const useMarkNotificationAsReadMutation = () => {
   return useApiMutation({
      queryKey: ['mark-notification-as-read'],
      mutationFn: notificationsEndpoints.markNotificationAsRead,
   });
};
