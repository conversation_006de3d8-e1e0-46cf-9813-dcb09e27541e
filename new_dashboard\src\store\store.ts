import {
   configureStore,
   ThunkAction,
   Action,
   combineReducers,
   createAction,
} from '@reduxjs/toolkit';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';
import sampleReducer from './reducer/sampleReducer';
import configSlice from './reducer/configReducer';
import mediaSlice from './reducer/user-details-reducer';
import modalReducer from './reducer/modal-reducer';
import contentIdeationReducer from './reducer/content-ideation-reducer';
import popoverReducer from './reducer/popover-reducer';
import kpiSlice from './reducer/kpi-reducer';
import trackedReducer from './reducer/tracked-reducer';
import dropdownReducer from './reducer/overview-dropdown-reducer';
import tourReducer from './reducer/tour-reducer';
import settingsReducer from './reducer/settings-reducer';
import integrationReducer from './reducer/integration-reducer';
import cfoReducer from './reducer/cfo-reducer';
import onboardingReducer from './reducer/onboarding-reducer';
import userManagementReducer from './reducer/user-management-reducer';
import alertingAgentReducer from './reducer/alerting-agent-reducer';
import analyticsAgentReducer from './reducer/analytics-agent-reducer';
import metaAdsManagerReducer from './reducer/meta-ads-manager-reducer';
import marcoReducer from './reducer/marco-reducer';
import subscriptionReducer from './reducer/subscriptionReducer';
import metaAdsAutoAgentReducer from './reducer/metaAdsAutoAgentReducer';
import customAlertsReducer from './reducer/custom-alerts-reducer';

const appReducer = combineReducers({
   sample: sampleReducer,
   config: configSlice,
   media: mediaSlice,
   modal: modalReducer,
   contentIdeation: contentIdeationReducer,
   popover: popoverReducer,
   kpi: kpiSlice,
   tracked: trackedReducer,
   dropdown: dropdownReducer,
   tour: tourReducer,
   settings: settingsReducer,
   integration: integrationReducer,
   cfo: cfoReducer,
   onboarding: onboardingReducer,
   userManagement: userManagementReducer,
   alertingAgent: alertingAgentReducer,
   metaAdsManager: metaAdsManagerReducer,
   marco: marcoReducer,
   analyticsAgent: analyticsAgentReducer,
   subscription: subscriptionReducer,
   metaAdsAutoAgent: metaAdsAutoAgentReducer,
   customAlerts: customAlertsReducer,
});

export const logoutAction = createAction('auth/logout');

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const rootReducer = (
   state: ReturnType<typeof appReducer> | undefined,
   action: Action,
) => {
   if (action.type === logoutAction.type) {
      // Reset state by setting it to undefined; each slice will fall back to its initial state
      state = undefined;
   }
   return appReducer(state, action);
};

const store = configureStore({
   reducer: rootReducer,
});

export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

export type AppDispatch = typeof store.dispatch;
export type RootState = ReturnType<typeof store.getState>;
export type AppThunk<ReturnType = void> = ThunkAction<
   ReturnType,
   RootState,
   unknown,
   Action<string>
>;
export const useAppDispatch = () => useDispatch<AppDispatch>();
export default store;
