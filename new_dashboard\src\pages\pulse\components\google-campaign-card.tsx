import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useAppSelector } from '../../../store/store';
import './performance-card.scss';
import { UserDetails } from './interface';
import LineChart from './linechart';
import pulseService, { Campaign } from '../../../api/service/pulse';
import {
   toUpperCase,
   toShowCurrency,
   truncateText,
   StatusTypes,
} from '../utils/helper';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import {
   Flex,
   Image,
   Text,
   Tooltip,
   useColorMode,
   useToast,
} from '@chakra-ui/react';
import trackedpin from '../../../assets/icons/tracked-pin.svg';
import { OverviewQueryKeys } from '../../dashboard/utils/query-keys';
import { useApiMutation } from '../../../hooks/react-query-hooks';
import GooglePopup from './google-campaign-pop-up';
import { button } from '../../../utils/strings/pulse-strings';
import {
   calculateHelper,
   specialMetrics,
} from '../../utils/kpiCalculaterHelper';

interface CardProps {
   TrackBtnIdG: string;
   googleViewId: string;
   data: Campaign;
   allcampaign: Campaign[];
   tracked: boolean;
}

const GoogleAdsPerformanceCard: React.FC<CardProps> = ({
   TrackBtnIdG,
   googleViewId,
   data,
   allcampaign,
   tracked,
}) => {
   const userDetails =
      LocalStorageService.getItem<UserDetails>(Keys.FlableUserDetails) ||
      ({} as UserDetails);
   const [isPopupOpen, setIsPopupOpen] = useState(false);
   const { objective, metric, channel } = useAppSelector(
      (state) => state.dropdown,
   );

   const handlePopupOpen = () => setIsPopupOpen(true);
   const handlePopupClose = () => setIsPopupOpen(false);
   const toast = useToast();
   const { colorMode } = useColorMode();

   const handleTrackWrapper = () => {
      void trackedKpiService.mutate({
         client_id: userDetails.client_id,
         kpi_name: metric,
         objective: objective,
         campaign_id: String(data.campaign_id),
         tracked: true,
         channel: channel,
      });
   };

   const handleError = (msg: string | null) =>
      toast({
         title: 'Error',
         description: msg,
         status: 'error',
         duration: 5000,
         isClosable: true,
      });

   const trackedKpiService = useApiMutation({
      mutationFn: pulseService.updateTrackedKpis,
      onSuccessHandler() {
         toast({
            title: 'Tracked successfully',
            status: 'success',
            duration: 3000,
            isClosable: true,
         });
      },
      onError(msg) {
         handleError(msg);
      },
      invalidateCacheQuery: [OverviewQueryKeys.daywiseCampaign],
   });

   const filteredChartData = data?.kpis?.filter(
      (kpi) => !isNaN(Number(kpi.kpi_value)) && kpi.kpi_name === metric,
   );
   const kpi_recommendationData = data?.day_wise_kpis?.[metric]
      ? Object.entries(data.day_wise_kpis[metric])
           .filter(([, value]) => value !== null)
           .sort(([, valueA], [, valueB]) => valueB - valueA)
      : [];
   const selectedKPIValue = data?.total_val?.[metric];
   const prevPeriodKPIValue = data?.prev_total_val?.[metric];
   const { percentage, color, direction, currentValue } = calculateHelper(
      metric,
      selectedKPIValue,
      prevPeriodKPIValue,
   );
   const arrow = direction === 'is up' ? '↑' : '↓';

   return (
      <div className='CardWrapper'>
         <div className='usercards'>
            <div className={`usercard-containers ${colorMode}`}>
               <div className='card-top'>
                  <Tooltip
                     label={
                        data?.campaign_name.length > 40
                           ? data?.campaign_name
                           : ''
                     }
                     placement='top'
                     fontSize='small'
                  >
                     <button className='campaign-name'>
                        <b>{truncateText(data?.campaign_name, false, 40)}</b>
                     </button>
                  </Tooltip>
                  <button className='campaign-status'>
                     <p
                        className={
                           data?.campaign_status === StatusTypes.ENABLED
                              ? 'campaign-status-active'
                              : data?.campaign_status === StatusTypes.REMOVED
                                ? 'campaign-status-removed'
                                : 'campaign-status-pause'
                        }
                     >
                        {data?.campaign_status}
                     </p>
                  </button>
               </div>
               <div className='chart-elements'>
                  <div className='elements'>
                     <h6 style={{ color }}>
                        <b>
                           {Number(percentage) ? `${percentage}% ${arrow}` : ''}
                        </b>
                     </h6>
                     <p>
                        <b>
                           {metric.length > 15 ? (
                              <Tooltip
                                 label={metric.toUpperCase()}
                                 hasArrow
                                 placement='bottom'
                              >
                                 {metric.slice(0, 15)}
                              </Tooltip>
                           ) : (
                              toUpperCase(metric)
                           )}
                           {Number(percentage) ? ` ${direction}` : ''}
                        </b>
                     </p>
                     <h4>
                        <b>
                           {currentValue !== 'N/A' &&
                              toShowCurrency(metric, data.currency)}
                           {currentValue}
                        </b>
                     </h4>
                  </div>
                  <div className='chart'>
                     {data && filteredChartData && currentValue !== 'N/A' && (
                        <LineChart
                           kpiDetails={{
                              displayName: data?.campaign_name,
                              allData: filteredChartData?.map((kpi) => ({
                                 date: kpi.kpi_date,
                                 kpi_value: parseFloat(
                                    Number(kpi.kpi_value)?.toFixed(2),
                                 ),
                              })),
                              stat: data?.kpis?.[0]?.campaign_status,
                           }}
                        />
                     )}
                  </div>
               </div>
               <div className='kpi-recommendation'>
                  {kpi_recommendationData.length > 1 &&
                     Math.floor(kpi_recommendationData?.[0]?.[1]) > 0 && (
                        <>
                           <button className='campaign-recommendation'>
                              <p
                                 className={` ${specialMetrics.includes(metric) ? 'campaign-recommendation-bad-day' : 'campaign-recommendation-good-day'}`}
                              >
                                 {`Highest on ${
                                    kpi_recommendationData?.[0]?.[1] &&
                                    kpi_recommendationData?.[0]?.[0]
                                 }s : ${Math.floor(
                                    kpi_recommendationData?.[0]?.[1] &&
                                       kpi_recommendationData?.[0]?.[1],
                                 )}`}
                              </p>
                           </button>
                           <button className='campaign-recommendation'>
                              <p
                                 className={` ${specialMetrics.includes(metric) ? 'campaign-recommendation-good-day' : 'campaign-recommendation-bad-day'}`}
                              >
                                 {`Lowest on ${
                                    kpi_recommendationData?.[
                                       kpi_recommendationData.length - 1
                                    ]?.[0]
                                 }s : ${Math.ceil(
                                    kpi_recommendationData?.[
                                       kpi_recommendationData.length - 1
                                    ]?.[1],
                                 )}`}
                              </p>
                           </button>
                        </>
                     )}
               </div>
               <div className='bottom'>
                  <hr className={`divider ${colorMode}`} />
                  <div className='bottom-buttons'>
                     {currentValue === 'N/A' || Number(currentValue) === 0 ? (
                        <Tooltip
                           hasArrow
                           label='No data available for this campaign'
                        >
                           <Text cursor='not-allowed'>View Details</Text>
                        </Tooltip>
                     ) : (
                        <Link
                           to='#'
                           onClick={handlePopupOpen}
                           id={googleViewId}
                        >
                           View Details
                        </Link>
                     )}
                     <Flex gap={2}>
                        {!tracked && (
                           <Image
                              src={trackedpin}
                              style={{
                                 filter:
                                    colorMode === 'dark' ? 'invert(1)' : 'none',
                              }}
                           />
                        )}
                        <button
                           id={TrackBtnIdG}
                           className={`track-button ${tracked ? 'tracking' : ''}`}
                           disabled={tracked}
                           onClick={handleTrackWrapper}
                        >
                           {tracked ? button.tracking : button.track}
                        </button>
                     </Flex>
                  </div>
               </div>
            </div>
         </div>
         {isPopupOpen && (
            <GooglePopup
               isOpen={isPopupOpen}
               onClose={handlePopupClose}
               details={`Detailed information about`}
               allCampaigns={allcampaign}
               data={data}
            />
         )}
      </div>
   );
};

export default GoogleAdsPerformanceCard;
