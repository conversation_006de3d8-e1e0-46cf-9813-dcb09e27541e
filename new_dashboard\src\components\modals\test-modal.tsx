import ModalWrapper from './modal-wrapper';

/**
 * Usage: In order to use this modal
 * 1. import this in modal manager and put this into modalsLookup
 * 2. Now based on whatever button click, you wanna ooen the modal,
 * just dispatch the action openModal with the same name of the modal as the modalType
 * dispatch(openModal({modalType:'TestModal'}))
 *
 */

function TestModal() {
   return (
      <ModalWrapper heading='Test' overlayBgcolor='#2424241c'>
         <p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Eum ad</p>
      </ModalWrapper>
   );
}

export default TestModal;
