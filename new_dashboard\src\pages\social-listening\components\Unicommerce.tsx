/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { useNavigate } from 'react-router-dom';
import { useEffect, useState } from 'react';
import Card from './Card';
import image from '../images/integrations/unicommerce.jpg';
import { channelNames } from '../utils/constant';
import endPoints from '../apis/agent';
import { connectDisconnectToUnicommerce } from '../utils';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { AuthUser } from '../../../types/auth';

interface UnicommerceConnectionDetails {
   is_active?: boolean;
}

const Unicommerce = () => {
   const navigate = useNavigate();

   const client_id = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   )?.client_id;
   const [connectionDetails, setConnectionDetails] =
      useState<UnicommerceConnectionDetails>({});
   const [isFetching, setIsFetching] = useState<boolean>(false);

   function handleNavigation() {
      navigate('/integrations/unicommerce');
   }

   useEffect(() => {
      if (!client_id) return;
      const fetchData = async () => {
         try {
            setIsFetching(true);
            const res = await endPoints.checkUnicommerceConnectionDetails({
               client_id,
               channel_name: channelNames.UNICOMMERCE,
            });
            const is_active = res.data;
            setConnectionDetails(is_active);
         } catch (error) {
            console.error('Error fetching connection details:', error);
         } finally {
            setIsFetching(false);
         }
      };

      void fetchData();
   }, []);

   async function handleDisconnect() {
      if (!client_id) return;
      try {
         const isConfirmed = confirm('Are you sure?');

         if (!isConfirmed) return;

         await connectDisconnectToUnicommerce({
            channel_name: channelNames.UNICOMMERCE,
            client_id,
            isConnect: false,
         });
         setConnectionDetails({});
      } catch (err) {
         console.log(err);
      }
   }

   return (
      <Card
         isConnected={connectionDetails.is_active}
         isFetching={isFetching}
         heading={'Unicommerce'}
         src={image}
         onButtonClick={
            connectionDetails.is_active ? handleDisconnect : handleNavigation
         }
      />
   );
};

export default Unicommerce;
