import React, { useEffect, useState, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Skeleton } from '@/components/ui/skeleton';

interface SummaryAgentMessageProps {
   summary: string | null;
   loading: boolean;
   onType?: () => void;
   onComplete?: () => void;
   scrollRef?: React.RefObject<HTMLDivElement>;
   disableAnimation?: boolean;
}

const getFromStorage = (key: string): string | null => {
   try {
      return localStorage.getItem(key);
   } catch (error) {
      console.warn('Failed to access localStorage:', error);
      return null;
   }
};

const setToStorage = (key: string, value: string): void => {
   try {
      localStorage.setItem(key, value);
   } catch (error) {
      console.warn('Failed to set localStorage:', error);
   }
};

const SummaryAgentMessage: React.FC<SummaryAgentMessageProps> = ({
   summary,
   loading,
   onType,
   onComplete,
   scrollRef,
   disableAnimation = false,
}) => {
   const [displayText, setDisplayText] = useState('');
   const [currentIndex, setCurrentIndex] = useState(0);
   const [hasAnimated, setHasAnimated] = useState(false);
   const lastSummary = useRef<string | null>(null);
   const animationKey = `summary_${summary?.substring(0, 50) || 'default'}`;

   const scrollToBottom = () => {
      if (scrollRef?.current) {
         scrollRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'end',
            inline: 'nearest',
         });
      }
   };

   const scrollToBottomEnhanced = () => {
      if (scrollRef?.current) {
         const container = scrollRef.current.closest('.overflow-y-auto');
         if (container) {
            container.scrollTo({
               top: container.scrollHeight,
               behavior: 'smooth',
            });
         } else {
            scrollRef.current.scrollIntoView({
               behavior: 'smooth',
               block: 'end',
               inline: 'nearest',
            });
         }
      }
   };

   useEffect(() => {
      if (summary && !loading && !hasAnimated) {
         setTimeout(() => {
            scrollToBottom();
         }, 50);
      }
   }, [summary, loading, hasAnimated]);

   useEffect(() => {
      if (!loading && summary && !hasAnimated) {
         setTimeout(() => {
            scrollToBottom();
         }, 100);
      }
   }, [loading, summary, hasAnimated]);

   useEffect(() => {
      if (loading) {
         setDisplayText('');
         setCurrentIndex(0);
         setHasAnimated(false);
         lastSummary.current = null;
         return;
      }

      if (disableAnimation && summary) {
         setDisplayText(summary);
         setCurrentIndex(summary.length);
         setHasAnimated(true);

         setToStorage(animationKey, 'true');
         if (onComplete) {
            setTimeout(() => {
               onComplete();
            }, 100);
         }
         return;
      }

      const isAlreadyAnimated = getFromStorage(animationKey) === 'true';

      if (isAlreadyAnimated && summary) {
         setDisplayText(summary);
         setCurrentIndex(summary.length);
         setHasAnimated(true);
         return;
      }

      if (summary !== lastSummary.current) {
         setDisplayText('');
         setCurrentIndex(0);
         setHasAnimated(false);
         lastSummary.current = summary;
      }

      if (summary && !hasAnimated && currentIndex < summary.length) {
         const timer = setTimeout(() => {
            setDisplayText((prev) => prev + summary[currentIndex]);
            setCurrentIndex((prev) => prev + 1);

            if (onType && currentIndex % 3 === 0) {
               onType();
            }
            if (currentIndex % 3 === 0) {
               setTimeout(() => {
                  scrollToBottom();
               }, 10);
            }
         }, 10);
         return () => clearTimeout(timer);
      } else if (summary && !hasAnimated && currentIndex >= summary.length) {
         setHasAnimated(true);

         setToStorage(animationKey, 'true');

         if (onComplete) {
            setTimeout(() => {
               onComplete();
            }, 100);
         }

         setTimeout(() => {
            scrollToBottomEnhanced();
         }, 150);
      }
   }, [
      summary,
      loading,
      currentIndex,
      hasAnimated,
      animationKey,
      onType,
      onComplete,
      disableAnimation,
   ]);

   if (loading) {
      return (
         <div className='flex justify-start w-[100%] mt-2'>
            <div className='w-[100%] text-white p-[12px_14px]'>
               <div className='flex flex-col gap-4'>
                  <Skeleton className='h-4 w-[75%]' />
                  <Skeleton className='h-4 w-[75%]' />
                  <Skeleton className='h-4 w-[75%]' />
               </div>
            </div>
         </div>
      );
   }

   if (!summary) return null;

   if (hasAnimated) {
      return (
         <div className=' p-6 text-[17px] leading-9 font-nunito text-gray-800'>
            <ReactMarkdown
               remarkPlugins={[remarkGfm]}
               components={{
                  strong: ({ ...props }) => (
                     <strong className='font-bold [color:#333333]' {...props} />
                  ),
                  p: ({ ...props }) => <p className='mb-3' {...props} />,
               }}
            >
               {summary}
            </ReactMarkdown>
         </div>
      );
   }

   return (
      <div className=' p-6 text-[17px] leading-9 font-nunito text-gray-800'>
         <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            components={{
               strong: ({ ...props }) => (
                  <strong className='font-bold [color:#333333]' {...props} />
               ),
               p: ({ ...props }) => <p className='mb-3' {...props} />,
            }}
         >
            {displayText}
         </ReactMarkdown>
      </div>
   );
};

export default SummaryAgentMessage;
