import { useEffect, useState } from 'react';
import { Flex, Stack, Text, Box, useColorMode } from '@chakra-ui/react';
import Competitor from './competitors';
import Preferences from './preferences';
import {
   settingsIndex,
   settingsModes,
} from '../../utils/strings/settings-strings';
import Reports from './reports';
import { Modes } from './interface';
import CostSettings from './cost-setting/cost-setting';
import ApiToken from './api-token';
import AttributionSettings from './attributions/attribution-settings';

import introJs from 'intro.js';
import { TooltipPosition } from 'intro.js/src/packages/tooltip';
import { componentNames, setFlag } from '../../store/reducer/tour-reducer';
import { useAppSelector } from '../../store/store';
import { useDispatch } from 'react-redux';

import { useSearchParams } from 'react-router-dom';
import { cn } from '@/utils';
import PlanTopups from './subscription/plan-topups';
import UsagesBilling from './subscription/usages-billing';
import { plansTab, billingsTab } from './subscription/constants';

const modesWithTabs = ['plansTopups', 'usagesBilling'] as Modes[];

const modeToTabObject: Record<
   string,
   Record<string, { label: string; content: React.ReactNode }>
> = {
   plansTopups: plansTab,
   usagesBilling: billingsTab,
};

function Settings() {
   const [searchParams, setSearchParams] = useSearchParams();
   const modeParam = searchParams.get('mode');
   const defaultMode: Modes = modeParam ? (modeParam as Modes) : 'languages';
   const [mode, setMode] = useState<Modes>(defaultMode);

   const { colorMode } = useColorMode();
   const isDarkTheme = colorMode === 'dark';
   const intro = introJs();
   const dispatch = useDispatch();
   const { settings } = useAppSelector((state) => state.tour);

   const steps: {
      element: string;
      intro: string;
      position: TooltipPosition;
   }[] = [
      {
         element: '#title-languages',
         intro: 'In Language & Timezone settings, you can update your language, timezone and region.',
         position: 'top',
      },
      {
         element: '#title-competitors',
         intro: 'In competitors, you can add your competitors social media pages. This helps you get insights of your competitors performance on various social media.',
         position: 'top',
      },
      {
         element: '#title-reports',
         intro: 'In reports, you can setup and subscribe for reports. This helps you get regular reports on your email.',
         position: 'top',
      },
      {
         element: '#title-costsettings',
         intro: 'In cost setting you can setup your various types of costs. This helps you view your P&L correctly.',
         position: 'top',
      },
   ];

   const startTour = () => {
      intro.setOptions({ steps });
      void intro.start();

      dispatch(
         setFlag({
            componentName: componentNames.SETTINGS,
            flag: false,
         }),
      );
   };

   useEffect(() => {
      if (settings) startTour();
   }, [settings]);

   const handleModeChange = (newMode: Modes) => {
      const newParams = new URLSearchParams(searchParams);
      newParams.set('mode', newMode);

      if (modesWithTabs.includes(newMode)) {
         const tabObject = modeToTabObject[newMode];
         const firstTab = Object.keys(tabObject)[0];
         newParams.set('tab', firstTab);
      } else {
         newParams.delete('tab');
      }

      setSearchParams(newParams);
      setMode(newMode);
   };

   useEffect(() => {
      if (modeParam && modeParam !== mode) {
         setMode(modeParam as Modes);

         if (modesWithTabs.includes(modeParam as Modes)) {
            const tabObject = modeToTabObject[modeParam];
            const firstTab = Object.keys(tabObject)[0];
            const urlTab = searchParams.get('tab');
            if (!urlTab || !tabObject[urlTab]) {
               searchParams.set('tab', firstTab);
               setSearchParams(searchParams);
            }
         } else {
            if (searchParams.has('tab')) {
               searchParams.delete('tab');
               setSearchParams(searchParams);
            }
         }
      }
   }, [modeParam]);

   const MAP_COMPONENT: {
      [key: string]: JSX.Element;
   } = {
      languages: <Preferences />,
      competitors: <Competitor />,
      reports: <Reports />,
      costsettings: <CostSettings />,
      apitoken: <ApiToken />,
      attributions: <AttributionSettings setMode={handleModeChange} />,
      plansTopups: <PlanTopups />,
      usagesBilling: <UsagesBilling />,
   };

   return (
      <Flex direction='row' height='100%'>
         {/* Sidebar */}
         <Flex
            flex={1}
            direction='column'
            pt='2em'
            px='1em'
            maxWidth='250px'
            borderRight='1px solid #F0F0F0'
         >
            <Text
               fontSize='large'
               fontWeight='600'
               color={isDarkTheme ? 'white' : 'black'}
            >
               {settingsIndex.title}
            </Text>
            <Stack mt='1em' gap='2em'>
               {settingsModes.map((m) => (
                  <Box
                     key={m.key}
                     onClick={() => handleModeChange(m.key)}
                     cursor='pointer'
                     id={`title-${m.key}`}
                  >
                     <span
                        className={cn(
                           'para4 font-semibold text-charcoal',
                           mode === m.key
                              ? 'text-cerulean'
                              : isDarkTheme
                                ? 'text-white'
                                : 'text-black',
                        )}
                     >
                        {m.title}
                     </span>
                     <br />
                     <span
                        className={cn(
                           'para6 text-charcoal',
                           mode === m.key
                              ? 'text-cerulean'
                              : isDarkTheme
                                ? 'text-white'
                                : 'text-black',
                        )}
                     >
                        {m.description}
                     </span>
                  </Box>
               ))}
            </Stack>
         </Flex>

         {/* Main Content */}
         <Flex flex={4} overflow='hidden'>
            {MAP_COMPONENT[mode]}
         </Flex>
      </Flex>
   );
}

export default Settings;
