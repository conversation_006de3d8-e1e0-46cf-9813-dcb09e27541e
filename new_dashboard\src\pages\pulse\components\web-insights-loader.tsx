import { Box, Skeleton, Stack, SimpleGrid } from '@chakra-ui/react';

const WebAnalyticsSkeleton = ({
   spacing = { base: 4, sm: 6, md: 8, lg: 10 },
   length = 12,
}) => {
   return (
      <Box p={5}>
         <SimpleGrid columns={{ base: 1, sm: 2, xl: 3 }} spacing={spacing}>
            {Array.from({ length }).map((_, index) => (
               <Box
                  key={index}
                  p={5}
                  boxShadow='md'
                  borderWidth='1px'
                  borderRadius='md'
                  overflow='hidden'
               >
                  <Stack spacing={4}>
                     <Skeleton height='20px' width='100%' />
                     <Skeleton height='20px' width='100%' />
                  </Stack>
               </Box>
            ))}
         </SimpleGrid>
      </Box>
   );
};

export default WebAnalyticsSkeleton;
