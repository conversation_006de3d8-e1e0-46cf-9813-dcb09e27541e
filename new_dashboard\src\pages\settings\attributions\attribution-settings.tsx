import CaretRight from '../../../assets/icons/attribution-settings/CaretRight.svg';
import metaIcon from '../../../assets/icons/pulse/meta.png';
import googleAdsIcon from '../../../assets/icons/kpi/gads.png';

import { useNavigate } from 'react-router-dom';
import { Modes } from '../interface';

type AttributionSettingsProps = {
   setMode: (mode: Modes) => void;
};

function AttributionSettings({ setMode }: AttributionSettingsProps) {
   const navigate = useNavigate();

   return (
      <div className='w-full px-4 sm:px-6 lg:px-8 py-6 bg-gray-50'>
         <div className='flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4'>
            <div className='text-2xl font-semibold text-gray-900'>
               Attribution Settings
            </div>
            <div className='flex items-center text-sm text-gray-500 mt-2 sm:mt-0'>
               <span
                  className='cursor-pointer hover:text-gray-700'
                  onClick={() => {
                     setMode('attributions');
                     navigate('/settings');
                  }}
               >
                  Settings
               </span>
               <img src={CaretRight} alt='chevron' className='mx-1 w-3 h-3' />
               <span className='text-gray-700'>Attribution Agent</span>
            </div>
         </div>

         <div className='mb-6 text-gray-600'>
            <p className='w-full text-sm sm:text-base leading-relaxed'>
               Our Attribution Agent connects data across all channels to give
               you a clear, accurate view of what's really driving conversions.
               Go beyond last-touch models and make confident, data-backed
               marketing decisions.
            </p>
         </div>

         <div className='flex flex-wrap gap-6'>
            <div className='bg-white rounded-[21px] border border-gray-200 shadow-sm w-full sm:w-[201px] h-[251px] flex flex-col items-center justify-center py-8 px-4 space-y-8'>
               <img src={metaIcon} alt='Meta' className='w-20 h-20' />
               <h3 className='text-base font-medium text-gray-800'>Meta Ads</h3>
               <button
                  onClick={() =>
                     navigate('/settings/attribution-settings/meta-ads', {
                        state: { from: 'attributions' },
                     })
                  }
                  className='bg-blue-500 hover:bg-blue-600 text-white py-2 px-3 rounded-md text-sm font-medium w-[110px] text-center cursor-pointer'
               >
                  Get Started
               </button>
            </div>

            <div className='bg-white rounded-[21px] border border-gray-200 shadow-sm w-full sm:w-[201px] h-[251px] flex flex-col items-center justify-center py-8 px-4 space-y-8'>
               <img
                  src={googleAdsIcon}
                  alt='Google Ads'
                  className='w-20 h-20'
               />
               <h3 className='text-base font-medium text-gray-800'>
                  Google Ads
               </h3>
               <button
                  onClick={() =>
                     navigate('/settings/attribution-settings/google-ads', {
                        state: { from: 'attributions' },
                     })
                  }
                  className='bg-blue-500 hover:bg-blue-600 text-white py-2 px-3 rounded-md text-sm font-medium w-[110px] text-center cursor-pointer'
               >
                  Get Started
               </button>
            </div>
         </div>
      </div>
   );
}

export default AttributionSettings;
