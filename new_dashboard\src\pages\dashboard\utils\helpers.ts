import { addMilliseconds } from 'date-fns';
import { defaultStaticRanges } from './default-ranges';
import {
   DateRang<PERSON>,
   KPIAgg,
   KPIData,
   KPIMeta,
   KPIRange,
   KPIStoreRange,
   MetaCatAgg,
   TimeRange,
} from './interface';
import { TrackKPI } from '../../pulse/components/interface';

export const getPrevPeriod = (dateRange: KPIRange): KPIRange => {
   const timeDiff = dateRange.start.getTime() - dateRange.end.getTime();
   return {
      end: addMilliseconds(dateRange.end, timeDiff - 1000),
      start: addMilliseconds(dateRange.start, timeDiff),
   };
};

export const getFullMonth = (num: string): string => {
   const monthNames = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
   ];
   return monthNames[Number(num) - 1] || '';
};

export const getMetricDescription = (key: string): string => {
   const descriptions: {
      [key: string]: string;
   } = {
      date_tim: 'Date',
      roas: 'ROAS',
      cpp: 'CPP',
      cpc: 'CPC',
      ctr: 'CTR',
      unique_ctr: 'Unique CTR',
      cpm: 'CPM',
      frequency: 'Ad Frequency',
      purchase: 'Purchase',
      clicks: 'Clicks',
      unique_clicks: 'Unique Clicks',
      reach: 'Reach',
      impressions: 'Impressions',
      spend: 'Spend',
      add_to_cart: 'Add to Cart',
      campaign_name: 'Campaign Name',
   };

   return descriptions[key] || key;
};

export const getPercent = (val1: number, val2: number): [string, boolean] => {
   val1 = Number(val1);
   val2 = Number(val2);
   if (val1 == val2 && val1 == 0) {
      return ['0', true];
   }
   let diff = val1 - val2;
   let percentDiff;
   if (diff == 0) diff = 1;
   if (val2 <= 0) val2 = 1;
   if (diff > 0) {
      percentDiff = (diff / val2) * 100;
   } else percentDiff = ((diff * -1) / val2) * 100;
   return [
      (Math.ceil(percentDiff * 100) / 100).toLocaleString('en-IN'),
      diff > 0,
   ];
};

export const getFormattedVal = (val: number) => {
   return val.toLocaleString('en-IN');
};

export const getLabel = (dateRange: TimeRange, noDefault?: boolean): string => {
   if (!noDefault) {
      const defaultLabel = defaultStaticRanges.filter((x) =>
         x.isSelected(dateRange),
      )[0]?.label;
      if (defaultLabel) return defaultLabel;
   }
   const isYearReq =
      !!(dateRange.endDate.getFullYear() - dateRange.startDate.getFullYear()) ||
      new Date().getFullYear() != dateRange.startDate.getFullYear();
   return `${getUserDate(dateRange.startDate, !!isYearReq)} - ${getUserDate(dateRange.endDate, !!isYearReq)}`;
};

export const getFullData = (
   dateRange: KPIStoreRange,
   data: KPIData[],
   groupBy: string,
): KPIData[] => {
   if (groupBy !== 'day') return data;

   const startDate = new Date(dateRange.start);
   const endDate = new Date(dateRange.end);

   const dateArray: KPIData[] = [];
   let i = 0;

   while (startDate.getTime() <= endDate.getTime()) {
      const currData = data[i] || dateArray[0];
      const dataD = new Date(currData.date);

      if (
         dataD.getFullYear() === startDate.getFullYear() &&
         dataD.getMonth() === startDate.getMonth() &&
         dataD.getDate() === startDate.getDate()
      ) {
         dateArray.push(currData);
         i++;
      } else {
         dateArray.push({
            ...currData,
            date: `${startDate.getFullYear()}-${startDate.getMonth() + 1}-${startDate.getDate()}`,
            kpi_value: 0,
         });
      }

      startDate.setDate(startDate.getDate() + 1);
   }

   return dateArray;
};

export const getChartDateLabel = (
   data: KPIData[] | TrackKPI[],
   groupBy: string,
): string[] => {
   if (groupBy == 'day')
      return data.map((x) =>
         getUserDate(new Date(x.date.split('T')[0]), false),
      );
   return data.map((x) => {
      const [date1, date2] = x.date.split(' - ');
      return getLabel(
         { startDate: new Date(date1), endDate: new Date(date2) },
         true,
      );
   });
};

export const isValidDateString = (str: string): boolean => {
   const ymd = str.split('T')[0].split('-');
   if (ymd.length !== 3 || ymd.join('').length !== 8) return false;
   return new Date(str).toString() !== 'Invalid Date';
};

export const getUserDate = (date: Date, isYearReq: boolean): string => {
   const month = date.toLocaleString('en-EN', { month: 'short' });
   const dateNum = date.getDate();
   const year = date.getFullYear();
   return `${month} ${dateNum}${isYearReq ? ', ' + year : ''}`;
};
export const toHHMMSS = (secs: number) => {
   const hours = Math.floor(secs / 3600);
   const minutes = Math.floor(secs / 60) % 60;
   const seconds = secs % 60;

   return [hours, minutes, seconds]
      .map((v) => (v < 10 ? '0' + v : v))
      .map((v) => v.toString().substring(0, 2))
      .join(':');
};

export const getPinnedAgg = (
   data: KPIMeta[],
   kpis: KPIAgg[],
   prevKpis: KPIAgg[],
) => {
   const pinned = data.filter((kpi) => kpi.pinned);
   const pinnedKpiAgg: KPIAgg = {};
   const pinnedPrevAgg: KPIAgg = {};
   const filteredMeta = data.filter((kpi) => {
      let available = false;
      kpis.forEach((cat) => {
         if (cat[kpi.kpi] && cat[kpi.kpi].category == kpi.category) {
            available = true;
         }
      });
      return available;
   });
   pinned.forEach((pin) => {
      const kpiDetails = kpis.filter((value) => value[pin.kpi])[0];
      if (kpiDetails) {
         pinnedKpiAgg[pin.kpi] = kpiDetails[pin.kpi];
      }
      const prevKpiDetails = prevKpis.filter((value) => value[pin.kpi])[0];
      if (prevKpiDetails) {
         pinnedPrevAgg[pin.kpi] = prevKpiDetails[pin.kpi];
      }
   });
   return { pinnedKpiAgg, pinnedPrevAgg, filteredMeta };
};

export const getAddedAvailable = (
   cat: string,
   kpiMeta: KPIMeta[],
): { added: KPIMeta[]; available: KPIMeta[] } => {
   let added = [],
      available = [];
   if (cat == 'pinned') {
      added = kpiMeta
         .filter((kpi) => kpi.pinned)
         .sort((a, b) => a.pinned_order - b.pinned_order);
      available = kpiMeta
         .filter((kpi) => !kpi.pinned)
         .sort((a, b) => a.pinned_order - b.pinned_order);
   } else {
      added = kpiMeta
         .filter((kpi) => kpi.category == cat && kpi.visible)
         .sort((a, b) => a.visible_order - b.visible_order);
      available = kpiMeta
         .filter((kpi) => kpi.category == cat && !kpi.visible)
         .sort((a, b) => a.visible_order - b.visible_order);
   }
   return { added, available };
};
// a little function to help us with reordering the result
export const reorder = (
   list: KPIMeta[],
   startIndex: number,
   endIndex: number,
) => {
   const result = Array.from(list);
   const [removed] = result.splice(startIndex, 1);
   result.splice(endIndex, 0, removed);

   return result;
};
export const filterVisibleKPI = (
   isPinned: boolean,
   catData: KPIAgg,
   metaData: KPIMeta[],
   category: string,
): { filteredCat: KPIAgg; filteredMeta: KPIMeta[] } => {
   const filteredCat: KPIAgg = {};
   const availableKPI = metaData.filter((kpi) => {
      return isPinned ? kpi.pinned : kpi.visible;
   });
   const filteredMeta = metaData.filter((kpi) => {
      if (kpi.category !== category) return true;
      return !!catData[kpi.kpi];
   });
   availableKPI.forEach((kpi) => {
      if (
         catData[kpi.kpi] &&
         catData[kpi.kpi].allData?.length > 0 &&
         catData[kpi.kpi].allData[0].category == kpi.category
      ) {
         filteredCat[kpi.kpi] = { ...catData[kpi.kpi] };
         filteredCat[kpi.kpi].visible = kpi.visible;
         filteredCat[kpi.kpi].pinned = kpi.pinned;
         filteredCat[kpi.kpi].order = isPinned
            ? kpi.pinned_order
            : kpi.visible_order;
      }
   });
   return { filteredCat, filteredMeta };
};

export const isDisabledGroupBy = (
   dateRange: DateRange,
   group: string,
): boolean => {
   const diffInTime =
      dateRange.endDate.getTime() - dateRange.startDate.getTime();
   const diffInDays = diffInTime / (1000 * 60 * 60 * 24);
   if (group == 'week') return diffInDays < 14;
   else if (group == 'month') return diffInDays < 60;
   else if (group == 'quarter') return diffInDays < 240;
   return false;
};

export const getValidGroupBy = (
   end: Date,
   start: Date,
   group: string,
): string => {
   const diffInTime = end.getTime() - start.getTime();
   const diffInDays = diffInTime / (1000 * 60 * 60 * 24);
   if (group == 'quarter') {
      if (diffInDays > 240) return 'quarter';
      else if (diffInDays > 60) return 'month';
      else if (diffInDays > 14) return 'week';
   } else if (group == 'month') {
      if (diffInDays > 60) return 'month';
      else if (diffInDays > 14) return 'week';
   } else if (group == 'week' && diffInDays > 14) return 'week';
   return 'day';
};

export const getAggData = (data: KPIMeta[]): MetaCatAgg => {
   const aggCategoryData = {} as MetaCatAgg;

   data.forEach((x) => {
      const cat = x.category.trim();
      if (aggCategoryData[cat]) {
         aggCategoryData[cat].push(x);
      } else {
         aggCategoryData[cat] = [x];
      }
   });
   return aggCategoryData;
};

export const getUserTime = (str: string): string => {
   const [hr, sec] = str.split(':').map(Number);
   return `${hr % 12 < 10 ? '0' : ''}${hr != 12 ? hr % 12 : 12}: ${sec < 10 ? '0' : ''}${sec} ${hr >= 12 ? 'PM' : 'AM'}`;
};

// export const calculateRawValue = (
//    kpiDetails: KPIDetails,
//    anomaly: anomalyData | null,
// ): number | null => {
//    const kpiName = kpiDetails?.kpi_names;

//    if (!kpiName) return null;

//    const rawValue = anomaly?.[kpiName]?.percentage_change ?? null;

//    if (rawValue === null) return null;

//    return specialMetrics.includes(kpiName) ? -rawValue : rawValue;
// };

export const anomalyKeys = {
   facebookads: 'meta',
   googleads: 'google',
   web: 'web',
   store: 'shopify',
   amazon_ads: 'amazon_ads',
};
