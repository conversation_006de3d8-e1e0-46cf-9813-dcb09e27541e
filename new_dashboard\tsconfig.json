{
   "compilerOptions": {
      "forceConsistentCasingInFileNames": true,
      "target": "ES2020",
      "useDefineForClassFields": true,
      "lib": ["ES2020", "DOM", "DOM.Iterable"],
      "module": "ESNext",
      "skipLibCheck": true,

      /* Bundler mode */
      "moduleResolution": "bundler",
      "allowImportingTsExtensions": true,
      "resolveJsonModule": true,
      "isolatedModules": true,
      "noEmit": true,
      "jsx": "react-jsx",

      /* Linting */
      "strict": true,
      "noUnusedLocals": true,
      "noUnusedParameters": true,
      "noFallthroughCasesInSwitch": true,
      "baseUrl": ".",
      "paths": {
         "@/*": ["./src/*"]
      }
   },
   "include": [".eslintrc.cjs", "src", "types.d.ts"],
   "references": [{ "path": "./tsconfig.node.json" }]
}
