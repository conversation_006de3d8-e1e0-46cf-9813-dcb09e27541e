import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaEye, FaEyeSlash } from 'react-icons/fa';
import {
   Box,
   Button,
   FormControl,
   Input,
   Image,
   useToast,
   FormErrorMessage,
   InputGroup,
   InputRightElement,
   Text,
   FormLabel,
} from '@chakra-ui/react';
import { setPasswordStrings } from '../../utils/strings/login-strings';
import { appStrings } from '../../utils/strings/app-strings';

import ICON from '../../assets/icons/icon.png';
import { regex } from '../../utils/strings/auth-strings';
import { useApiMutation } from '../../hooks/react-query-hooks';
import userManagementEndpoints, {
   CreateUpdateUserPayload,
} from '../../api/service/users';
import { userManagementKeys } from '../../utils/strings/query-keys';
import { addEditUserPageStrings } from '../../utils/strings/user-managament-strings';
import { useQueryClient } from '@tanstack/react-query';
import { logoutAction, useAppDispatch } from '@/store/store';

function SetPassword() {
   const navigate = useNavigate();
   const toast = useToast();
   const queryClient = useQueryClient();
   const dispatch = useAppDispatch();

   const urlParams = new URLSearchParams(window.location.search);

   const user_exists: string = urlParams.get('user_exists') as string;

   const [showPassword, setShowPassword] = useState({
      password: false,
      confirm_password: false,
   });

   const [form, setForm] = useState({
      password: '',
      confirm_password: '',
   });

   const [error, setError] = useState({
      password: '',
      confirm_password: '',
   });

   const createUserMutation = useApiMutation({
      queryKey: [userManagementKeys.createUser],
      mutationFn: userManagementEndpoints.createUser,
      onSuccessHandler: () => {
         toast({
            title: 'Success',
            description: addEditUserPageStrings.createUserSuccess,
            status: 'success',
            duration: 5000,
            isClosable: true,
         });

         navigate('/auth/login');
      },
      onError: () => {
         toast({
            title: 'Failed',
            description: addEditUserPageStrings.createUserFailed,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      },
   });

   const handleCreateUser = () => {
      if (form.password !== form.confirm_password) {
         toast({
            title: 'Failed',
            description: addEditUserPageStrings.passwordsDontMatch,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });

         return;
      }

      const createUserPayload: CreateUpdateUserPayload = {
         client_id: urlParams.get('client_id') as string,
         email_address: urlParams.get('email_address') as string,
         cb_product_updates: urlParams.get('cb_product_updates') as 'Y' | 'N',
         company_url: urlParams.get('company_url') as string,
         country: urlParams.get('country') as string,
         create_date: urlParams.get('create_date') as string,
         full_name: urlParams.get('full_name') as string,
         language: urlParams.get('language') as string,
         last_update_date: urlParams.get('last_update_date') as string,
         user_active: urlParams.get('user_active') as 'Y' | 'N',
         user_role: urlParams.get('user_role') as 'Admin' | 'Contributor',
         profile_image: urlParams.get('profile_image') as string,
         is_active: true,
      };

      const updatedPayload =
         user_exists === 'Y'
            ? createUserPayload
            : {
                 ...createUserPayload,
                 password: form.password,
                 confirm_password: form.confirm_password,
                 register_progress: 'Completed',
              };

      createUserMutation.mutate({
         ...updatedPayload,
         user_confirmed: 'Y',
      });
   };

   const validateFields = (name: string, value: string) => {
      switch (name) {
         case 'password':
            if (!regex.password.test(value)) {
               setError((prevErrors) => ({
                  ...prevErrors,
                  password:
                     'Password must be at least 8 characters long, must have atleast one number and one special character',
               }));
            } else {
               setError((prevErrors) => ({
                  ...prevErrors,
                  password: '',
               }));
            }

            break;

         case 'confirm_password':
            if (!regex.password.test(value)) {
               setError((prevErrors) => ({
                  ...prevErrors,
                  confirm_password:
                     'Password must be at least 8 characters long, must have atleast one number and one special character',
               }));
            } else {
               setError((prevErrors) => ({
                  ...prevErrors,
                  confirm_password: '',
               }));
            }
      }
   };

   const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setForm((prevForm) => ({
         ...prevForm,
         [name]: value,
      }));
      validateFields(name, value);
   };

   const handleClearCache = () => {
      queryClient.clear();
      sessionStorage.clear();
      localStorage.clear();
      dispatch(logoutAction());
   };

   useEffect(() => {
      handleClearCache();
   }, []);

   return (
      <>
         <Box
            display='flex'
            justifyContent='center'
            alignItems='center'
            mt={10}
            mb={10}
         >
            <Image src={ICON} alt='Flable Icon' w='15%' />
            <Box ml={2} fontSize='xl' fontWeight='bold'>
               {appStrings.companyName}
            </Box>
         </Box>
         <Box>
            <Text as='h2' textAlign='center' fontWeight='bold' fontSize='24px'>
               {user_exists === 'Y'
                  ? setPasswordStrings.confirmAccount
                  : setPasswordStrings.setPassword}
            </Text>
            {user_exists === 'Y' ? (
               <>
                  <Text
                     mt={8}
                     fontSize='16px'
                     textAlign='center'
                  >{`Are you certain you want to confirm your role as a ${urlParams.get('user_role')} for ${urlParams.get('company_url')}? By proceeding, you acknowledge and accept all responsibilities and permissions associated with this role.`}</Text>
                  <Button
                     mt={8}
                     width='full'
                     colorScheme='blue'
                     size='sm'
                     onClick={handleCreateUser}
                  >
                     {setPasswordStrings.confirm}
                  </Button>
               </>
            ) : (
               <>
                  <FormControl>
                     <FormLabel ml={1} fontSize='sm'>
                        Email Address *
                     </FormLabel>
                     <Input
                        type='email'
                        name='email_address'
                        value={urlParams.get('email_address') as string}
                        variant='outline'
                        size='md'
                        disabled
                     />
                  </FormControl>
                  <FormControl
                     mt={4}
                     id='password'
                     isInvalid={!!error.password}
                  >
                     <FormLabel ml={1} fontSize='sm'>
                        Password *
                     </FormLabel>
                     <InputGroup size='lg'>
                        <Input
                           type={showPassword.password ? 'text' : 'password'}
                           name='password'
                           value={form.password}
                           onChange={handleChange}
                           placeholder='Enter your password'
                           variant='outline'
                           size='md'
                        />
                        <InputRightElement>
                           <Button
                              h='1.75rem'
                              size='sm'
                              onClick={() =>
                                 setShowPassword((prev) => ({
                                    ...prev,
                                    password: !prev.password,
                                 }))
                              }
                              variant='ghost'
                           >
                              {showPassword.password ? (
                                 <FaEyeSlash />
                              ) : (
                                 <FaEye />
                              )}
                           </Button>
                        </InputRightElement>
                     </InputGroup>
                     {error.password && (
                        <FormErrorMessage fontSize='xs'>
                           {error.password}
                        </FormErrorMessage>
                     )}
                  </FormControl>
                  <FormControl
                     mt={4}
                     id='confirm_password'
                     isInvalid={!!error.confirm_password}
                  >
                     <FormLabel ml={1} fontSize='sm'>
                        Confirm Password *
                     </FormLabel>
                     <InputGroup size='lg'>
                        <Input
                           type={
                              showPassword.confirm_password
                                 ? 'text'
                                 : 'password'
                           }
                           name='confirm_password'
                           value={form.confirm_password}
                           onChange={handleChange}
                           placeholder='Confirm your password'
                           variant='outline'
                           size='md'
                        />
                        <InputRightElement>
                           <Button
                              h='1.75rem'
                              size='sm'
                              onClick={() =>
                                 setShowPassword((prev) => ({
                                    ...prev,
                                    confirm_password: !prev.confirm_password,
                                 }))
                              }
                              variant='ghost'
                           >
                              {showPassword.confirm_password ? (
                                 <FaEyeSlash />
                              ) : (
                                 <FaEye />
                              )}
                           </Button>
                        </InputRightElement>
                     </InputGroup>
                     {error.confirm_password && (
                        <FormErrorMessage fontSize='xs'>
                           {error.confirm_password}
                        </FormErrorMessage>
                     )}
                  </FormControl>
                  <Button
                     mt={3}
                     width='full'
                     colorScheme='blue'
                     size='sm'
                     onClick={handleCreateUser}
                     isDisabled={!!error.password || !!error.confirm_password}
                  >
                     {setPasswordStrings.confirm}
                  </Button>
               </>
            )}
         </Box>
      </>
   );
}

export default SetPassword;
