import {
   AlertDialog,
   AlertDialogContent,
   AlertDialogHeader,
   AlertDialogOverlay,
   AlertDialogBody,
   AlertDialogFooter,
   Button,
} from '@chakra-ui/react';
import { useRef } from 'react';

interface DeleteConfirmationProps {
   isOpen: boolean;
   onClose: () => void;
   onConfirm: () => void;
}

const DeleteConfirmation = (props: DeleteConfirmationProps) => {
   const { isOpen, onClose, onConfirm } = props;

   const cancelRef = useRef(null);

   return (
      <AlertDialog
         isOpen={isOpen}
         leastDestructiveRef={cancelRef}
         onClose={() => onClose()}
      >
         <AlertDialogOverlay>
            <AlertDialogContent>
               <AlertDialogHeader fontSize='lg' fontWeight='bold'>
                  Delete Alert
               </AlertDialogHeader>

               <AlertDialogBody>
                  Are you sure you want to delete this alert? This action cannot
                  be undone.
               </AlertDialogBody>

               <AlertDialogFooter>
                  <Button ref={cancelRef} onClick={onClose}>
                     Cancel
                  </Button>
                  <Button colorScheme='red' onClick={onConfirm} ml={3}>
                     Delete
                  </Button>
               </AlertDialogFooter>
            </AlertDialogContent>
         </AlertDialogOverlay>
      </AlertDialog>
   );
};

export default DeleteConfirmation;
