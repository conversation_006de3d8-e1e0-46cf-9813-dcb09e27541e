/* eslint-disable @typescript-eslint/no-explicit-any */
import { PayloadAction, createSlice } from '@reduxjs/toolkit';

interface InitialState {
   show: boolean;
   payload: { modalType: string; modalProps?: Record<string, any> } | null;
}

const initialState: InitialState = {
   show: false,
   payload: null,
};

const modalSlice = createSlice({
   name: 'modal',
   initialState,
   reducers: {
      openModal: (
         state,
         action: PayloadAction<{
            modalType: string;
            modalProps?: Record<string, any>;
         }>,
      ) => {
         const { modalType, modalProps } = action.payload;
         state.show = true;
         state.payload = { modalType, modalProps };
      },
      closeModal: (state) => {
         state.show = false;
         state.payload = null;
      },
   },
});

export const { openModal, closeModal } = modalSlice.actions;
export default modalSlice.reducer;
