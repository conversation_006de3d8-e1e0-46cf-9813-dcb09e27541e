/* eslint-disable @typescript-eslint/no-explicit-any */
import { PayloadAction, createSlice } from '@reduxjs/toolkit';

interface InitialState {
   show: boolean;
   payload: { popoverType: string; popoverProps?: Record<string, any> } | null;
}

const initialState: InitialState = {
   show: false,
   payload: null,
};

const popoverSlice = createSlice({
   name: 'popover',
   initialState,
   reducers: {
      openPopover: (
         state,
         action: PayloadAction<{
            popoverType: string;
            popoverProps?: Record<string, any>;
         }>,
      ) => {
         const { popoverType, popoverProps } = action.payload;
         state.show = true;
         state.payload = { popoverType, popoverProps };
      },
      closePopover: (state) => {
         state.show = false;
         state.payload = null;
      },
   },
});

export const { openPopover, closePopover } = popoverSlice.actions;
export default popoverSlice.reducer;
