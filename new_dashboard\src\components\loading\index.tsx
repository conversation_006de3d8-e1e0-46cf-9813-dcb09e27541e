import React from 'react';
import { Flex, Spinner } from '@chakra-ui/react';

interface LoadingProps {
   full?: boolean;
   background?: boolean;
}

const Loading: React.FC<LoadingProps> = ({ full, background }) => {
   return (
      <Flex
         justifyContent='center'
         alignItems='center'
         position={full ? 'fixed' : 'absolute'}
         top={0}
         left={0}
         right={0}
         bottom={0}
         height={full ? '100vh' : 'auto'}
         width={full ? '100vw' : 'auto'}
         backgroundColor={background ? 'rgba(0, 0, 0, 0.5)' : ''}
         zIndex={full ? 9999 : 'auto'}
      >
         <Spinner size='xl' />
      </Flex>
   );
};

export default Loading;
