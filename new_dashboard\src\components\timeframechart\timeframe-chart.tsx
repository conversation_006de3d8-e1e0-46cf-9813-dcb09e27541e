import React, { useEffect, useState } from 'react';
import { Select } from '@chakra-ui/react';
import './timeframe-chart.scss';
import { PerformanceChartData } from '../chatbox/interface';

interface TimeFrameMenuProps {
   id: string;
   handleTimeFrameSelect: (id: string, timeFrame: string) => void;
   defaultValue: PerformanceChartData;
   showMenu: boolean;
}

const TimeFrameChartMenu: React.FC<TimeFrameMenuProps> = ({
   id,
   handleTimeFrameSelect,
   defaultValue,
   showMenu,
}) => {
   if (!showMenu) return null;
   const [selectedTimeFrame, setSelectedTimeFrame] = useState<string>(
      defaultValue.dateFrequency || 'Select Frequency',
   );

   useEffect(() => {
      if (defaultValue.dateFrequency) {
         setSelectedTimeFrame(defaultValue.dateFrequency);
      }
   }, [defaultValue]);

   const handleTimeFrameChange = (value: string) => {
      setSelectedTimeFrame(value);
      handleTimeFrameSelect(id, value);
   };

   return (
      <div>
         <Select
            value={selectedTimeFrame}
            onChange={(e) => handleTimeFrameChange(e.target.value)}
            width={{ base: '80px', sm: '90px', md: '95px', lg: '105px' }}
            height={{ base: '20px', sm: '20px', md: '25px', lg: '30px' }}
            fontWeight='normal'
            fontSize={{ base: 'xs', md: 'xs', lg: 'sm' }}
            borderColor='#5579f2'
            color='#5579f2'
         >
            <option value='hourly'>Hourly</option>
            <option value='daily'>Daily</option>
            <option value='weekly'>Weekly</option>
            <option value='monthly'>Monthly</option>
         </Select>
      </div>
   );
};

export default TimeFrameChartMenu;
