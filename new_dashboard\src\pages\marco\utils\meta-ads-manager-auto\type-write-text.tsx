import React, { useEffect, useState, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { replacePlaceholders } from './icons-helper';
interface TypewriterTextProps {
   text: string;
   speed?: number;
   onComplete?: () => void;
   onType?: () => void;
   messageId?: string;
   disableAnimation?: boolean;
}

// Utility function to clear animation cache
export const clearAnimationCache = () => {
   try {
      const keys = Object.keys(localStorage);
      keys.forEach((key) => {
         if (key.startsWith('typewriter_') || key.startsWith('summary_')) {
            localStorage.removeItem(key);
         }
      });
   } catch (error) {
      console.warn('Failed to clear animation cache:', error);
   }
};

// Helper function to safely access localStorage
const getFromStorage = (key: string): string | null => {
   try {
      return localStorage.getItem(key);
   } catch (error) {
      console.warn('Failed to access localStorage:', error);
      return null;
   }
};

const setToStorage = (key: string, value: string): void => {
   try {
      localStorage.setItem(key, value);
   } catch (error) {
      console.warn('Failed to set localStorage:', error);
   }
};

const TypewriterText: React.FC<TypewriterTextProps> = ({
   text,
   speed = 20,
   onComplete,
   onType,
   messageId,
   disableAnimation,
}) => {
   const [displayText, setDisplayText] = useState('');
   const [currentIndex, setCurrentIndex] = useState(0);
   const [hasAnimated, setHasAnimated] = useState(false);
   const lastText = useRef<string>('');
   const animationKey = messageId
      ? `typewriter_${messageId}`
      : `typewriter_${text.substring(0, 50)}`;

   useEffect(() => {
      if (disableAnimation && text) {
         setDisplayText(text);
         setCurrentIndex(text.length);
         setHasAnimated(true);

         setToStorage(animationKey, 'true');
         if (onComplete) onComplete();
         return;
      }

      const isAlreadyAnimated = getFromStorage(animationKey) === 'true';

      if (isAlreadyAnimated) {
         setDisplayText(text);
         setCurrentIndex(text.length);
         setHasAnimated(true);
         if (onComplete) onComplete();
         return;
      }

      if (text !== lastText.current) {
         setDisplayText('');
         setCurrentIndex(0);
         setHasAnimated(false);
         lastText.current = text;
      }
      // Only animate if not already animated for this text
      if (text && !hasAnimated && currentIndex < text.length) {
         const timer = setTimeout(() => {
            setDisplayText((prev) => prev + text[currentIndex]);
            setCurrentIndex((prev) => prev + 1);

            if (onType && currentIndex % 3 === 0) {
               onType();
            }
         }, speed);
         return () => clearTimeout(timer);
      } else if (text && !hasAnimated && currentIndex >= text.length) {
         setHasAnimated(true);

         setToStorage(animationKey, 'true');
         if (onComplete) onComplete();
      }
   }, [
      text,
      speed,
      currentIndex,
      onComplete,
      onType,
      hasAnimated,
      animationKey,
      disableAnimation,
   ]);

   const renderWithIcons = (children: React.ReactNode): React.ReactNode => {
      if (typeof children === 'string') {
         // Replace all tokens like [:checkbox:] with icons
         return replacePlaceholders(children);
      }
      if (Array.isArray(children)) {
         // Use a typed helper to avoid 'any' lint error for child
         return (children as React.ReactNode[]).map((child, idx) => (
            <React.Fragment key={idx}>{renderWithIcons(child)}</React.Fragment>
         ));
      }
      return children;
   };

   return (
      <ReactMarkdown
         remarkPlugins={[remarkGfm]}
         components={{
            p: ({ children, ...props }) => (
               <p
                  className='mb-3 text-gray-700 whitespace-pre-wrap leading-relaxed'
                  {...props}
               >
                  {renderWithIcons(children)}
               </p>
            ),
            li: ({ children, ...props }) => (
               <li className='list-disc ml-6 text-gray-700 mb-1' {...props}>
                  {renderWithIcons(children)}
               </li>
            ),
            strong: ({ children, ...props }) => (
               <strong className='font-bold text-gray-800' {...props}>
                  {renderWithIcons(children)}
               </strong>
            ),
         }}
      >
         {hasAnimated ? text : displayText}
      </ReactMarkdown>
   );
};

export default TypewriterText;
