import { AxiosResponse } from 'axios';
import { KPICategory } from '../../../utils/strings/kpi-constants';
import { PlacementWithLogical } from '@chakra-ui/react';

export interface EndPoints {
   getKpiData: (payload: KPIPayload) => Promise<AxiosResponse<FinalAgg>>;
   getKpiSummary: (
      payload: KPISummaryPayload,
   ) => Promise<AxiosResponse<string>>;
   getKpiMeta: (clientId: string) => Promise<AxiosResponse<KPIMeta[]>>;
   updatePinned: (payload: PinPayload) => Promise<AxiosResponse<string>>;
   updateVisible: (payload: VisblePayload) => Promise<AxiosResponse<string>>;
   updatePinVisibleOrder: (
      payload: PinVisibleOrderPayload,
   ) => Promise<AxiosResponse<string>>;
   getAnomalyCause: (
      payload: KPIAnomalyCausePayload,
   ) => Promise<AxiosResponse<AnomalyCause>>;
}
export interface KPIAgg {
   [key: string]: KPIDetails;
}
export interface PinPayload {
   clientId: string;
   category: string;
   kpis: string[];
   pinned: boolean;
}
export interface BreakDown {
   [key: string]: {
      value: number;
      kpi_display_name: string;
      unit: string;
      category: string;
      up: boolean;
      sub_items: BreakDown | null;
      kpi_type: string;
   };
}
export interface MetricKPI {
   category: string;
   kpi: string;
   kpi_display_name: string;
}
export interface MetricReport {
   clientId: string;
   user_id?: string;
   title: string;
   time: string;
   frequency: string;
   interval: string;
   start_date: string;
   end_date: string;
   date: string;
   emails: string;
   kpis: MetricKPI[] | string;
   username: string;
   reportId: string;
   report_id?: string;
   client_id?: string;
   created_at?: string;
   is_auto_report?: boolean;
   is_subscribed?: boolean;
}
export interface GetReportPayload {
   clientId: string;
   userId: string;
}
export interface DeleteReportPayload {
   clientId: string;
   reportId: string;
}
export interface PauseReportPayload {
   clientId: string;
   isSubscribed: boolean;
   reportId: string;
}
export interface UpdateReportPayload {
   clientId: string;
   user_id: string;
   reportId: string;
   title: string;
   time: string;
   frequency: string;
   interval: string;
   start_date: string;
   end_date: string;
   date: string;
   emails: string;
   username: string;
   kpis: MetricKPI[] | string;
}

export interface Edorment {
   [key: string]: string;
}
export interface ValueProps {
   kpi: KPIData;
   totalVal: number;
   fontSize?: string;
   rightAlign?: boolean;
}
export interface VisblePayload {
   clientId: string;
   category: string;
   kpis: string[];
   visible: boolean;
}
export interface PinVisibleOrderPayload {
   clientId: string;
   kpiOrder: {
      kpi: string;
      order: number;
   }[];
   pinOrder: boolean;
}

export interface KPIAnomalyCausePayload {
   client_id: string;
   start_date: string;
   end_date: string;
   kpi: string;
}

export interface AnomalyCause {
   [key: string]: string;
}
export interface KPIMeta {
   category: string;
   kpi: string;
   visible: boolean;
   pinned: boolean;
   kpi_display_name: string;
   pinned_order: number;
   visible_order: number;
}
export interface CategoryAgg {
   [key: string]: KPIData[];
}
export interface MetaCatAgg {
   [key: string]: KPIMeta[];
}

export interface FinalAgg {
   [key: string]: KPIAgg;
}

export interface PinnedKPIProps {
   metaData: KPIMeta[];
   aggCategoryData: FinalAgg;
   comparedAgg: FinalAgg;
   pinnedId: string;
   loading: Loading;
}

export interface TooltipContentProps {
   children: React.ReactNode;
   category?: string;
   kpi?: string;
   label?: string;
   hasArrow?: boolean;
   placement?: PlacementWithLogical;
}
export interface KPIPayload {
   clientId: string;
   startDate: string;
   endDate: string;
   prevStartDate: string;
   prevEndDate: string;
   compareBy: string;
}
export interface KPISummaryPayload {
   data: GPTData[];
   kpi: string;
   category: string;
   currency: string;
}
export interface GPTData {
   xaxis: string;
   yaxis: number;
}

export interface Ranges {
   label: string;
   range: () => TimeRange;
}

export interface KPIRange {
   end: Date;
   start: Date;
}
export interface KPIStoreRange {
   end: string;
   start: string;
}

export interface TimeRange {
   endDate: Date;
   startDate: Date;
}
export interface DateRange {
   startDate: Date;
   endDate: Date;
   key: string;
}

export interface RangeState {
   show: boolean;
   dateRange: DateRange[];
}

export interface RangeEventItem {
   [key: string]: DateRange;
}
export interface StaticRange {
   label: string;
   range: () => TimeRange;
   isSelected: (range: TimeRange) => boolean;
}

export interface Loading {
   first: boolean;
   last: boolean;
}
export interface CategoryProps {
   head: string;
   loading: Loading;
   data: KPIAgg;
   prevData: KPIAgg;
   metaData: KPIMeta[];
   id: string;
   anomaly?: anomalies;
}
export interface KPIWrapperProps {
   loading: Loading;
   data: KPIAgg;
   prevData: KPIAgg;
   head: string;
   anomaly: anomalies;
}
export type KpiCategoryKeys = keyof typeof KPICategory;
export interface ChartProp {
   kpiDetails: KPIDetails;
   value?: boolean;
}
export interface MultiLineChartProp {
   kpiDetails: KPIDetails;
   prevDetails: KPIDetails;
   startLabel: string;
}
export interface OptionsMore {
   seriesIndex: number;
   dataPointIndex: number;
   w: {
      globals: {
         series: string[];
      };
   };
}
export interface CardProp {
   kpiDetails: KPIDetails;
   prevKpi: KPIDetails;
   loading: Loading;
   head: string;
   anomaly?: boolean;
}
export interface DateRangeSelectProp {
   setcompareBy: (compareBy: string) => void;
}

export interface ViewDetailsProp {
   kpiDetails: KPIDetails;
   prevKpi: KPIDetails;
   isAnomaly?: boolean;
}
export interface SummaryProp {
   summary: string[];
   isFetching: boolean;
   error: string | null;
}
export interface KPIDetails {
   allData: KPIData[];
   totalVal: number;
   category: string;
   unit: string;
   displayName: string;
   kpi_names: string;
   pinned?: boolean;
   visible?: boolean;
   order?: number;
   daily_breakdown?: BreakDown | null;
   percentage_change?: number | null;
   standard_deviation?: number | null;
}

export interface KPIData {
   date: string;
   category: string;
   kpi_unit: string;
   client_id: string;
   kpi_names: string;
   kpi_value: number;
   kpi_type: string;
   kpi_display_name: string;
   last_updated_date: string;
   load_date_and_time: string;
}
export interface anomalies {
   has_data: boolean;
   anomalies?: {
      [key: string]: boolean;
   };
   message?: string;
}
export interface anomalyResult {
   [key: string]: anomalies;
}
export interface anomalyData {
   status: string;
   message: string;
   result: anomalyResult;
}
