import image from '../images/integrations/googleAds.jpg';
import FivetranConnectorWrapper from './fivetran-connector-wrapper';
import darkImage from '../../../assets/icons/kpi/gads.png';

import { connectToGoogleADS } from '../utils';
import { useColorMode } from '@chakra-ui/react';

const GoogleAds = () => {
   const { colorMode } = useColorMode();

   return (
      <FivetranConnectorWrapper
         imageSrc={colorMode === 'dark' ? darkImage : image}
         heading='Google Ads'
         channelType='GOOGLE_ADS'
         connectToSentimentFn={connectToGoogleADS}
         modalData={{
            heading: 'Connect Google Ads',
            content:
               'You are being redirected to Google Ads to connect your account...',
         }}
      />
   );
};

export default GoogleAds;
