import DefaultCard from '@/components/DefaultCard';
// import { RootState } from '@/store/store';
// import { useSelector } from 'react-redux';
import PaymentsTable from './PaymentTable';
import TopupTable from './TopupTable';
import SubscriptionTable from './SubscriptionTable';
import InvoiceTable from './InvoiceTable';

const Billing = () => {
   // const { isActive } = useSelector((state: RootState) => state.subscription);
   // const subscribed = isActive !== null;

   return (
      <section className='bg-white p-6 rounded-md space-y-6 overflow-auto max-h-[72vh] w-full'>
         <h2 className='head5 text-jet'>Billings</h2>
         <hr className='h-[1px] w-full bg-fog' />
         <p className='para4 text-charcoal'>
            Get your all Billing records here.
         </p>
         {
            <div className='billing flex flex-col space-y-6'>
               <PaymentsTable />
               <TopupTable />
               <SubscriptionTable />
               <InvoiceTable />
            </div>
         }
         <DefaultCard
            banner='inactivePlan'
            title={
               // subscribed ? 'Still Have Questions?' : 'No Active Subscription'
               'Still Have Questions?'
            }
            desc={
               // subscribed
               // ? 'Our dedicated team is ready to assist you. Reach out for more information or explore our resources.'
               // : 'Choose a plan that fits your needs and unlock full access to our platform.'
               'Our dedicated team is ready to assist you. Reach out for more information or explore our resources.'
            }
            actionLabel={
               // subscribed ? 'Contact Us' : 'Subscribe to a plan'
               'Contact Us'
            }
            navigate='/settings?mode=plansTopups&tab=plans'
         />
      </section>
   );
};

export default Billing;
