import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
   ImageTextData,
   PersonalisedData,
} from '../../pages/socialwatch/interface';

interface ConfigState {
   clickedPillId: string;
   personalisedData: PersonalisedData | ImageTextData | null;
}

const initialState: ConfigState = {
   clickedPillId: '',
   personalisedData: null,
};

const contentIdeationSlice = createSlice({
   name: 'contentIdeation',
   initialState,
   reducers: {
      setClickedPill: (state: ConfigState, action: PayloadAction<string>) => {
         state.clickedPillId = action.payload;
      },
      setPersonalisedData: (
         state: ConfigState,
         action: PayloadAction<PersonalisedData | ImageTextData>,
      ) => {
         state.personalisedData = action.payload;
      },
   },
});

export const { setClickedPill, setPersonalisedData } =
   contentIdeationSlice.actions;

export default contentIdeationSlice.reducer;
