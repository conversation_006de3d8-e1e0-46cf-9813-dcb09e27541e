export const toastMessage = {
   success: {
      status: 'Success',
      description: 'Operation completed successfully.',
      title: 'Data has been saved successfully',
   },
   error: {
      title: 'Error!',
      description: 'Operation failed.',
      status: 'error',
   },
   warning: {
      title: 'Warning',
      description: 'Please select a date and time',
   },
   info: {
      title: 'Info',
      description: 'Please select a date and time',
   },
   tweet: {
      title: 'Data has been posted successfully',
      description: 'Operation completed successfully.',
      status: 'success',
   },
   copilotMessageSuccess: {
      title: 'Content generated successfully.',
      description: 'You have successfully generated content.',
      status: 'success',
   },
   copilotMessageError: {
      title: 'Content generation failed.',
      description: 'An error occurred while generating content.',
      status: 'error',
   },
   dateMessage: {
      title: 'Invalid Date',
      description: 'Please select a future date and time',
      status: 'error',
   },
};

export const buttonMessage = {
   save: 'Save',
   post: 'Post Now',
   regenerate: 'Regenerate',
   cancel: 'Cancel',
   Calendar: 'Save to Calendar',
   delete: 'Delete',
   edit: 'Edit',
   close: 'Close',
   submit: 'Submit',
   generate: 'Generate Content',
   plainGenerate: 'Generate',
   aiButton: 'AI Assist',
};

export const dialogMessage = {
   delete: {
      title: 'Are you sure?',
      description: "You won't be able to revert this!",
      status: 'warning',
      buttonMessage: 'Yes, delete it!',
   },
   edit: {
      title: 'Edit',
      description: 'Are you sure you want to edit this item?',
      status: 'info',
   },
   deleteSuccess: {
      title: 'Deleted!',
      description: 'Your file has been deleted.',
      status: 'success',
   },
   noGaAccount: {
      title: 'No GA accounts found',
      description: 'Please connect to GA from the Google Analytics website',
      status: 'warning',
   },
   accountSuccess: {
      title: 'Success',
      description: 'Google Analytics account connected',
      status: 'success',
   },
};

export const sessionKey = {
   aiButton: 'aibutton',
   aiSuggestion: 'aiSuggestion',
   // aiContent: 'aicontent',
};

export const sessionValue = {
   aiButton: 'AI Assist',
   regenerate: 'Regenerate',
};

export const socialMediaAuthInfo = {
   title: ' Please login at least one social media account to generate content',
};

export const contentCreationCardWriteHere = {
   title: 'Write Your Content',
   description: 'Something in mind? Write it. Schedule it.',
};

export const contentCreationCardContentGeneration = {
   title: 'Content Generation',
   description: 'Got a caption idea? Let AI craft and schedule it for you.',
};

export const contentCreationCardContentIndentation = {
   title: 'Content Ideation',
   description: 'Get content idea from emerging trends and competitor posts.',
};

export const toneOptions = [
   { value: 'personal', label: 'Personal' },
   { value: 'witty', label: 'Witty' },
   { value: 'professional', label: 'Professional' },
   { value: 'empathetic', label: 'Empathetic' },
   { value: 'casual', label: 'Casual' },
];

export const wordSizeOptions = [
   { value: 'Short', label: 'Short' },
   { value: 'Medium', label: 'Medium' },
   { value: 'Long', label: 'Long' },
];
