import { useEffect, useRef, useState } from 'react';
import {
   DropdownMenu,
   DropdownMenuTrigger,
   DropdownMenuContent,
   DropdownMenuItem,
   DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { cn } from '@/utils';
import { PiCheckBold } from 'react-icons/pi';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

type DropdownWithCustomInputProps = {
   label?: string;
   options: { value: string; label: string }[];
   selected?: { value: string; label: string };
   placeholderInput?: string;
   onSelect: (value: { value: string; label: string }) => void;
   placeholder?: string;
   triggerLabel?: string;
};

const SelectPeriodCombobox = ({
   options,
   selected,
   onSelect,
   placeholderInput = 'Select',
   placeholder = 'Enter Custom Days',
}: DropdownWithCustomInputProps) => {
   const [days, setDays] = useState('');
   const [inputWidth, setInputWidth] = useState<number | undefined>(undefined);
   const [customOptions, setCustomOptions] = useState<
      { value: string; label: string }[]
   >([]);

   const inputWrapperRef = useRef<HTMLDivElement>(null);

   const handleSelect = (value: { value: string; label: string }) => {
      onSelect(value);
   };

   const handleCustomDaysChange = () => {
      if (days.trim() === '') return;

      if (Number(days.trim()) <= 0 || Number(days.trim()) > 180) {
         toast.error('Please enter a valid number of days between 1 and 180.');
         return;
      }

      if (
         days &&
         [...customOptions, ...options].some((opt) => opt.value === days)
      ) {
         onSelect(
            [...customOptions, ...options].find(
               (opt) => opt.value === days,
            ) || { value: '', label: '' },
         );
         setDays('');
         return;
      }

      const newOption = {
         value: days,
         label: `Last ${days} Days`,
      };
      setCustomOptions((prev) => [...prev, newOption]);
      setDays('');
      onSelect(newOption);
   };

   useEffect(() => {
      const updateWidth = () => {
         if (inputWrapperRef.current) {
            setInputWidth(inputWrapperRef.current.offsetWidth);
         }
      };
      updateWidth();

      const observer = new ResizeObserver(updateWidth);
      if (inputWrapperRef.current) observer.observe(inputWrapperRef.current);

      return () => {
         if (inputWrapperRef.current)
            observer.unobserve(inputWrapperRef.current);
      };
   }, [inputWrapperRef.current?.offsetWidth]);

   return (
      <DropdownMenu>
         <DropdownMenuTrigger className='focus-visible:ring-0 focus-visible:outline-none'>
            <div ref={inputWrapperRef} className='w-full'>
               <Input
                  value={selected?.label || ''}
                  placeholder={placeholderInput}
                  className='w-full focus-visible:ring-0 focus-visible:outline-none !cursor-pointer'
               />
            </div>
         </DropdownMenuTrigger>
         <DropdownMenuContent
            style={{ width: inputWidth ? `${inputWidth}px` : undefined }}
            className='w-full p-1 bg-white'
         >
            <div className='px-2 flex items-center gap-2 my-1'>
               <span>Last</span>
               <Input
                  value={days}
                  type='number'
                  min='1'
                  max='180'
                  onChange={(e) => setDays(e.target.value)}
                  placeholder={placeholder}
                  className='w-full !rounded-[6px] focus-visible:ring-0 focus-visible:outline-none'
               />
               <span>Days</span>
               <Button
                  className='!rounded-[6px]'
                  size='sm'
                  onClick={handleCustomDaysChange}
               >
                  Add
               </Button>
            </div>
            <DropdownMenuSeparator className='border-b my-2' />
            {[...customOptions, ...options]
               .sort((a, b) => Number(a.value) - Number(b.value))
               .map((opt, index) => (
                  <DropdownMenuItem
                     key={index}
                     onSelect={() => handleSelect(opt)}
                     className={cn(
                        'w-full cursor-pointer flex items-center justify-between',
                        { 'font-bold': selected?.value === opt.value },
                     )}
                  >
                     {selected?.value === opt.value ? (
                        <>
                           <div className='w-full flex justify-between items-center gap-2'>
                              <span>{opt.label}</span>
                              <span>
                                 <PiCheckBold />
                              </span>
                           </div>
                        </>
                     ) : (
                        opt.label
                     )}
                  </DropdownMenuItem>
               ))}
         </DropdownMenuContent>
      </DropdownMenu>
   );
};

export default SelectPeriodCombobox;
