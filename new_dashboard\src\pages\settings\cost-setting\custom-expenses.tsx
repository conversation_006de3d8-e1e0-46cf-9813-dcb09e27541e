import { Button, Flex, Heading } from '@chakra-ui/react';
import { CUSTOM_EXPENSES_STRING } from '../../../utils/strings/cfo';
import FixedExpenses from './fixed-expense';
import VariableExpenses from './variable-expense';
import { useDispatch } from 'react-redux';
import { openModal } from '../../../store/reducer/modal-reducer';
import { modalTypes } from '../../../components/modals/modal-types';

function Customexpenses() {
   const dispatch = useDispatch();
   const handleAddExpense = (fixed: boolean) => {
      if (fixed) {
         dispatch(
            openModal({
               modalType: modalTypes.FIXED_EXPENSE_MODAL,
            }),
         );
      } else {
         dispatch(
            openModal({
               modalType: modalTypes.VARIABLE_EXPENSE_MODAL,
            }),
         );
      }
   };
   return (
      <Flex direction={'column'} className='custom-expenses'>
         <Flex justifyContent={'space-between'} alignItems={'center'}>
            <Heading fontSize={'16px'} className='panel'>
               {CUSTOM_EXPENSES_STRING.title}
            </Heading>
            <Flex gap={3} className='btns'>
               <Button onClick={() => handleAddExpense(true)}>
                  Add Fixed Expense
               </Button>
               <Button onClick={() => handleAddExpense(false)}>
                  Add Variable Expense
               </Button>
               <Button
                  disabled
                  cursor={'not-allowed'}
                  color={'gray !important'}
               >
                  Export to CSV
               </Button>
            </Flex>
         </Flex>
         <FixedExpenses />
         <VariableExpenses />
      </Flex>
   );
}

export default Customexpenses;
