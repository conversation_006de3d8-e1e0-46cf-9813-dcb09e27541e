export const CurrentStepIndicator = () => {
   return (
      <svg
         xmlns='http://www.w3.org/2000/svg'
         version='1.1'
         width={30}
         height={30}
      >
         <circle
            cx='15'
            cy='15'
            r='13'
            stroke='white'
            strokeWidth='2'
            fill='none'
         />
         <circle
            cx='15'
            cy='15'
            r='7'
            stroke='white'
            strokeWidth='2'
            fill='white'
         />
      </svg>
   );
};

export const IncompleteStepIndicator = () => {
   return (
      <svg
         xmlns='http://www.w3.org/2000/svg'
         version='1.1'
         width={30}
         height={30}
      >
         <circle
            cx='15'
            cy='15'
            r='13'
            stroke='white'
            strokeWidth='2'
            fill='none'
         />
      </svg>
   );
};

export const CompleteStepIndicator = () => {
   return (
      <svg
         xmlns='http://www.w3.org/2000/svg'
         version='1.1'
         width={30}
         height={30}
      >
         <circle
            cx='15'
            cy='15'
            r='13'
            stroke='#00FF66'
            strokeWidth='2'
            fill='none'
         />

         <path
            d='M8 15 L12 20 L22 10'
            stroke='#00FF66'
            strokeWidth='2'
            fill='none'
            strokeLinecap='round'
            strokeLinejoin='round'
         />
      </svg>
   );
};
