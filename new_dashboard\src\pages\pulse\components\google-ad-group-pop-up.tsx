import React, { useState, useEffect, useRef } from 'react';
import './popup.scss';
import pulseService, {
   Campaign,
   GoogleAdsData,
   GoogleAdgroupsData,
   SummaryResponse,
   GoogleAdsKeywordsData,
   GoogleAdsSearchTermData,
   GoogleAdsKeywordTermData,
   KeywordKPIValues,
} from '../../../api/service/pulse';
import { useAppSelector } from '../../../store/store';
import {
   cap,
   toUpperCase,
   extractLinesFromRecommendation,
   toShowCurrency,
   truncateText,
   getDateRange,
   formatValue,
   REQUIRED_KPIS_GOOGLE_ADS,
} from '../utils/helper';
import { LocalStorageService, Keys } from '../../../utils/local-storage';
import GoogleAdgroupsPerformenceMultiChart from './google-ad-group-performace-chart';
import {
   Flex,
   HStack,
   IconButton,
   InputGroup,
   InputRightElement,
   <PERSON>u,
   <PERSON>u<PERSON>utton,
   <PERSON>u<PERSON><PERSON>,
   <PERSON>uList,
   Skeleton<PERSON>ext,
   Tag,
   TagCloseButton,
   TagLabel,
   VStack,
} from '@chakra-ui/react';
import { UserDetails } from './interface';
import { useApiQuery } from '../../../hooks/react-query-hooks';
import { calculateHelper } from '../../utils/kpiCalculaterHelper';
import { LuArrowDownUp, LuArrowUp, LuArrowDown } from 'react-icons/lu';
import { FaChevronDown, FaChevronRight } from 'react-icons/fa';
import { IoChevronDown, IoCloseSharp } from 'react-icons/io5';

interface NestedPopupProps {
   data: Campaign;
   isOpen: boolean;
   onBack: () => void;
   onClose: () => void;
   details: number;
   adgroup: GoogleAdgroupsData;
}
const GoogleAdgroupPopup: React.FC<NestedPopupProps> = ({
   data,
   isOpen,
   onBack,
   onClose,
   adgroup,
}) => {
   const { channel, objective, metric, metricsOptions, adgroup_ChartType } =
      useAppSelector((state) => state.dropdown);
   const [selectedMetric, setmetric] = useState(metric);
   const [adData, setAdData] = useState<GoogleAdsData[]>([]);
   const [keywordSummary, setKeywordSummary] = useState<SummaryResponse | null>(
      null,
   );
   const [graphSummary, setGraphSummary] = useState<SummaryResponse | null>(
      null,
   );
   const [adsSummary, setAdsSummary] = useState<SummaryResponse | null>(null);
   const [isKeywordSummaryLoading, setisKeywordSummaryLoading] =
      useState<boolean>(true);
   const [isGraphSummaryLoading, setisGraphSummaryLoading] =
      useState<boolean>(true);
   const [isAdSummaryLoading, setisAdSummaryLoading] = useState<boolean>(true);
   const [filteredKeywordData, setFilteredKeywordData] = useState<
      GoogleAdsKeywordsData[]
   >([]);
   const [sortOrder, setSortOrder] = useState('desc');
   const [selectedKpi, setSelectedKpi] = useState<string>('conversions');
   const allOptions = ['BROAD', 'EXACT', 'PHRASE'];
   const [selectedOptions, setSelectedOptions] = useState<string[]>(allOptions);

   const userDetails =
      LocalStorageService.getItem<UserDetails>(Keys.FlableUserDetails) ||
      ({} as UserDetails);
   const [keywordExpandedState, setkeywordExpandedState] = useState<
      Record<string | number, boolean>
   >({});
   const { dateRange, prevRange } = useAppSelector((state) => state.kpi);

   const { start_date, end_date, prev_end_date, prev_start_date, days } =
      getDateRange(dateRange, prevRange);

   const GoogleKpisNames = metricsOptions?.map((option) => option.value);
   const modelstate = useRef(false);

   const keywordPayload = {
      client_id: userDetails?.client_id,
      campaign_id: data?.campaign_id,
      ad_group_id: adgroup?.ad_group_id,
      start_date: start_date,
      end_date: end_date,
   };

   const {
      data: adgroupKeywordData,
      isLoading: adgroupKeywordDataLoading,
      isSuccess: adgroupKeywordDataSuccess,
   } = useApiQuery({
      queryKey: [String(isOpen), `${adgroup?.ad_group_id}`, `${days}`],
      queryFn: () => pulseService.fetchGoogleAdskeywords(keywordPayload),
      enabled: !!data?.campaign_id && !!adgroup?.ad_group_id,
      refetchOnWindowFocus: false,
   });

   useEffect(() => {
      const fetchAdData = async () => {
         if (isOpen && adgroup && adgroup.ad_group_id) {
            const payload = {
               client_id: userDetails?.client_id,
               campaign_id: data?.campaign_id,
               ad_group_id: adgroup?.ad_group_id,
               channel_type: objective,
               kpis: [...GoogleKpisNames, ...REQUIRED_KPIS_GOOGLE_ADS],
               start_date: start_date,
               end_date: end_date,
               prev_start_date: prev_start_date,
               prev_end_date: prev_end_date,
            };

            const response = await pulseService.fetchGoogelAds(payload);
            if (response.data?.[0]?.fn_googleads_ads_with_kpi_get) {
               setAdData(response.data?.[0]?.fn_googleads_ads_with_kpi_get);
            }
         }
      };
      void fetchAdData();
   }, [isOpen, adgroup]);

   useEffect(() => {
      if (!isOpen) {
         modelstate.current = false;
         return;
      }
      !modelstate.current && setisKeywordSummaryLoading(true);
      const fetchSummary = async () => {
         if (
            isOpen &&
            adgroupKeywordData &&
            !modelstate.current &&
            filteredKeywordData.length > 0
         ) {
            modelstate.current = true;
            const payload = {
               client_id: userDetails?.client_id,
               timeframe: [start_date, end_date, String(days)],
               campaign_type: objective,
               campaign_name: data.campaign_name,
               campaign_id: data.campaign_id,
               keyword: filteredKeywordData,
            };

            const response =
               await pulseService.fetchAdgroupKeywordSummary(payload);
            if (response) {
               setKeywordSummary(response.data);
               setisKeywordSummaryLoading(false);
            }
         }
      };

      void fetchSummary();
   }, [isOpen, adgroupKeywordDataSuccess, filteredKeywordData]);

   useEffect(() => {
      setisGraphSummaryLoading(true);
      const fetchSummary = async () => {
         if (isOpen && adgroup) {
            const payload = {
               client_id: userDetails?.client_id,
               timeframe: [start_date, end_date, String(days)],
               campaign_type: objective,
               campaign_name: data.campaign_name,
               campaign_id: data.campaign_id,
               metric: selectedMetric,
               ad_group_data: adgroup,
               adgroup_ChartType: adgroup_ChartType,
            };

            const response =
               await pulseService.fetchAdgroupGraphSummary(payload);
            if (response) {
               setGraphSummary(response.data);
               setisGraphSummaryLoading(false);
            }
         }
      };

      void fetchSummary();
   }, [isOpen, selectedMetric, adgroup_ChartType]);

   useEffect(() => {
      setisAdSummaryLoading(true);
      const fetchSummary = async () => {
         if (isOpen && adData?.length > 0) {
            const payload = {
               client_id: userDetails?.client_id,
               timeframe: [start_date, end_date, String(days)],
               campaign_type: objective,
               campaign_name: data.campaign_name,
               campaign_id: data.campaign_id,
               ads: adData,
            };

            const response = await pulseService.fetchAdgroupSummary(payload);
            if (response) {
               setAdsSummary(response.data);
               setisAdSummaryLoading(false);
            }
         }
      };
      void fetchSummary();
   }, [isOpen, adData]);

   const sortedAdgroupKpis = adgroup?.kpis
      ?.slice()
      ?.sort((a, b) => a.kpi?.localeCompare(b.kpi));

   const sortedAdData = adData?.slice()?.map((ad) => ({
      ...ad,
      kpis: ad?.kpis?.slice()?.sort((a, b) => a.kpi?.localeCompare(b.kpi)),
   }));

   const handleMetricChange = (newMetric: string) => {
      setmetric(newMetric);
   };

   const toggleKeywordExpandedState = (key: string | number) => {
      setkeywordExpandedState((prevState) => ({
         [key]: !prevState[key],
      }));
   };

   const handleSort = (kpi: string) => {
      const newSortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
      setSortOrder(newSortOrder);
      setSelectedKpi(kpi);
   };

   useEffect(() => {
      if (adgroupKeywordData && !adgroupKeywordDataLoading) {
         const keywordsList: GoogleAdsKeywordsData[] = [];

         Object.keys(adgroupKeywordData)
            .filter((matchType) => selectedOptions.includes(matchType))
            .forEach((matchType) => {
               const keywords =
                  adgroupKeywordData[
                     matchType as keyof typeof adgroupKeywordData
                  ];
               for (const [keyword, val] of Object.entries(keywords)) {
                  const valueWithMatchType: GoogleAdsKeywordTermData & {
                     keywordMatchType: string;
                  } = {
                     ...(val as GoogleAdsKeywordTermData),
                     keywordMatchType: matchType,
                  };
                  keywordsList.push({
                     [keyword]: valueWithMatchType,
                  });
               }
            });
         if (keywordsList.length >= 1) {
            const sortedFilteredKeywordData = sortData(
               keywordsList,
               selectedKpi,
               sortOrder,
            );
            setFilteredKeywordData(sortedFilteredKeywordData);
         } else {
            setFilteredKeywordData(keywordsList);
         }
      }
   }, [
      adgroupKeywordDataLoading,
      adgroupKeywordDataSuccess,
      selectedOptions,
      selectedKpi,
      sortOrder,
   ]);

   const sortData = (
      keywordList: GoogleAdsKeywordsData[],
      kpi: string,
      order: string,
   ) => {
      const sortedData = keywordList.sort((a, b) => {
         const aValue = Object.values(a)[0].kpi?.keyword?.[kpi];
         const bValue = Object.values(b)[0].kpi?.keyword?.[kpi];

         const aIsBad = aValue == null;
         const bIsBad = bValue == null;

         if (aIsBad && !bIsBad) return 1;
         if (!aIsBad && bIsBad) return -1;
         if (aIsBad && bIsBad) return 0;

         if (order === 'asc') {
            return aValue! - bValue!;
         } else {
            return bValue! - aValue!;
         }
      });
      return sortedData;
   };
   const handleSelect = (value: string) => {
      if (!selectedOptions.includes(value)) {
         setSelectedOptions([...selectedOptions, value]);
      }
   };

   const handleRemove = (value: string) => {
      setSelectedOptions(selectedOptions.filter((v) => v !== value));
   };

   const handleClearAll = () => {
      setSelectedOptions([]);
   };

   function sortSearchTermKpiData(
      data: { [key: string]: KeywordKPIValues },
      kpi: string,
      order: string,
   ) {
      const sortedEntries = Object.entries(data).sort(([, aVal], [, bVal]) => {
         const aValue = aVal[kpi];
         const bValue = bVal[kpi];

         const aIsBad = aValue == null;
         const bIsBad = bValue == null;

         if (aIsBad && !bIsBad) return 1;
         if (!aIsBad && bIsBad) return -1;
         if (aIsBad && bIsBad) return 0;

         return order === 'asc' ? aValue! - bValue! : bValue! - aValue!;
      });

      return Object.fromEntries(sortedEntries);
   }

   return (
      <div className='popup-overlay'>
         <div className='popup-content'>
            <div className='heading'>
               <h3>
                  <button onClick={onClose}>{'<'}</button>{' '}
                  {adgroup?.ad_group_name}
               </h3>
               <button className='close-button' onClick={onBack}>
                  x
               </button>
            </div>
            <hr className='divider' />
            <div className=' top'>
               <button>{cap(channel)}</button>
               <button>{days} Days</button>
            </div>
            <div className='keyword-section'>
               <div className='keywords-header'>
                  <h3 className='heading-bs'>Keywords Performance</h3>
               </div>
               <div className='keyword-overview'>
                  <div className='keyword-content'>
                     <table className='keyword-table'>
                        <thead className='table-head'>
                           <tr className='head-row'>
                              <th className='head-col heading-bs'>Keyword</th>
                              <th className='head-col option-section'>
                                 <InputGroup
                                    className='inputfield'
                                    border='1px solid #ccc'
                                    borderRadius='xl'
                                    px={2}
                                    py={2}
                                 >
                                    <HStack
                                       wrap='wrap'
                                       spacing={2}
                                       className='selctedoptions'
                                    >
                                       {selectedOptions.map((value) => (
                                          <Tag
                                             key={value}
                                             borderRadius='full'
                                             className='matchnamesbox'
                                          >
                                             <TagLabel className='matchnames'>
                                                {value}
                                             </TagLabel>
                                             <TagCloseButton
                                                onClick={() =>
                                                   handleRemove(value)
                                                }
                                             />
                                          </Tag>
                                       ))}
                                    </HStack>

                                    <InputRightElement
                                       width='4.5rem'
                                       className='buttons'
                                    >
                                       <HStack spacing={1}>
                                          <IconButton
                                             className='close-buttion'
                                             size='sm'
                                             variant='ghost'
                                             icon={<IoCloseSharp size={20} />}
                                             aria-label='Clear All'
                                             onClick={handleClearAll}
                                          />
                                          <Menu>
                                             <MenuButton
                                                as={IconButton}
                                                icon={
                                                   <IoChevronDown size={20} />
                                                }
                                                className='drop-down-icon'
                                             />
                                             <MenuList className='dropdown'>
                                                <VStack align='start' px={2}>
                                                   {allOptions.map((opt) => (
                                                      <MenuItem
                                                         key={opt}
                                                         onClick={() =>
                                                            handleSelect(opt)
                                                         }
                                                         isDisabled={selectedOptions.includes(
                                                            opt,
                                                         )}
                                                      >
                                                         {opt}
                                                      </MenuItem>
                                                   ))}
                                                </VStack>
                                             </MenuList>
                                          </Menu>
                                       </HStack>
                                    </InputRightElement>
                                 </InputGroup>
                              </th>
                              {sortedAdgroupKpis
                                 ?.filter(
                                    (kpi) =>
                                       ![
                                          'impressions',
                                          'cpm',
                                          'conversions_value',
                                          'conversion_rate',
                                          'video_views',
                                          'interactions',
                                          'clicks',
                                       ].includes(kpi.kpi),
                                 )
                                 .map((kpi, index) => (
                                    <th
                                       key={index}
                                       className='head-col kpi-name'
                                       onClick={() => handleSort(kpi.kpi)}
                                    >
                                       <p className='kpiname'>
                                          <span>{toUpperCase(kpi.kpi)}</span>
                                          <span className='arrow-icon'>
                                             {selectedKpi === kpi.kpi ? (
                                                sortOrder === 'asc' ? (
                                                   <LuArrowUp />
                                                ) : (
                                                   <LuArrowDown />
                                                )
                                             ) : (
                                                <span className='filter-arrow'>
                                                   <LuArrowDownUp />
                                                </span>
                                             )}
                                          </span>
                                       </p>
                                    </th>
                                 ))}
                           </tr>
                        </thead>
                        <tbody className='table-body'>
                           {adgroupKeywordDataLoading ? (
                              <>
                                 {Array.from({ length: 6 }, (_, i) => {
                                    return (
                                       <tr className='body-row' key={i}>
                                          <td
                                             className='bodyRow-column'
                                             colSpan={2}
                                          >
                                             <SkeletonText
                                                p={6}
                                                noOfLines={1}
                                             />
                                          </td>
                                          {Array.from(
                                             {
                                                length: metricsOptions.length,
                                             },
                                             (_, j) => {
                                                return (
                                                   <td
                                                      className='bodyRow-column'
                                                      key={j}
                                                   >
                                                      <SkeletonText
                                                         p={6}
                                                         noOfLines={1}
                                                      />
                                                   </td>
                                                );
                                             },
                                          )}
                                       </tr>
                                    );
                                 })}
                              </>
                           ) : (
                              <>
                                 {filteredKeywordData.length > 0 ? (
                                    <>
                                       {filteredKeywordData.map(
                                          (keywordData, keywordIndex) => {
                                             const keywordName =
                                                Object.keys(keywordData);
                                             const keywordVal = Object.values(
                                                keywordData,
                                             )[0] as GoogleAdsSearchTermData;
                                             const keywordKpiVal =
                                                Object.values(keywordData)[0]
                                                   .kpi?.keyword;
                                             const keywordMatchType =
                                                keywordVal?.keywordMatchType as unknown as string;
                                             const keywordKpis =
                                                keywordKpiVal &&
                                                Object.keys(keywordKpiVal)
                                                   .filter(
                                                      (kpi) =>
                                                         ![
                                                            'impressions',
                                                            'conversions_value',
                                                            'conversion_rate',
                                                            'interactions',
                                                            'video_views',
                                                            'clicks',
                                                         ].includes(kpi),
                                                   )
                                                   .sort();
                                             return (
                                                <>
                                                   <tr
                                                      key={keywordIndex}
                                                      className='body-row'
                                                   >
                                                      <td
                                                         // colSpan={2}
                                                         className='body-col keyword-name'
                                                      >
                                                         <div className='keywords'>
                                                            <button
                                                               className='keywords-toggle'
                                                               onClick={() =>
                                                                  toggleKeywordExpandedState(
                                                                     `${keywordName[0]}${keywordMatchType}`,
                                                                  )
                                                               }
                                                            >
                                                               {keywordExpandedState[
                                                                  `${keywordName[0]}${keywordMatchType}`
                                                               ] ? (
                                                                  <FaChevronDown />
                                                               ) : (
                                                                  <FaChevronRight />
                                                               )}
                                                            </button>
                                                            <p className='keywords-name'>
                                                               {keywordName}
                                                            </p>
                                                         </div>
                                                      </td>
                                                      <td className='body-col keyword-terms'>
                                                         {keywordMatchType}
                                                      </td>
                                                      {keywordKpis &&
                                                         keywordKpis?.map(
                                                            (kpi, idx) => {
                                                               const currencyValue =
                                                                  toShowCurrency(
                                                                     kpi,
                                                                     data.currency,
                                                                  );
                                                               return (
                                                                  <td
                                                                     key={idx}
                                                                     className='body-col'
                                                                  >
                                                                     {`${keywordKpiVal[kpi] !== null && keywordKpiVal[kpi] !== undefined ? `${currencyValue || ''} ${formatValue(keywordKpiVal[kpi])}` : 'N/A'}`}
                                                                  </td>
                                                               );
                                                            },
                                                         )}
                                                   </tr>
                                                   {Object.keys(
                                                      keywordExpandedState,
                                                   )[0] ===
                                                      `${keywordName[0]}${keywordMatchType}` &&
                                                      keywordExpandedState[
                                                         `${keywordName[0]}${keywordMatchType}`
                                                      ] &&
                                                      keywordVal && (
                                                         <>
                                                            {Object.entries(
                                                               keywordVal,
                                                            ).map(
                                                               (
                                                                  [
                                                                     matchtype,
                                                                     matchtypedata,
                                                                  ],
                                                                  index,
                                                               ) => {
                                                                  if (
                                                                     matchtype ===
                                                                        'kpi' ||
                                                                     matchtype ===
                                                                        'keywordMatchType'
                                                                  )
                                                                     return null;
                                                                  matchtypedata =
                                                                     sortSearchTermKpiData(
                                                                        matchtypedata,
                                                                        selectedKpi,
                                                                        sortOrder,
                                                                     );
                                                                  return (
                                                                     <React.Fragment
                                                                        key={
                                                                           index
                                                                        }
                                                                     >
                                                                        {matchtypedata &&
                                                                           Object.entries(
                                                                              matchtypedata,
                                                                           ).map(
                                                                              (
                                                                                 [
                                                                                    searchTerm,
                                                                                    searchTermKPIVal,
                                                                                 ],
                                                                                 idx,
                                                                              ) => {
                                                                                 return (
                                                                                    <tr
                                                                                       className='body-row search-terms'
                                                                                       key={
                                                                                          idx
                                                                                       }
                                                                                    >
                                                                                       <td className='body-col search-term-name'>
                                                                                          {
                                                                                             searchTerm
                                                                                          }
                                                                                       </td>
                                                                                       <td className='body-col search-terms'>
                                                                                          {
                                                                                             matchtype
                                                                                          }
                                                                                       </td>
                                                                                       {keywordKpis &&
                                                                                          keywordKpis?.map(
                                                                                             (
                                                                                                kpi,
                                                                                                kpiIdx,
                                                                                             ) => {
                                                                                                const currencyValue =
                                                                                                   toShowCurrency(
                                                                                                      kpi,
                                                                                                      data.currency,
                                                                                                   );
                                                                                                const value =
                                                                                                   searchTermKPIVal[
                                                                                                      kpi
                                                                                                   ];
                                                                                                return (
                                                                                                   <td
                                                                                                      key={
                                                                                                         kpiIdx
                                                                                                      }
                                                                                                      className='body-col search-terms search-term-kpi'
                                                                                                   >
                                                                                                      {value !==
                                                                                                         null &&
                                                                                                      value !==
                                                                                                         undefined
                                                                                                         ? `${currencyValue || ''} ${formatValue(value)}`
                                                                                                         : 'N/A'}
                                                                                                   </td>
                                                                                                );
                                                                                             },
                                                                                          )}
                                                                                    </tr>
                                                                                 );
                                                                              },
                                                                           )}
                                                                     </React.Fragment>
                                                                  );
                                                               },
                                                            )}
                                                         </>
                                                      )}
                                                </>
                                             );
                                          },
                                       )}
                                    </>
                                 ) : (
                                    <tr>
                                       <td className='no-data-col' colSpan={7}>
                                          None of the keyword has any metric
                                          currently
                                       </td>
                                    </tr>
                                 )}
                              </>
                           )}
                        </tbody>
                     </table>
                  </div>
                  <Flex className='recommendations' direction={'column'}>
                     <h3>Recommendations for Keywords</h3>

                     {isKeywordSummaryLoading ? (
                        <SkeletonText mt={4} noOfLines={3} />
                     ) : filteredKeywordData.length === 0 ? (
                        <ul>No data available</ul>
                     ) : keywordSummary?.keyword?.Recommendation ? (
                        <ul className='recommendations-list'>
                           {extractLinesFromRecommendation(
                              keywordSummary?.keyword?.Recommendation,
                           )?.map((line, index) => <li key={index}>{line}</li>)}
                        </ul>
                     ) : (
                        <ul>No data available</ul>
                     )}
                  </Flex>
               </div>
            </div>

            <div className='performance-overview'>
               <h3 className='heading-bs'>Ad Group Performance</h3>
               <div className='performance-chart'>
                  <Flex direction={'column'} gap={2} className='details'>
                     <GoogleAdgroupsPerformenceMultiChart
                        chartMetricData={adgroup}
                        handleMetricChange={handleMetricChange}
                        selectedMetric={selectedMetric}
                        loading={isGraphSummaryLoading}
                     />
                     <Flex className='recommendations' direction={'column'}>
                        <h3>Recommendations for Ad Group</h3>
                        {isGraphSummaryLoading ? (
                           <>
                              <SkeletonText mt={4} noOfLines={3} />
                           </>
                        ) : graphSummary?.graph?.Recommendation ? (
                           <ul className='recommendations-list'>
                              {extractLinesFromRecommendation(
                                 graphSummary?.graph?.Recommendation,
                              )?.map((line, index) => (
                                 <li key={index}>{line}</li>
                              ))}
                           </ul>
                        ) : (
                           <ul>No data available</ul>
                        )}
                     </Flex>
                  </Flex>
               </div>
            </div>

            <div className='ad-set'>
               <h3>Ad Group Overview</h3>
               <div className='adset-overview'>
                  <table className='adset-table'>
                     <thead>
                        <tr>
                           {sortedAdgroupKpis
                              ?.filter(
                                 (kpi) =>
                                    ![
                                       'impressions',
                                       'cpm',
                                       'conversions_value',
                                       'video_views',
                                       'impressions',
                                       'interactions',
                                       'clicks',
                                       // 'spend',
                                    ].includes(kpi?.kpi),
                              )
                              ?.map((kpi, index) => (
                                 <th key={index}>
                                    {kpi?.kpi === 'conversion_rate'
                                       ? 'CVR'
                                       : toUpperCase(kpi?.kpi)}
                                 </th>
                              ))}
                        </tr>
                     </thead>
                     <tbody>
                        <tr>
                           {sortedAdgroupKpis
                              ?.filter(
                                 (kpi) =>
                                    ![
                                       'impressions',
                                       'cpm',
                                       'conversions_value',
                                       'video_views',
                                       'impressions',
                                       'interactions',
                                       'clicks',
                                       // 'spend',
                                    ].includes(kpi?.kpi),
                              )
                              ?.map((kpi, index) => {
                                 const {
                                    percentage,
                                    color,
                                    direction,
                                    currentValue,
                                 } = calculateHelper(
                                    kpi?.kpi,
                                    kpi?.kpi_current,
                                    kpi?.kpi_previous,
                                 );
                                 const arrow =
                                    direction === 'is up' ? '↑' : '↓';
                                 const currencyValue = toShowCurrency(
                                    kpi?.kpi,
                                    data?.currency,
                                 );
                                 return (
                                    <td key={index}>
                                       <div>
                                          {currencyValue} {currentValue}
                                       </div>
                                       <div>
                                          {percentage && direction && (
                                             <span style={{ color }}>
                                                {percentage}% {arrow}
                                             </span>
                                          )}
                                       </div>
                                    </td>
                                 );
                              })}
                        </tr>
                     </tbody>
                  </table>
               </div>
            </div>

            <div className='ad-set'>
               <h3>Ads Overview</h3>
               <div className='adset-overview'>
                  <table className='adset-table'>
                     <thead>
                        <tr>
                           <th>Ads</th>
                           <th>Headline</th>
                           {sortedAdData?.[0]?.kpis
                              ?.filter(
                                 (kpi) =>
                                    ![
                                       'cpm',
                                       'conversions_value',
                                       'video_views',
                                       'impressions',
                                       'interactions',
                                       'clicks',
                                       // 'spend',
                                    ].includes(kpi?.kpi),
                              )
                              ?.map((kpi, index) => (
                                 <th key={index}>
                                    {kpi?.kpi === 'conversion_rate'
                                       ? 'CVR'
                                       : toUpperCase(kpi?.kpi)}
                                 </th>
                              ))}
                        </tr>
                     </thead>
                     <tbody>
                        {adData?.length === 0 ? (
                           <>
                              <tr>
                                 {Array.from(
                                    { length: metricsOptions?.length + 2 },
                                    (_, i) => {
                                       return (
                                          <td key={i}>
                                             <SkeletonText noOfLines={1} />
                                          </td>
                                       );
                                    },
                                 )}
                              </tr>
                           </>
                        ) : (
                           sortedAdData?.map((ad, index) => (
                              <tr key={index}>
                                 <td className='ads'>
                                    {ad?.ad_name ?? 'Not Available'}
                                 </td>
                                 <td className='ads headline'>
                                    {ad?.headlines ? (
                                       <>
                                          {truncateText(
                                             ad.headlines.replace(
                                                /[[\]"']/g,
                                                '',
                                             ),
                                             keywordExpandedState[index],
                                             80,
                                          )}
                                          {ad.headlines.replace(/[[\]"']/g, '')
                                             .length > 80 && (
                                             <a
                                                href='#'
                                                className='view-more'
                                                onClick={(e) => {
                                                   e.preventDefault();
                                                   toggleKeywordExpandedState(
                                                      index,
                                                   );
                                                }}
                                             >
                                                {' '}
                                                {keywordExpandedState[index]
                                                   ? 'View Less'
                                                   : 'View More'}
                                             </a>
                                          )}
                                       </>
                                    ) : (
                                       'Not Available'
                                    )}
                                 </td>
                                 {sortedAdData?.[index]?.kpis
                                    ?.filter(
                                       (kpi) =>
                                          ![
                                             'cpm',
                                             'conversions_value',
                                             'video_views',
                                             'impressions',
                                             'interactions',
                                             'clicks',
                                             // 'spend',
                                          ].includes(kpi?.kpi),
                                    )
                                    ?.map((kpi, kpiIndex) => {
                                       const {
                                          percentage,
                                          color,
                                          direction,
                                          currentValue,
                                       } = calculateHelper(
                                          kpi?.kpi,
                                          kpi?.kpi_current,
                                          kpi?.kpi_previous,
                                       );
                                       const arrow =
                                          direction === 'is up' ? '↑' : '↓';
                                       const currencyValue = toShowCurrency(
                                          kpi?.kpi || '',
                                          data.currency,
                                       );
                                       return (
                                          <td key={kpiIndex}>
                                             <div>
                                                {currencyValue} {currentValue}
                                             </div>
                                             <div>
                                                {percentage && direction && (
                                                   <span style={{ color }}>
                                                      {percentage}% {arrow}
                                                   </span>
                                                )}
                                             </div>
                                          </td>
                                       );
                                    })}
                              </tr>
                           ))
                        )}
                     </tbody>
                  </table>
                  <Flex className='recommendations' direction={'column'}>
                     <h4 className='recommendations-title'>
                        Recommendations for Ads
                     </h4>
                     {isAdSummaryLoading ? (
                        <>
                           <SkeletonText mt={4} noOfLines={3} />
                        </>
                     ) : adsSummary?.ads?.Recommendation ? (
                        <ul className='recommendations-list'>
                           {extractLinesFromRecommendation(
                              adsSummary?.ads?.Recommendation,
                           )?.map((line, index) => <li key={index}>{line}</li>)}
                        </ul>
                     ) : (
                        <ul>No data available</ul>
                     )}
                  </Flex>
               </div>
            </div>
         </div>
      </div>
   );
};

export default GoogleAdgroupPopup;
