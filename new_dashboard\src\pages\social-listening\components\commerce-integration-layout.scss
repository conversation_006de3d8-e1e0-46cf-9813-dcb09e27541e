.commerce-integration {
   display: flex;

   &__left {
      flex: 1;
      border-right: 0.5px solid #a0a4a8;
      min-height: 90vh;
      padding: 20px;
   }

   &__right {
      flex: 1;
      display: flex;
      flex-direction: column;

      &-logo {
         border-bottom: 0.5px solid #a4a0a8;
         padding: 20px 15px;

         img {
            width: 90px;
            height: 65px;
         }
      }

      &-content {
         padding: 0 20px;
         flex: 1;
         margin-top: 20px;

         .api-response {
            padding: 6px 10px;
            color: white;
            font-weight: bold;
            margin-bottom: 15px;

            &__message {
               font-size: 18px;
            }

            &__success {
               border: 1px solid green;
               background-color: rgb(133, 231, 133);
            }
            &__error {
               background-color: rgb(234, 147, 147);
               border: 1px solid red;
            }
         }
      }
   }

   &__button {
      color: #337cdf;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      border-radius: 5px;
      border: 1px solid #337cdf;
      background: #fff;
      padding: 6px 10px;
      margin-top: 10px;
   }
}
