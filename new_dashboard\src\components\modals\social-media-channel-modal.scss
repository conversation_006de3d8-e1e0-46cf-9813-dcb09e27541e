// social-media-channel-modal.scss
.channel-description {
   font-size: 14px;
   padding-bottom: 14px;
}
.options {
   margin-bottom: 20px;
   display: flex;
   gap: 10px;
   .option-label {
      display: flex;
      margin-bottom: 10px;
      input[type='checkbox'] {
         margin-right: 5px;
         vertical-align: middle;
      }
   }
}

.generate-btn {
   padding: 10px 20px;
   background-color: #437eeb;
   color: white;
   border: none;
   cursor: pointer;
   width: 100%;

   &:hover {
      background-color: #2c5282;
   }
}
.spinner-container {
   display: flex;
   gap: 8px;
   padding: 10px 20px;
   background-color: #437eeb;
   color: white;
   border: none;
   cursor: pointer;
   width: 100%;
   justify-content: center;
}
