import { ApexOptions } from 'apexcharts';
import Chart from 'react-apexcharts';
import {
   DimensionsAggregated,
   GoogleAdgroupsData,
   KeywordDimension,
} from '../../../api/service/pulse';
import { Flex, useColorMode } from '@chakra-ui/react';
import { useAppSelector } from '../../../store/store';
import { useEffect, useState } from 'react';
import CustomdropDown from '../../../components/customDropdown/choose-dropdown';
import { toUpperCase, cap } from '../utils/helper';
import { setChartMetric } from '../../../store/reducer/overview-dropdown-reducer';
import { useAppDispatch } from '../../../store/store';
interface Props {
   chartMetricData: GoogleAdgroupsData;
   selectedMetric: string;
   handleMetricChange: (newMetric: string) => void;
   loading?: boolean;
}
function GoogleAdgroupsPerformenceMultiChart({
   chartMetricData,
   handleMetricChange,
   selectedMetric,
   loading,
}: Props) {
   const { metricsOptions, adgroup_ChartType } = useAppSelector(
      (state) => state.dropdown,
   );
   const [chartType, setchartype] = useState('column');
   const [chartData, setChartData] = useState<KeywordDimension[]>([]);
   const dispatch = useAppDispatch();

   const chartTypes = [
      { value: 'column', label: 'Bar' },
      { value: 'line', label: 'Line' },
   ];
   const chartMetrics = [
      { value: 'hour_wise', label: 'Hour' },
      { value: 'device_wise', label: 'Device' },
      { value: 'day_of_week_wise', label: 'Days' },
   ];

   const metrics = metricsOptions?.filter((x) =>
      Object.keys(
         chartMetricData?.[adgroup_ChartType as keyof typeof chartMetricData],
      )?.includes(x.value),
   );
   useEffect(() => {
      const selectedMetricData = chartMetricData?.[
         adgroup_ChartType as keyof GoogleAdgroupsData
      ] as DimensionsAggregated;
      if (selectedMetricData) {
         setChartData(selectedMetricData?.[selectedMetric]);
      }
   }, [adgroup_ChartType, selectedMetric, chartMetricData]);

   let state;

   if (chartMetricData && chartData) {
      state = {
         series: [
            {
               name: `Current ${toUpperCase(selectedMetric)}`,
               type: chartType,
               data: chartData.map((x) =>
                  x.current_value !== null
                     ? Number(x.current_value.toFixed(2))
                     : 0,
               ),
            },
            {
               name: `Previous ${toUpperCase(selectedMetric)}`,
               type: chartType,
               data: chartData.map((x) =>
                  x.previous_value !== null
                     ? Number(x.previous_value.toFixed(2))
                     : 0,
               ),
            },
         ],
         options: {
            chart: {
               type: 'line',
               background: useColorMode().colorMode === 'dark' ? '#1b202d' : '',
               toolbar: {
                  show: true,
                  offsetX: -30,
                  offsetY: -10,
                  tools: {
                     download: true,
                     selection: false,
                     zoom: false,
                     zoomin: false,
                     zoomout: false,
                     pan: false,
                  },
               },
               zoom: {
                  enabled: false,
               },
            },
            legend: {
               horizontalAlign: 'left',
               offsetX: 0,
               labels: {
                  colors:
                     useColorMode().colorMode === 'dark'
                        ? '#FFFFFF'
                        : '#000000',
               },
            },

            labels: chartData?.map((val) => val.keyword_name),
            tooltip: {
               y: {
                  formatter: function (
                     val: number,
                     {
                        seriesIndex,
                        dataPointIndex,
                     }: { seriesIndex: number; dataPointIndex: number },
                  ) {
                     if (seriesIndex === 0) {
                        return chartData[dataPointIndex]?.current_value === null
                           ? 'Data not available'
                           : val;
                     } else if (seriesIndex === 1) {
                        return chartData[dataPointIndex]?.previous_value ===
                           null
                           ? 'Data not available'
                           : val;
                     }
                     return val;
                  },
               },
            },
            xaxis: {
               title: {
                  text: `${chartMetrics?.find((item) => item?.value === adgroup_ChartType)?.label}`,
                  style: {
                     color:
                        useColorMode().colorMode === 'dark'
                           ? '#FFFFFF'
                           : '#000000',
                  },
               },
               labels: {
                  style: {
                     colors:
                        useColorMode().colorMode === 'dark'
                           ? '#FFFFFF'
                           : '#000000',
                  },
               },
               // labels: {
               //    style: {
               //       colors: chartData?.map((val) =>
               //          val.current_value === null &&
               //          val.previous_value === null
               //             ? 'red'
               //             : 'black',
               //       ),
               //    },
               // },
            },
            yaxis: [
               {
                  title: {
                     text: `Current ${toUpperCase(selectedMetric)} Aggregate`,
                  },
                  seriesName: 'Current_kpi',
               },
               {
                  opposite: true,
                  labels: {
                     style: {
                        colors:
                           useColorMode().colorMode === 'dark'
                              ? '#fff'
                              : '#000',
                     },
                  },
                  title: {
                     // text: `Previous ${toUpperCase(selectedMetric)} aggregatte`,
                     style: {
                        color:
                           useColorMode().colorMode === 'dark'
                              ? '#fff'
                              : '#000',
                     },
                     text: `Previous ${toUpperCase(selectedMetric)} Aggregate`,
                  },
                  seriesName: 'Previous_kpi',
               },
            ],
         },
      };
   }

   const handleMetric = (value: string) => {
      handleMetricChange(value);
   };
   const handleChartMetric = (value: string) => {
      dispatch(setChartMetric(value));
   };
   const handleType = (value: string) => {
      setchartype(value);
   };

   return (
      <>
         <div
            className='spend-budget'
            style={{ width: '100%', height: '500px' }}
         >
            <Flex className='selects' justifyContent={'flex-end'} gap={2}>
               {metrics && (
                  <CustomdropDown
                     options={metrics}
                     onSelect={handleMetric}
                     initialValue={selectedMetric}
                     width={125}
                     loading={loading}
                  />
               )}
               {chartMetrics && (
                  <CustomdropDown
                     options={chartMetrics}
                     onSelect={handleChartMetric}
                     initialValue={adgroup_ChartType}
                     width={125}
                     loading={loading}
                  />
               )}
               {chartTypes && (
                  <CustomdropDown
                     options={chartTypes}
                     onSelect={handleType}
                     initialValue={chartType}
                     width={114}
                  />
               )}
            </Flex>
            {!chartMetricData || !chartData ? (
               <div
                  style={{
                     marginTop: '75px',
                     textAlign: 'center',
                  }}
               >{`There is no data to show for ${cap(adgroup_ChartType)} on ${toUpperCase(selectedMetric)}`}</div>
            ) : (
               <>
                  <Chart
                     options={state?.options as ApexOptions}
                     series={state?.series}
                     type='line'
                     width='100%'
                     height='100%'
                  />
               </>
            )}
         </div>
      </>
   );
}

export default GoogleAdgroupsPerformenceMultiChart;
