import {
   <PERSON><PERSON>,
   PopoverTrigger,
   PopoverContent,
   PopoverBody,
   // PopoverFooter,
} from '@chakra-ui/react';
import './tourpop.scss';
import { componentNames, setFlag } from '../../store/reducer/tour-reducer';
import { RiQuestionLine } from 'react-icons/ri';

import { Button, PopoverCloseButton, Portal } from '@chakra-ui/react';

import { useDispatch } from 'react-redux';
import { useAppSelector } from '../../store/store';

const TourPopover = () => {
   const dispatch = useDispatch();
   const { step } = useAppSelector((state) => state.config);

   function startTour() {
      const url = window.location.href;
      const splittedUrl = url.split('/');
      const componentName = splittedUrl[splittedUrl.length - 1];
      if (componentName === 'marco') {
         dispatch(setFlag({ componentName: componentNames.MARCO, flag: true }));
      } else if (componentName === '') {
         dispatch(
            setFlag({ componentName: componentNames.DASHBOARD, flag: true }),
         );
      } else if (componentName === 'overview') {
         if (url.includes('performance-insights'))
            dispatch(
               setFlag({
                  componentName: componentNames.PERFORMANCE_INISGHT,
                  flag: true,
               }),
            );
         else
            dispatch(
               setFlag({
                  componentName: componentNames.WEB_INSIGHT,
                  flag: true,
               }),
            );
      } else if (componentName === 'tracked') {
         dispatch(
            setFlag({
               componentName: componentNames.TRACKED,
               flag: true,
            }),
         );
      } else if (componentName === 'integrations') {
         dispatch(
            setFlag({
               componentName: componentNames.INTEGRATION,
               flag: true,
            }),
         );
      } else if (componentName === 'settings') {
         dispatch(
            setFlag({
               componentName: componentNames.SETTINGS,
               flag: true,
            }),
         );
      } else if (componentName === 'optimisations') {
         dispatch(
            setFlag({
               componentName: componentNames.OPTIMISATIONS,
               flag: true,
            }),
         );
      } else if (componentName === 'socialwatch') {
         if (step === 1) {
            // content creation
            dispatch(
               setFlag({
                  componentName: componentNames.CONTENT_CREATION,
                  flag: true,
               }),
            );
            // content generation
         } else if (step === 2) {
            dispatch(
               setFlag({
                  componentName: componentNames.CONTENT_GENERATION,
                  flag: true,
               }),
            );
            // content ideation
         } else if (step === 3) {
            dispatch(
               setFlag({
                  componentName: componentNames.CONTENT_IDEATION,
                  flag: true,
               }),
            );
         }
      }
   }
   const getCurrentPageName = (): string => {
      const url = window.location.href;
      const splittedUrl = url.split('/');
      const componentName = splittedUrl[splittedUrl.length - 1] || 'dashboard';

      // Map the URL components to human-readable page names
      const pageNames: { [key: string]: string } = {
         marco: 'Marco',
         dashboard: 'Dashboard',
         overview: window.location.href.includes('performance-insights')
            ? 'Performance Insights'
            : 'Web Insight',
         tracked: 'Tracked Items',
         integrations: 'Integrations',
         socialwatch: 'Social Watch',
      };

      return pageNames[componentName] || 'Page';
   };

   // Get the current page name
   const currentPageName = getCurrentPageName();

   return (
      <div className='Tourpopup mx-1 mt-auto'>
         <Popover placement='bottom-end' closeOnBlur={true}>
            <PopoverTrigger>
               <Button className='infoBtn'>
                  <RiQuestionLine className='questionMark' />
                  <span className='tourHeading'>Take a tour</span>
               </Button>
            </PopoverTrigger>
            <Portal>
               <PopoverContent
                  width={'fit-content'}
                  className='date-select PopoverWrapper'
               >
                  <PopoverBody padding={0}>
                     <div className='TourPopoverContainer'>
                        <h1>Take a tour</h1>

                        <p>Take a quick tour to explore {currentPageName}!</p>

                        <div className='startTourBtn'>
                           <PopoverCloseButton
                              className='apply btn tourBtns'
                              onClick={startTour}
                           >
                              Start Tour
                           </PopoverCloseButton>
                        </div>
                     </div>
                  </PopoverBody>
               </PopoverContent>
            </Portal>
         </Popover>
      </div>
   );
};
export default TourPopover;
