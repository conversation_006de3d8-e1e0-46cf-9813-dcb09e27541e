import {
   Box,
   Text,
   Input,
   Button,
   VStack,
   FormControl,
   FormLabel,
   Link,
   useToast,
   HStack,
} from '@chakra-ui/react';
import { useEffect, useState, useRef } from 'react';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import metaAdsManagerEndPoints from '../../../api/service/agentic-workflow/meta-ads-manager';

const ApiToken = () => {
   const [metaToken, setMetaToken] = useState('');
   const [pageId, setPageId] = useState('');
   const [adAccountId, setAdAccountId] = useState('');

   const [savedMetaToken, setSavedMetaToken] = useState('');
   const [savedPageId, setSavedPageId] = useState('');
   const [savedAdAccountId, setSavedAdAccountId] = useState('');

   const [isLoading, setIsLoading] = useState(false);
   const [isFetching, setIsFetching] = useState(true);
   const [isEditing, setIsEditing] = useState(false);

   const toast = useToast();
   const inputRef = useRef<HTMLInputElement>(null);
   useEffect(() => {
      const fetchCredentials = async () => {
         try {
            const client_id = LocalStorageService.getItem(
               Keys.ClientId,
            ) as string;

            if (!client_id) return;

            const response = await metaAdsManagerEndPoints.fetchCredentials({
               client_id,
            });

            const data = response?.data?.data;

            if (
               data?.meta_access_token &&
               data?.page_id &&
               data?.ad_account_id
            ) {
               setSavedMetaToken(data.meta_access_token);
               setSavedPageId(data.page_id);
               setSavedAdAccountId(data.ad_account_id);

               // Also set form fields initially
               setMetaToken(data.meta_access_token);
               setPageId(data.page_id);
               setAdAccountId(data.ad_account_id);
            }
         } catch (error) {
            console.error('Error fetching credentials:', error);
         } finally {
            setIsFetching(false);
         }
      };

      void fetchCredentials();
   }, []);

   const handleSave = async () => {
      try {
         setIsLoading(true);
         const client_id =
            LocalStorageService.getItem<string>(Keys.ClientId) ?? '';

         if (!client_id || typeof client_id !== 'string') {
            throw new Error('Client ID is invalid or missing.');
         }

         const payload = {
            client_id,
            meta_access_token: metaToken,
            page_id: pageId,
            ad_account_id: adAccountId,
         };

         const response =
            await metaAdsManagerEndPoints.updateCredentials(payload);

         if (response.status === 200) {
            toast({
               title: 'Settings saved',
               description: 'Meta Ads Manager credentials saved successfully.',
               status: 'success',
               duration: 4000,
               isClosable: true,
               position: 'top',
            });

            // Save new credentials
            setSavedMetaToken(metaToken);
            setSavedPageId(pageId);
            setSavedAdAccountId(adAccountId);

            setIsEditing(false);
         }
      } catch (error) {
         console.error('Error saving Meta credentials:', error);
         toast({
            title: 'Error saving settings',
            description:
               error instanceof Error
                  ? error.message
                  : 'Failed to save Meta Ads Manager credentials. Please try again.',
            status: 'error',
            duration: 5000,
            isClosable: true,
            position: 'top',
         });
      } finally {
         setIsLoading(false);
      }
   };

   const handleEdit = () => {
      setMetaToken(savedMetaToken);
      setPageId(savedPageId);
      setAdAccountId(savedAdAccountId);
      setIsEditing(true);
   };

   setTimeout(() => {
      inputRef.current?.focus();
   }, 0);
   const handleCancel = () => {
      setMetaToken(savedMetaToken);
      setPageId(savedPageId);
      setAdAccountId(savedAdAccountId);
      setIsEditing(false);
   };

   const allFieldsFilled = metaToken && pageId && adAccountId;

   if (isFetching) {
      return (
         <Box p={6}>
            <Text>Loading...</Text>
         </Box>
      );
   }

   return (
      <Box p={6}>
         <Text fontSize='xl' fontWeight='bold' mb={6}>
            API Token Management
         </Text>

         <VStack spacing={6} align='stretch'>
            {!isEditing && savedMetaToken && savedPageId && savedAdAccountId ? (
               <Box
                  p={4}
                  border='1px solid #e2e8f0'
                  borderRadius='md'
                  mt={1}
                  width='100%'
                  bg='green.50'
               >
                  <Text fontWeight='semibold' color='green.500'>
                     Meta credentials are already saved.
                  </Text>
                  <Text mt={2} fontSize='sm' color='gray.600'>
                     You can edit your Meta Access Token, Page ID or Ad Account
                     ID if needed.
                  </Text>
                  <Button
                     bg='#ffe5e5'
                     color='#cc0000'
                     border='1px solid #cc0000'
                     _hover={{ bg: '#ffcccc' }}
                     mt={4}
                     ml={1}
                     onClick={handleEdit}
                  >
                     Edit Credentials
                  </Button>
               </Box>
            ) : (
               <FormControl>
                  {!isEditing && !metaToken && !pageId && !adAccountId && (
                     <Text mb={4} fontWeight={'300'} color={'red'}>
                        Please provide Meta credenitals to start creating
                        Campaign
                     </Text>
                  )}
                  <FormLabel>Meta Ads Manager Token</FormLabel>

                  <Text fontSize='sm' color='gray.600' mb={2}>
                     To generate your Meta Ads Manager access token, follow our{' '}
                     <Link
                        href='https://digital-expanse.com/tutorials/facebook-marketing-api-access-token/'
                        color='blue.500'
                        isExternal
                     >
                        step-by-step guide
                     </Link>
                  </Text>
                  <Text fontSize='sm' color='gray.600' mb={4}>
                     Note: This is a long-lived access token that will expire
                     after ~60 days.
                  </Text>
                  <Input
                     placeholder='Enter Meta Access Token'
                     value={metaToken}
                     onChange={(e) => setMetaToken(e.target.value)}
                     type='password'
                     mb={4}
                  />

                  <FormLabel mt={4}>Meta Page ID</FormLabel>
                  <Text fontSize='sm' color='gray.600' mb={2}>
                     To find your Facebook Page ID:{' '}
                     <Link
                        href='https://www.facebook.com/pages/?category=your_pages'
                        color='blue.500'
                        isExternal
                     >
                        Go to your Facebook Pages
                     </Link>
                  </Text>
                  <Text fontSize='sm' color='gray.600' mb={4}>
                     Required for ad management on your page.
                  </Text>
                  <Input
                     placeholder='Enter Page ID'
                     value={pageId}
                     onChange={(e) => setPageId(e.target.value)}
                     mb={4}
                  />

                  <FormLabel mt={4}>Meta Ad Account ID</FormLabel>
                  <Text fontSize='sm' color='gray.600' mb={2}>
                     To find your Facebook Ad Account ID:{' '}
                     <Link
                        href='https://www.facebook.com/ads/manager/account/?id=**********'
                        color='blue.500'
                        isExternal
                     >
                        Go to your Facebook Ad Account
                     </Link>
                  </Text>
                  <Text fontSize='sm' color='gray.600' mb={4}>
                     Required for managing Facebook ads.
                  </Text>
                  <Input
                     placeholder='Enter Ad Account ID'
                     value={adAccountId}
                     onChange={(e) => setAdAccountId(e.target.value)}
                     mb={6}
                  />

                  <HStack spacing={4}>
                     <Button
                        colorScheme='blue'
                        onClick={() => void handleSave()}
                        isLoading={isLoading}
                        isDisabled={!allFieldsFilled}
                     >
                        Save Meta Settings
                     </Button>
                     <Button
                        bg='#ffe5e5'
                        color='#cc0000'
                        border='1px solid #cc0000'
                        _hover={{ bg: '#ffcccc' }}
                        mt={2}
                        ml={6}
                        onClick={handleCancel}
                     >
                        Cancel
                     </Button>
                  </HStack>
               </FormControl>
            )}
         </VStack>
      </Box>
   );
};

export default ApiToken;
