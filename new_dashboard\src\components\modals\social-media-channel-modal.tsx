import ModalWrapper from './modal-wrapper';
import { useState } from 'react';
import { useAppSelector } from '../../store/store';
import socailWatchEndpoints from '../../api/service/social-watch/apis';
import { useDispatch } from 'react-redux';
import { Keys, LocalStorageService } from '../../utils/local-storage';
import { useToast, Spinner } from '@chakra-ui/react';
import { setAiText, setLoading } from '../../store/reducer/configReducer';
import { closeModal } from '../../store/reducer/modal-reducer';
import { AuthUser } from '../../types/auth';
import './social-media-channel-modal.scss';
import {
   setRawFiles,
   setMultiMedia,
   setSelectedTab,
} from '../../store/reducer/user-details-reducer';
import socialWatchEndpoints from '../../api/service/social-watch/apis';

function SocialMediaChannelModal() {
   const currentModal = useAppSelector((state) => state.modal);
   const options: string[] =
      (currentModal.payload?.modalProps?.options as string[]) || [];
   const generateText: string =
      (currentModal.payload?.modalProps?.text as string) || '';
   const handleBackToWrapper = currentModal.payload?.modalProps
      ?.handleBackToWrapper as () => void;
   const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
   const [loader, setLoader] = useState<boolean>(false);
   const dispatch = useDispatch();
   const toast = useToast();
   const {
      connections: { linkedin, twitter },
   } = useAppSelector((state) => state.integration);

   const socialMediaData = useAppSelector(
      (state) => state.media.uploadToSocialMedia,
   );

   const selectedPill = useAppSelector(
      (state) => state.contentIdeation.clickedPillId,
   );

   const userDetails = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   );

   const handleCheckboxChange = (option: string) => {
      setSelectedOptions((prevSelected) =>
         prevSelected.includes(option)
            ? prevSelected.filter((o) => o !== option)
            : [...prevSelected, option],
      );
   };
   const handleGenerateWrapper = () => {
      handleGenerate().catch(console.error);
   };

   const handleGenerate = async () => {
      setLoader(true);
      try {
         const responses = await Promise.all(
            selectedOptions.map(async (option) => {
               const payload = {
                  client_id: userDetails!.client_id,
                  social_channel:
                     option.charAt(0).toUpperCase() + option.slice(1),
                  reference_text: generateText,
                  tone: 'Professional',
                  no_hashtags: '5',
                  word_size: 'Medium',
                  top_posts: '',
                  top_competitor_posts: '',
                  top_hashtags: '',
               };
               const aiSuggestion =
                  await socailWatchEndpoints.getAiSuggestion(payload);
               if (aiSuggestion && aiSuggestion.data) {
                  dispatch(
                     setAiText({
                        media: aiSuggestion.data.media,
                        text: aiSuggestion.data.captions[0],
                     }),
                  );
               }
               if (option === 'twitter') {
                  dispatch(setSelectedTab('twitter'));
                  if (selectedPill === 'Image Upload') {
                     await uploadToTwitter(
                        socialMediaData.file,
                        socialMediaData?.encodedUri,
                        socialMediaData?.decodedUri,
                     );
                  }
               } else if (option === 'linkedin') {
                  dispatch(setSelectedTab('linkedin'));
                  if (selectedPill === 'Image Upload') {
                     await uploadToLinkedin(
                        socialMediaData.file,
                        socialMediaData?.encodedUri,
                        socialMediaData?.decodedUri,
                     );
                  }
               }

               return aiSuggestion;
            }),
         );
         if (
            responses.every((response) => response && response.status === 200)
         ) {
            dispatch(closeModal());
            handleBackToWrapper();
            toast({
               title: 'Success',
               description: 'Content generated successfully!',
               status: 'success',
               duration: 5000,
               isClosable: true,
            });
         } else {
            toast({
               title: 'Error',
               description:
                  'Error occured while generating content please try again.',
               status: 'error',
               duration: 5000,
               isClosable: true,
            });
         }
      } catch (error) {
         dispatch(setLoading(false));
         toast({
            title: 'Error',
            description: 'An error occurred while generating content.',
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
         console.error(error);
      } finally {
         setLoader(false);
      }
   };
   const uploadToTwitter = async (
      file: File | null,
      uri: string,
      decodedUri: string,
   ) => {
      const formData = new FormData();
      if (!twitter) return;
      const { oauthToken, oauthTokenSecret } = twitter;
      if (file) {
         formData.append('media', file);
         formData.append('oauthToken', oauthToken);
         formData.append('oauthTokenSecret', oauthTokenSecret);
      }

      try {
         const { data } =
            await socialWatchEndpoints.uploadImageToTwitter(formData);
         if (data) {
            dispatch(
               setMultiMedia({
                  media_id: data.mediaId,
                  image_link: uri,
                  rawFile: decodedUri,
                  socialMedia: 'twitter',
               }),
            );
            dispatch(
               setRawFiles({ result: decodedUri, socialMedia: 'twitter' }),
            );
            console.log('image uplaod to twitter succesfuly');
         }
      } catch (error) {
         console.error('Error uploading image:', error);
      }
   };
   const uploadToLinkedin = async (
      file: File | null,
      uri: string,
      decodedUri: string,
   ) => {
      const formData = new FormData();
      if (!linkedin) return;
      const { user } = linkedin;
      if (file) {
         formData.append('media', file);
         formData.append('token', user.access_token);
         formData.append('userId', user.userId);
      }
      try {
         const { data } =
            await socialWatchEndpoints.uploadImageToLinkedin(formData);
         if (data) {
            dispatch(
               setMultiMedia({
                  media_id: data.assetId,
                  image_link: uri,
                  rawFile: decodedUri,
                  socialMedia: 'linkedin',
               }),
            );
            dispatch(
               setRawFiles({ result: decodedUri, socialMedia: 'linkedin' }),
            );
            console.log('image uplaod to linkedin succesfuly');
         }
      } catch (error) {
         console.error('Error uploading image:', error);
      }
   };
   return (
      <ModalWrapper
         heading='Choose social media channel'
         overlayBgcolor='#2424241c'
      >
         <div className='channel-description'>
            For which social media accounts you want to generate content
         </div>
         <div className='options'>
            {options.map((option: string) => (
               <label key={option} className='option-label'>
                  <input
                     type='checkbox'
                     checked={selectedOptions.includes(option)}
                     onChange={() => handleCheckboxChange(option)}
                  />
                  {option}
               </label>
            ))}
         </div>
         {loader ? (
            <div className='spinner-container'>
               <Spinner size='md' />
               <p>Generating Content</p>
            </div>
         ) : (
            <button className='generate-btn' onClick={handleGenerateWrapper}>
               Generate
            </button>
         )}
      </ModalWrapper>
   );
}
export default SocialMediaChannelModal;
